services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: optical_lines_db
    environment:
      POSTGRES_DB: optical_lines_database
      POSTGRES_USER: optical_user
      POSTGRES_PASSWORD: optical_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./database/sample-data.sql:/docker-entrypoint-initdb.d/02-sample-data.sql
    networks:
      - optical_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U optical_user -d optical_lines_database"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Java Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: optical_lines_backend
    environment:
      SPRING_DATASOURCE_URL: ******************************************************
      SPRING_DATASOURCE_USERNAME: optical_user
      SPRING_DATASOURCE_PASSWORD: optical_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_JPA_SHOW_SQL: true
    ports:
      - "8080:8080"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - optical_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Angular Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: optical_lines_frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - optical_network
    environment:
      API_BASE_URL: http://backend:8080

volumes:
  postgres_data:

networks:
  optical_network:
    driver: bridge
