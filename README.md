# Optical Lines Database

A comprehensive database system for managing optical fiber lines and connection points with interactive map visualization. The system provides a web interface for viewing optical infrastructure data and a provider dashboard for data management.

## Project Overview

This project consists of three main components:
- **Frontend**: Angular application with interactive maps and data tables
- **Backend**: Java Spring Boot REST API
- **Database**: PostgreSQL with sample data for Slovakia

## Features

- Interactive map display of optical lines and connection points
- Data tables for lines and connection points with filtering and search
- Provider login functionality for data management
- Sample data covering Slovakia with 20+ continuous optical lines
- Dockerized deployment for easy setup and scaling

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Angular)     │◄──►│  (Spring Boot)  │◄──►│  (PostgreSQL)   │
│   Port: 80      │    │   Port: 8080    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd optical_lines_database
   ```

2. **Start all services**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost
   - Backend API: http://localhost:8080
   - Database: localhost:5432

## Development Setup

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for frontend development)
- Java 17+ (for backend development)
- Maven 3.6+ (for backend development)

### Frontend Development
```bash
cd frontend
npm install
npm start
# Access at http://localhost:4200
```

### Backend Development
```bash
cd backend
mvn spring-boot:run
# Access at http://localhost:8080
```

### Database Setup
```bash
# Start PostgreSQL container
docker run -d --name postgres \
  -e POSTGRES_DB=optical_lines_database \
  -e POSTGRES_USER=optical_user \
  -e POSTGRES_PASSWORD=optical_password \
  -p 5432:5432 postgres:15-alpine
```

## Project Structure

```
optical_lines_database/
├── frontend/                 # Angular application
│   ├── src/
│   ├── Dockerfile
│   ├── nginx.conf
│   └── package.json
├── backend/                  # Spring Boot API
│   ├── src/main/java/
│   ├── src/main/resources/
│   ├── Dockerfile
│   └── pom.xml
├── database/                 # Database scripts
│   ├── init.sql
│   └── sample-data.sql
├── docker-compose.yml        # Multi-service orchestration
└── README.md
```

## API Endpoints

### Optical Lines
- `GET /api/optical-lines` - Get all optical lines
- `GET /api/optical-lines/{id}` - Get optical line by ID
- `GET /api/optical-lines/provider/{provider}` - Get lines by provider
- `POST /api/optical-lines` - Create new optical line
- `PUT /api/optical-lines/{id}` - Update optical line
- `DELETE /api/optical-lines/{id}` - Delete optical line

### Connection Points
- `GET /api/connection-points` - Get all connection points
- `GET /api/connection-points/{id}` - Get connection point by ID
- `GET /api/connection-points/optical-line/{lineId}` - Get points by line
- `POST /api/connection-points` - Create new connection point
- `PUT /api/connection-points/{id}` - Update connection point
- `DELETE /api/connection-points/{id}` - Delete connection point

## Sample Data

The database includes sample data for Slovakia with:
- 25+ optical lines covering major cities and regions
- Cross-border connections to neighboring countries
- Various providers (Slovak Telekom, Orange Slovakia, SWAN)
- Connection points along major routes
- Different line types (backbone, regional, backup)

## Technology Stack

### Frontend
- **Framework**: Angular 19
- **UI Library**: Angular Material
- **Maps**: Leaflet
- **HTTP Client**: Angular HttpClient
- **Build Tool**: Angular CLI

### Backend
- **Framework**: Spring Boot 3.2
- **Database**: PostgreSQL 15
- **ORM**: Spring Data JPA
- **Build Tool**: Maven
- **Java Version**: 17

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Web Server**: Nginx (for frontend)
- **Database**: PostgreSQL with persistent volumes

## Environment Variables

### Backend
- `SPRING_DATASOURCE_URL` - Database connection URL
- `SPRING_DATASOURCE_USERNAME` - Database username
- `SPRING_DATASOURCE_PASSWORD` - Database password

### Frontend
- `API_BASE_URL` - Backend API base URL

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
