# Database Configuration
spring.datasource.url=*******************************************************
spring.datasource.username=optical_user
spring.datasource.password=optical_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# Logging Configuration
logging.level.com.opticallines.backend=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Application Information
spring.application.name=optical-lines-backend
info.app.name=Optical Lines Database Backend
info.app.description=Backend API for managing optical lines and connection points
info.app.version=1.0.0
