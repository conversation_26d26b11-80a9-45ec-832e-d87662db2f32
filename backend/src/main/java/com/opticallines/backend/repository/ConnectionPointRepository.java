package com.opticallines.backend.repository;

import com.opticallines.backend.entity.ConnectionPoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConnectionPointRepository extends JpaRepository<ConnectionPoint, Long> {
    
    List<ConnectionPoint> findByOpticalLineId(Long opticalLineId);
    
    List<ConnectionPoint> findByPointType(ConnectionPoint.PointType pointType);
    
    List<ConnectionPoint> findByStatus(ConnectionPoint.PointStatus status);
    
    List<ConnectionPoint> findByCity(String city);
    
    @Query("SELECT cp FROM ConnectionPoint cp WHERE cp.name LIKE %:name%")
    List<ConnectionPoint> findByNameContaining(@Param("name") String name);
    
    @Query("SELECT cp FROM ConnectionPoint cp WHERE " +
           "cp.latitude BETWEEN :minLat AND :maxLat AND " +
           "cp.longitude BETWEEN :minLon AND :maxLon")
    List<ConnectionPoint> findByLocationBounds(@Param("minLat") Double minLat, 
                                              @Param("maxLat") Double maxLat,
                                              @Param("minLon") Double minLon, 
                                              @Param("maxLon") Double maxLon);
}
