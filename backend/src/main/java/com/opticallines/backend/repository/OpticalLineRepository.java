package com.opticallines.backend.repository;

import com.opticallines.backend.entity.OpticalLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OpticalLineRepository extends JpaRepository<OpticalLine, Long> {
    
    List<OpticalLine> findByProvider(String provider);
    
    List<OpticalLine> findByStatus(OpticalLine.LineStatus status);
    
    @Query("SELECT ol FROM OpticalLine ol WHERE ol.name LIKE %:name%")
    List<OpticalLine> findByNameContaining(@Param("name") String name);
    
    @Query("SELECT ol FROM OpticalLine ol WHERE " +
           "ol.latitudeStart BETWEEN :minLat AND :maxLat AND " +
           "ol.longitudeStart BETWEEN :minLon AND :maxLon")
    List<OpticalLine> findByLocationBounds(@Param("minLat") Double minLat, 
                                          @Param("maxLat") Double maxLat,
                                          @Param("minLon") Double minLon, 
                                          @Param("maxLon") Double maxLon);
}
