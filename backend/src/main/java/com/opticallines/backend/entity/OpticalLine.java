package com.opticallines.backend.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "optical_lines")
public class OpticalLine {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @NotBlank
    @Column(name = "provider", nullable = false)
    private String provider;
    
    @NotNull
    @Column(name = "latitude_start", nullable = false)
    private Double latitudeStart;
    
    @NotNull
    @Column(name = "longitude_start", nullable = false)
    private Double longitudeStart;
    
    @NotNull
    @Column(name = "latitude_end", nullable = false)
    private Double latitudeEnd;
    
    @NotNull
    @Column(name = "longitude_end", nullable = false)
    private Double longitudeEnd;
    
    @Column(name = "length_km")
    private Double lengthKm;
    
    @Column(name = "fiber_count")
    private Integer fiberCount;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private LineStatus status = LineStatus.ACTIVE;
    
    @Column(name = "installation_date")
    private LocalDateTime installationDate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "opticalLine", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ConnectionPoint> connectionPoints;
    
    // Constructors
    public OpticalLine() {}
    
    public OpticalLine(String name, String provider, Double latitudeStart, 
                      Double longitudeStart, Double latitudeEnd, Double longitudeEnd) {
        this.name = name;
        this.provider = provider;
        this.latitudeStart = latitudeStart;
        this.longitudeStart = longitudeStart;
        this.latitudeEnd = latitudeEnd;
        this.longitudeEnd = longitudeEnd;
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getProvider() { return provider; }
    public void setProvider(String provider) { this.provider = provider; }
    
    public Double getLatitudeStart() { return latitudeStart; }
    public void setLatitudeStart(Double latitudeStart) { this.latitudeStart = latitudeStart; }
    
    public Double getLongitudeStart() { return longitudeStart; }
    public void setLongitudeStart(Double longitudeStart) { this.longitudeStart = longitudeStart; }
    
    public Double getLatitudeEnd() { return latitudeEnd; }
    public void setLatitudeEnd(Double latitudeEnd) { this.latitudeEnd = latitudeEnd; }
    
    public Double getLongitudeEnd() { return longitudeEnd; }
    public void setLongitudeEnd(Double longitudeEnd) { this.longitudeEnd = longitudeEnd; }
    
    public Double getLengthKm() { return lengthKm; }
    public void setLengthKm(Double lengthKm) { this.lengthKm = lengthKm; }
    
    public Integer getFiberCount() { return fiberCount; }
    public void setFiberCount(Integer fiberCount) { this.fiberCount = fiberCount; }
    
    public LineStatus getStatus() { return status; }
    public void setStatus(LineStatus status) { this.status = status; }
    
    public LocalDateTime getInstallationDate() { return installationDate; }
    public void setInstallationDate(LocalDateTime installationDate) { this.installationDate = installationDate; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public List<ConnectionPoint> getConnectionPoints() { return connectionPoints; }
    public void setConnectionPoints(List<ConnectionPoint> connectionPoints) { this.connectionPoints = connectionPoints; }
    
    public enum LineStatus {
        ACTIVE, INACTIVE, MAINTENANCE, PLANNED
    }
}
