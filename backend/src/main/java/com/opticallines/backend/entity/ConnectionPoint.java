package com.opticallines.backend.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "connection_points")
public class ConnectionPoint {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @NotNull
    @Column(name = "latitude", nullable = false)
    private Double latitude;
    
    @NotNull
    @Column(name = "longitude", nullable = false)
    private Double longitude;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "point_type")
    private PointType pointType;
    
    @Column(name = "address")
    private String address;
    
    @Column(name = "city")
    private String city;
    
    @Column(name = "postal_code")
    private String postalCode;
    
    @Column(name = "country")
    private String country = "Slovakia";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PointStatus status = PointStatus.ACTIVE;
    
    @Column(name = "capacity")
    private Integer capacity;
    
    @Column(name = "used_capacity")
    private Integer usedCapacity = 0;
    
    @Column(name = "installation_date")
    private LocalDateTime installationDate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "optical_line_id")
    private OpticalLine opticalLine;
    
    // Constructors
    public ConnectionPoint() {}
    
    public ConnectionPoint(String name, Double latitude, Double longitude, PointType pointType) {
        this.name = name;
        this.latitude = latitude;
        this.longitude = longitude;
        this.pointType = pointType;
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }
    
    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }
    
    public PointType getPointType() { return pointType; }
    public void setPointType(PointType pointType) { this.pointType = pointType; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    
    public String getPostalCode() { return postalCode; }
    public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public PointStatus getStatus() { return status; }
    public void setStatus(PointStatus status) { this.status = status; }
    
    public Integer getCapacity() { return capacity; }
    public void setCapacity(Integer capacity) { this.capacity = capacity; }
    
    public Integer getUsedCapacity() { return usedCapacity; }
    public void setUsedCapacity(Integer usedCapacity) { this.usedCapacity = usedCapacity; }
    
    public LocalDateTime getInstallationDate() { return installationDate; }
    public void setInstallationDate(LocalDateTime installationDate) { this.installationDate = installationDate; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public OpticalLine getOpticalLine() { return opticalLine; }
    public void setOpticalLine(OpticalLine opticalLine) { this.opticalLine = opticalLine; }
    
    public enum PointType {
        JUNCTION_BOX, DISTRIBUTION_POINT, TERMINAL, SPLICE_CLOSURE, MANHOLE, BUILDING_ENTRY
    }
    
    public enum PointStatus {
        ACTIVE, INACTIVE, MAINTENANCE, PLANNED
    }
}
