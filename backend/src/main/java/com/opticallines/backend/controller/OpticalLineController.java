package com.opticallines.backend.controller;

import com.opticallines.backend.entity.OpticalLine;
import com.opticallines.backend.repository.OpticalLineRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/optical-lines")
@CrossOrigin(origins = "*")
public class OpticalLineController {
    
    @Autowired
    private OpticalLineRepository opticalLineRepository;
    
    @GetMapping
    public List<OpticalLine> getAllOpticalLines() {
        return opticalLineRepository.findAll();
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<OpticalLine> getOpticalLineById(@PathVariable Long id) {
        Optional<OpticalLine> opticalLine = opticalLineRepository.findById(id);
        return opticalLine.map(ResponseEntity::ok)
                         .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/provider/{provider}")
    public List<OpticalLine> getOpticalLinesByProvider(@PathVariable String provider) {
        return opticalLineRepository.findByProvider(provider);
    }
    
    @GetMapping("/status/{status}")
    public List<OpticalLine> getOpticalLinesByStatus(@PathVariable OpticalLine.LineStatus status) {
        return opticalLineRepository.findByStatus(status);
    }
    
    @GetMapping("/search")
    public List<OpticalLine> searchOpticalLines(@RequestParam String name) {
        return opticalLineRepository.findByNameContaining(name);
    }
    
    @GetMapping("/bounds")
    public List<OpticalLine> getOpticalLinesInBounds(
            @RequestParam Double minLat,
            @RequestParam Double maxLat,
            @RequestParam Double minLon,
            @RequestParam Double maxLon) {
        return opticalLineRepository.findByLocationBounds(minLat, maxLat, minLon, maxLon);
    }
    
    @PostMapping
    public OpticalLine createOpticalLine(@Valid @RequestBody OpticalLine opticalLine) {
        return opticalLineRepository.save(opticalLine);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<OpticalLine> updateOpticalLine(@PathVariable Long id, 
                                                        @Valid @RequestBody OpticalLine opticalLineDetails) {
        Optional<OpticalLine> optionalOpticalLine = opticalLineRepository.findById(id);
        
        if (optionalOpticalLine.isPresent()) {
            OpticalLine opticalLine = optionalOpticalLine.get();
            opticalLine.setName(opticalLineDetails.getName());
            opticalLine.setDescription(opticalLineDetails.getDescription());
            opticalLine.setProvider(opticalLineDetails.getProvider());
            opticalLine.setLatitudeStart(opticalLineDetails.getLatitudeStart());
            opticalLine.setLongitudeStart(opticalLineDetails.getLongitudeStart());
            opticalLine.setLatitudeEnd(opticalLineDetails.getLatitudeEnd());
            opticalLine.setLongitudeEnd(opticalLineDetails.getLongitudeEnd());
            opticalLine.setLengthKm(opticalLineDetails.getLengthKm());
            opticalLine.setFiberCount(opticalLineDetails.getFiberCount());
            opticalLine.setStatus(opticalLineDetails.getStatus());
            opticalLine.setInstallationDate(opticalLineDetails.getInstallationDate());
            
            return ResponseEntity.ok(opticalLineRepository.save(opticalLine));
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteOpticalLine(@PathVariable Long id) {
        Optional<OpticalLine> opticalLine = opticalLineRepository.findById(id);
        
        if (opticalLine.isPresent()) {
            opticalLineRepository.delete(opticalLine.get());
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
