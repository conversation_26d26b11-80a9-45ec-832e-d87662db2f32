package com.opticallines.backend.controller;

import com.opticallines.backend.entity.ConnectionPoint;
import com.opticallines.backend.repository.ConnectionPointRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/connection-points")
@CrossOrigin(origins = "*")
public class ConnectionPointController {
    
    @Autowired
    private ConnectionPointRepository connectionPointRepository;
    
    @GetMapping
    public List<ConnectionPoint> getAllConnectionPoints() {
        return connectionPointRepository.findAll();
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ConnectionPoint> getConnectionPointById(@PathVariable Long id) {
        Optional<ConnectionPoint> connectionPoint = connectionPointRepository.findById(id);
        return connectionPoint.map(ResponseEntity::ok)
                             .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/optical-line/{opticalLineId}")
    public List<ConnectionPoint> getConnectionPointsByOpticalLine(@PathVariable Long opticalLineId) {
        return connectionPointRepository.findByOpticalLineId(opticalLineId);
    }
    
    @GetMapping("/type/{pointType}")
    public List<ConnectionPoint> getConnectionPointsByType(@PathVariable ConnectionPoint.PointType pointType) {
        return connectionPointRepository.findByPointType(pointType);
    }
    
    @GetMapping("/status/{status}")
    public List<ConnectionPoint> getConnectionPointsByStatus(@PathVariable ConnectionPoint.PointStatus status) {
        return connectionPointRepository.findByStatus(status);
    }
    
    @GetMapping("/city/{city}")
    public List<ConnectionPoint> getConnectionPointsByCity(@PathVariable String city) {
        return connectionPointRepository.findByCity(city);
    }
    
    @GetMapping("/search")
    public List<ConnectionPoint> searchConnectionPoints(@RequestParam String name) {
        return connectionPointRepository.findByNameContaining(name);
    }
    
    @GetMapping("/bounds")
    public List<ConnectionPoint> getConnectionPointsInBounds(
            @RequestParam Double minLat,
            @RequestParam Double maxLat,
            @RequestParam Double minLon,
            @RequestParam Double maxLon) {
        return connectionPointRepository.findByLocationBounds(minLat, maxLat, minLon, maxLon);
    }
    
    @PostMapping
    public ConnectionPoint createConnectionPoint(@Valid @RequestBody ConnectionPoint connectionPoint) {
        return connectionPointRepository.save(connectionPoint);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ConnectionPoint> updateConnectionPoint(@PathVariable Long id, 
                                                               @Valid @RequestBody ConnectionPoint connectionPointDetails) {
        Optional<ConnectionPoint> optionalConnectionPoint = connectionPointRepository.findById(id);
        
        if (optionalConnectionPoint.isPresent()) {
            ConnectionPoint connectionPoint = optionalConnectionPoint.get();
            connectionPoint.setName(connectionPointDetails.getName());
            connectionPoint.setDescription(connectionPointDetails.getDescription());
            connectionPoint.setLatitude(connectionPointDetails.getLatitude());
            connectionPoint.setLongitude(connectionPointDetails.getLongitude());
            connectionPoint.setPointType(connectionPointDetails.getPointType());
            connectionPoint.setAddress(connectionPointDetails.getAddress());
            connectionPoint.setCity(connectionPointDetails.getCity());
            connectionPoint.setPostalCode(connectionPointDetails.getPostalCode());
            connectionPoint.setCountry(connectionPointDetails.getCountry());
            connectionPoint.setStatus(connectionPointDetails.getStatus());
            connectionPoint.setCapacity(connectionPointDetails.getCapacity());
            connectionPoint.setUsedCapacity(connectionPointDetails.getUsedCapacity());
            connectionPoint.setInstallationDate(connectionPointDetails.getInstallationDate());
            
            return ResponseEntity.ok(connectionPointRepository.save(connectionPoint));
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteConnectionPoint(@PathVariable Long id) {
        Optional<ConnectionPoint> connectionPoint = connectionPointRepository.findById(id);
        
        if (connectionPoint.isPresent()) {
            connectionPointRepository.delete(connectionPoint.get());
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
