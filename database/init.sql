-- Create database schema for optical lines database

-- Create optical_lines table
CREATE TABLE IF NOT EXISTS optical_lines (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    provider VARCHAR(255) NOT NULL,
    latitude_start DOUBLE PRECISION NOT NULL,
    longitude_start DOUBLE PRECISION NOT NULL,
    latitude_end DOUBLE PRECISION NOT NULL,
    longitude_end DOUBLE PRECISION NOT NULL,
    length_km DOUBLE PRECISION,
    fiber_count INTEGER,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    installation_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create connection_points table
CREATE TABLE IF NOT EXISTS connection_points (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    point_type VARCHA<PERSON>(50),
    address VARCHAR(500),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Slovakia',
    status VARCHAR(50) DEFAULT 'ACTIVE',
    capacity INTEGER,
    used_capacity INTEGER DEFAULT 0,
    installation_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    optical_line_id BIGINT,
    FOREIGN KEY (optical_line_id) REFERENCES optical_lines(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_optical_lines_provider ON optical_lines(provider);
CREATE INDEX IF NOT EXISTS idx_optical_lines_status ON optical_lines(status);
CREATE INDEX IF NOT EXISTS idx_optical_lines_location_start ON optical_lines(latitude_start, longitude_start);
CREATE INDEX IF NOT EXISTS idx_optical_lines_location_end ON optical_lines(latitude_end, longitude_end);

CREATE INDEX IF NOT EXISTS idx_connection_points_optical_line ON connection_points(optical_line_id);
CREATE INDEX IF NOT EXISTS idx_connection_points_type ON connection_points(point_type);
CREATE INDEX IF NOT EXISTS idx_connection_points_status ON connection_points(status);
CREATE INDEX IF NOT EXISTS idx_connection_points_location ON connection_points(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_connection_points_city ON connection_points(city);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_optical_lines_updated_at 
    BEFORE UPDATE ON optical_lines 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_connection_points_updated_at 
    BEFORE UPDATE ON connection_points 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
