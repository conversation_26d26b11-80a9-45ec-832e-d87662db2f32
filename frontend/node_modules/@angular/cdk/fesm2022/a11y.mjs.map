{"version": 3, "file": "a11y.mjs", "sources": ["../../../../../../src/cdk/a11y/aria-describer/aria-reference.ts", "../../../../../../src/cdk/a11y/aria-describer/aria-describer.ts", "../../../../../../src/cdk/a11y/key-manager/typeahead.ts", "../../../../../../src/cdk/a11y/key-manager/list-key-manager.ts", "../../../../../../src/cdk/a11y/key-manager/activedescendant-key-manager.ts", "../../../../../../src/cdk/a11y/key-manager/focus-key-manager.ts", "../../../../../../src/cdk/a11y/key-manager/tree-key-manager.ts", "../../../../../../src/cdk/a11y/key-manager/noop-tree-key-manager.ts", "../../../../../../src/cdk/a11y/interactivity-checker/interactivity-checker.ts", "../../../../../../src/cdk/a11y/focus-trap/focus-trap.ts", "../../../../../../src/cdk/a11y/focus-trap/configurable-focus-trap.ts", "../../../../../../src/cdk/a11y/focus-trap/event-listener-inert-strategy.ts", "../../../../../../src/cdk/a11y/focus-trap/focus-trap-inert-strategy.ts", "../../../../../../src/cdk/a11y/focus-trap/focus-trap-manager.ts", "../../../../../../src/cdk/a11y/focus-trap/configurable-focus-trap-factory.ts", "../../../../../../src/cdk/a11y/fake-event-detection.ts", "../../../../../../src/cdk/a11y/input-modality/input-modality-detector.ts", "../../../../../../src/cdk/a11y/live-announcer/live-announcer-tokens.ts", "../../../../../../src/cdk/a11y/live-announcer/live-announcer.ts", "../../../../../../src/cdk/a11y/focus-monitor/focus-monitor.ts", "../../../../../../src/cdk/a11y/high-contrast-mode/high-contrast-mode-detector.ts", "../../../../../../src/cdk/a11y/a11y-module.ts", "../../../../../../src/cdk/a11y/id-generator.ts", "../../../../../../src/cdk/a11y/a11y_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function addAriaReferencedId(el: Element, attr: `aria-${string}`, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function removeAriaReferencedId(el: Element, attr: `aria-${string}`, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function getAriaReferenceIds(el: Element, attr: string): string[] {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Injectable, OnDestroy, APP_ID, inject} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId} from './aria-reference';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n/**\n * Interface used to register message elements and keep a count of how many registrations have\n * the same message and the reference to the message element used for the `aria-describedby`.\n */\nexport interface RegisteredMessage {\n  /** The element containing the message. */\n  messageElement: Element;\n\n  /** The number of elements that reference this message element via `aria-describedby`. */\n  referenceCount: number;\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nexport const MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nexport const CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nexport const CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\n@Injectable({providedIn: 'root'})\nexport class AriaDescriber implements OnDestroy {\n  private _platform = inject(Platform);\n  private _document = inject(DOCUMENT);\n\n  /** Map of all registered message elements that have been placed into the document. */\n  private _messageRegistry = new Map<string | Element, RegisteredMessage>();\n\n  /** Container for all registered messages. */\n  private _messagesContainer: HTMLElement | null = null;\n\n  /** Unique ID for the service. */\n  private readonly _id = `${nextId++}`;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n\n  /**\n   * Adds to the host element an aria-describedby reference to a hidden element that contains\n   * the message. If the same message has already been registered, then it will reuse the created\n   * message element.\n   */\n  describe(hostElement: Element, message: string, role?: string): void;\n\n  /**\n   * Adds to the host element an aria-describedby reference to an already-existing message element.\n   */\n  describe(hostElement: Element, message: HTMLElement): void;\n\n  describe(hostElement: Element, message: string | HTMLElement, role?: string): void {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {messageElement: message, referenceCount: 0});\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n\n  /** Removes the host element's aria-describedby reference to the message. */\n  removeDescription(hostElement: Element, message: string, role?: string): void;\n\n  /** Removes the host element's aria-describedby reference to the message element. */\n  removeDescription(hostElement: Element, message: HTMLElement): void;\n\n  removeDescription(hostElement: Element, message: string | HTMLElement, role?: string): void {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(\n      `[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`,\n    );\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  private _createMessageElement(message: string, role?: string) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n\n    this._createMessagesContainer();\n    this._messagesContainer!.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {messageElement, referenceCount: 0});\n  }\n\n  /** Deletes the message element from the global messages container. */\n  private _deleteMessageElement(key: string | Element) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n\n  /** Creates the global container for all aria-describedby messages. */\n  private _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(\n      `.${containerClassName}[platform=\"server\"]`,\n    );\n\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n\n    const messagesContainer = this._document.createElement('div');\n\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  private _removeCdkDescribedByReferenceIds(element: Element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(\n      id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0,\n    );\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  private _addMessageReference(element: Element, key: string | Element) {\n    const registeredMessage = this._messageRegistry.get(key)!;\n\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  private _removeMessageReference(element: Element, key: string | Element) {\n    const registeredMessage = this._messageRegistry.get(key)!;\n    registeredMessage.referenceCount--;\n\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n\n  /** Returns true if the element has been described by the provided message ID. */\n  private _isElementDescribedByMessage(element: Element, key: string | Element): boolean {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n\n  /** Determines whether a message can be described on a particular element. */\n  private _canBeDescribed(element: Element, message: string | HTMLElement | void): boolean {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n\n  /** Checks whether a node is an Element node. */\n  private _isElementNode(element: Node): element is Element {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n}\n\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message: string | Element, role?: string): string | Element {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element: HTMLElement, serviceId: string) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {A, NINE, Z, ZERO} from '@angular/cdk/keycodes';\nimport {Subject, Observable} from 'rxjs';\nimport {debounceTime, filter, map, tap} from 'rxjs/operators';\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n\ninterface TypeaheadItem {\n  getLabel?(): string;\n}\n\ninterface TypeaheadConfig<T> {\n  debounceInterval?: number;\n  skipPredicate?: (item: T) => boolean | undefined;\n}\n\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nexport class Typeahead<T extends TypeaheadItem> {\n  private readonly _letterKeyStream = new Subject<string>();\n  private _items: readonly T[] = [];\n  private _selectedItemIndex = -1;\n\n  /** Buffer for the letters that the user has pressed */\n  private _pressedLetters: string[] = [];\n\n  private _skipPredicateFn?: (item: T) => boolean | undefined;\n\n  private readonly _selectedItem = new Subject<T>();\n  readonly selectedItem: Observable<T> = this._selectedItem;\n\n  constructor(initialItems: readonly T[], config?: TypeaheadConfig<T>) {\n    const typeAheadInterval =\n      typeof config?.debounceInterval === 'number'\n        ? config.debounceInterval\n        : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n\n    if (\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      initialItems.length &&\n      initialItems.some(item => typeof item.getLabel !== 'function')\n    ) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n\n  setCurrentSelectedItemIndex(index: number) {\n    this._selectedItemIndex = index;\n  }\n\n  setItems(items: readonly T[]) {\n    this._items = items;\n  }\n\n  handleKey(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping(): boolean {\n    return this._pressedLetters.length > 0;\n  }\n\n  /** Resets the currently stored sequence of typed letters. */\n  reset(): void {\n    this._pressedLetters = [];\n  }\n\n  private _setupKeyHandler(typeAheadInterval: number) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream\n      .pipe(\n        tap(letter => this._pressedLetters.push(letter)),\n        debounceTime(typeAheadInterval),\n        filter(() => this._pressedLetters.length > 0),\n        map(() => this._pressedLetters.join('').toLocaleUpperCase()),\n      )\n      .subscribe(inputString => {\n        // Start at 1 because we want to start searching at the item immediately\n        // following the current active item.\n        for (let i = 1; i < this._items.length + 1; i++) {\n          const index = (this._selectedItemIndex + i) % this._items.length;\n          const item = this._items[index];\n\n          if (\n            !this._skipPredicateFn?.(item) &&\n            item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0\n          ) {\n            this._selectedItem.next(item);\n            break;\n          }\n        }\n\n        this._pressedLetters = [];\n      });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOWN_ARROW,\n  END,\n  HOME,\n  LEFT_ARROW,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  TAB,\n  UP_ARROW,\n  hasModifierKey,\n} from '@angular/cdk/keycodes';\nimport {EffectRef, Injector, QueryList, Signal, effect, isSignal, signal} from '@angular/core';\nimport {Subject, Subscription} from 'rxjs';\nimport {Typeahead} from './typeahead';\n\n/** This interface is for items that can be passed to a ListKeyManager. */\nexport interface ListKeyManagerOption {\n  /** Whether the option is disabled. */\n  disabled?: boolean;\n\n  /** Gets the label for this option. */\n  getLabel?(): string;\n}\n\n/** Modifier keys handled by the ListKeyManager. */\nexport type ListKeyManagerModifierKey = 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nexport class ListKeyManager<T extends ListKeyManagerOption> {\n  private _activeItemIndex = -1;\n  private _activeItem = signal<T | null>(null);\n  private _wrap = false;\n  private _typeaheadSubscription = Subscription.EMPTY;\n  private _itemChangesSubscription?: Subscription;\n  private _vertical = true;\n  private _horizontal: 'ltr' | 'rtl' | null;\n  private _allowedModifierKeys: ListKeyManagerModifierKey[] = [];\n  private _homeAndEnd = false;\n  private _pageUpAndDown = {enabled: false, delta: 10};\n  private _effectRef: EffectRef | undefined;\n  private _typeahead?: Typeahead<T>;\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  private _skipPredicateFn = (item: T) => item.disabled;\n\n  constructor(items: QueryList<T> | T[] | readonly T[]);\n  constructor(items: Signal<T[]> | Signal<readonly T[]>, injector: Injector);\n  constructor(\n    private _items: QueryList<T> | T[] | readonly T[] | Signal<T[]> | Signal<readonly T[]>,\n    injector?: Injector,\n  ) {\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe((newItems: QueryList<T>) =>\n        this._itemsChanged(newItems.toArray()),\n      );\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n\n      this._effectRef = effect(() => this._itemsChanged(_items()), {injector});\n    }\n  }\n\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  readonly tabOut = new Subject<void>();\n\n  /** Stream that emits whenever the active item of the list manager changes. */\n  readonly change = new Subject<number>();\n\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate: (item: T) => boolean): this {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true): this {\n    this._wrap = shouldWrap;\n    return this;\n  }\n\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled: boolean = true): this {\n    this._vertical = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction: 'ltr' | 'rtl' | null): this {\n    this._horizontal = direction;\n    return this;\n  }\n\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys: ListKeyManagerModifierKey[]): this {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval: number = 200): this {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n\n    this._typeaheadSubscription.unsubscribe();\n\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item),\n    });\n\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n\n    return this;\n  }\n\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead(): this {\n    this._typeahead?.reset();\n    return this;\n  }\n\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled: boolean = true): this {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled: boolean = true, delta: number = 10): this {\n    this._pageUpAndDown = {enabled, delta};\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the index specified.\n   * @param index The index of the item to be set as active.\n   */\n  setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the specified item.\n   * @param item The item to be set as active.\n   */\n  setActiveItem(item: T): void;\n\n  setActiveItem(item: any): void {\n    const previousActiveItem = this._activeItem();\n\n    this.updateActiveItem(item);\n\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n    const modifiers: ListKeyManagerModifierKey[] = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  get activeItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The active item. */\n  get activeItem(): T | null {\n    return this._activeItem();\n  }\n\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping(): boolean {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive(): void {\n    this._setActiveItemByIndex(0, 1);\n  }\n\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive(): void {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive(): void {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive(): void {\n    this._activeItemIndex < 0 && this._wrap\n      ? this.setLastItemActive()\n      : this._setActiveItemByDelta(-1);\n  }\n\n  /**\n   * Allows setting the active without any other effects.\n   * @param index Index of the item to be set as active.\n   */\n  updateActiveItem(index: number): void;\n\n  /**\n   * Allows setting the active item without any other effects.\n   * @param item Item to be set as active.\n   */\n  updateActiveItem(item: T): void;\n\n  updateActiveItem(item: any): void {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  private _setActiveItemByDelta(delta: -1 | 1): void {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  private _setActiveInWrapMode(delta: -1 | 1): void {\n    const items = this._getItemsArray();\n\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  private _setActiveInDefaultMode(delta: -1 | 1): void {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  private _setActiveItemByIndex(index: number, fallbackDelta: -1 | 1): void {\n    const items = this._getItemsArray();\n\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n\n  /** Returns the items as an array. */\n  private _getItemsArray(): T[] | readonly T[] {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n\n  /** Callback for when the items have changed. */\n  private _itemsChanged(newItems: T[] | readonly T[]) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\n\n/**\n * This is the interface for highlightable items (used by the ActiveDescendantKeyManager).\n * Each item must know how to style itself as active or inactive and whether or not it is\n * currently disabled.\n */\nexport interface Highlightable extends ListKeyManagerOption {\n  /** Applies the styles for an active item to this item. */\n  setActiveStyles(): void;\n\n  /** Applies the styles for an inactive item to this item. */\n  setInactiveStyles(): void;\n}\n\nexport class ActiveDescendantKeyManager<T> extends ListKeyManager<Highlightable & T> {\n  /**\n   * Sets the active item to the item at the specified index and adds the\n   * active styles to the newly active item. Also removes active styles\n   * from the previously active item.\n   * @param index Index of the item to be set as active.\n   */\n  override setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the item to the specified one and adds the\n   * active styles to the it. Also removes active styles from the\n   * previously active item.\n   * @param item Item to be set as active.\n   */\n  override setActiveItem(item: T): void;\n\n  override setActiveItem(index: any): void {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\nimport {FocusOrigin} from '../focus-monitor/focus-monitor';\n\n/**\n * This is the interface for focusable items (used by the FocusKeyManager).\n * Each item must know how to focus itself, whether or not it is currently disabled\n * and be able to supply its label.\n */\nexport interface FocusableOption extends ListKeyManagerOption {\n  /** Focuses the `FocusableOption`. */\n  focus(origin?: FocusOrigin): void;\n}\n\nexport class FocusKeyManager<T> extends ListKeyManager<FocusableOption & T> {\n  private _origin: FocusOrigin = 'program';\n\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin: FocusOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the specified\n   * index and focuses the newly active item.\n   * @param index Index of the item to be set as active.\n   */\n  override setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the item that is specified and focuses it.\n   * @param item Item to be set as active.\n   */\n  override setActiveItem(item: T): void;\n\n  override setActiveItem(item: any): void {\n    super.setActiveItem(item);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, QueryList} from '@angular/core';\nimport {coerceObservable} from '@angular/cdk/coercion/private';\nimport {Observable, Subject, Subscription, isObservable, of as observableOf} from 'rxjs';\nimport {take} from 'rxjs/operators';\nimport {\n  TreeKeyManagerFactory,\n  TreeKeyManagerItem,\n  TreeKeyManagerOptions,\n  TreeKeyManagerStrategy,\n} from './tree-key-manager-strategy';\nimport {Typeahead} from './typeahead';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nexport class TreeKeyManager<T extends TreeKeyManagerItem> implements TreeKeyManagerStrategy<T> {\n  /** The index of the currently active (focused) item. */\n  private _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  private _activeItem: T | null = null;\n  /** Whether or not we activate the item when it's focused. */\n  private _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  private _horizontalOrientation: 'ltr' | 'rtl' = 'ltr';\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  private _skipPredicateFn = (_item: T) => false;\n\n  /** Function to determine equivalent items. */\n  private _trackByFn: (item: T) => unknown = (item: T) => item;\n\n  /** Synchronous cache of the items to manage. */\n  private _items: T[] = [];\n\n  private _typeahead?: Typeahead<T>;\n  private _typeaheadSubscription = Subscription.EMPTY;\n\n  private _hasInitialFocused = false;\n\n  private _initializeFocus(): void {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n\n    const activeItem = this._items[activeIndex];\n\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n\n    this._hasInitialFocused = true;\n  }\n\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items: Observable<T[]> | QueryList<T> | T[], config: TreeKeyManagerOptions<T>) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe((newItems: QueryList<T>) => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n\n  /** Stream that emits any time the focused item changes. */\n  readonly change = new Subject<T | null>();\n\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event: KeyboardEvent) {\n    const key = event.key;\n\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl'\n          ? this._collapseCurrentItem()\n          : this._expandCurrentItem();\n        break;\n\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl'\n          ? this._expandCurrentItem()\n          : this._collapseCurrentItem();\n        break;\n\n      case 'Home':\n        this._focusFirstItem();\n        break;\n\n      case 'End':\n        this._focusLastItem();\n        break;\n\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  getActiveItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The currently active item. */\n  getActiveItem(): T | null {\n    return this._activeItem;\n  }\n\n  /** Focus the first available item. */\n  private _focusFirstItem(): void {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n\n  /** Focus the last available item. */\n  private _focusLastItem(): void {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n\n  /** Focus the next available item. */\n  private _focusNextItem(): void {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n\n  /** Focus the previous available item. */\n  private _focusPreviousItem(): void {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n\n  /**\n   * Focus the provided item by index.\n   * @param index The index of the item to focus.\n   * @param options Additional focusing options.\n   */\n  focusItem(index: number, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(item: T, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(itemOrIndex: number | T, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(itemOrIndex: number | T, options: {emitChangeEvent?: boolean} = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n\n    let index =\n      typeof itemOrIndex === 'number'\n        ? itemOrIndex\n        : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n\n    // If we're just setting the same item, don't re-call activate or focus\n    if (\n      this._activeItem !== null &&\n      this._trackByFn(activeItem) === this._trackByFn(this._activeItem)\n    ) {\n      return;\n    }\n\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n\n  private _updateActiveItemIndex(newItems: T[]) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n\n    const newIndex = newItems.findIndex(\n      item => this._trackByFn(item) === this._trackByFn(activeItem),\n    );\n\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n\n  private _setTypeAhead(debounceInterval: number | boolean) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item),\n    });\n\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n\n  private _findNextAvailableItemIndex(startingIndex: number) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n\n  private _findPreviousAvailableItemIndex(startingIndex: number) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  private _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent as T)) {\n        return;\n      }\n      this.focusItem(parent as T);\n    }\n  }\n\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  private _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren())\n        .pipe(take(1))\n        .subscribe(children => {\n          const firstChild = children.find(child => !this._skipPredicateFn(child as T));\n          if (!firstChild) {\n            return;\n          }\n          this.focusItem(firstChild as T);\n        });\n    }\n  }\n\n  private _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean'\n      ? this._activeItem.isExpanded\n      : this._activeItem.isExpanded();\n  }\n\n  private _isItemDisabled(item: TreeKeyManagerItem) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n\n  /** For all items that are the same level as the current item, we expand those items. */\n  private _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = observableOf(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n\n  private _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n\n/** @docs-private */\nexport function TREE_KEY_MANAGER_FACTORY<T extends TreeKeyManagerItem>(): TreeKeyManagerFactory<T> {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n\n/** Injection token that determines the key manager to use. */\nexport const TREE_KEY_MANAGER = new InjectionToken<TreeKeyManagerFactory<any>>('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY,\n});\n\n/** @docs-private */\nexport const TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Subject} from 'rxjs';\nimport {TREE_KEY_MANAGER} from './tree-key-manager';\nimport {\n  TreeKeyManagerFactory,\n  TreeKeyManagerItem,\n  TreeKeyManagerStrategy,\n} from './tree-key-manager-strategy';\nimport {Provider} from '@angular/core';\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport class NoopTreeKeyManager<T extends TreeKeyManagerItem> implements TreeKeyManagerStrategy<T> {\n  readonly _isNoopTreeKeyManager = true;\n\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  readonly change = new Subject<T | null>();\n\n  destroy() {\n    this.change.complete();\n  }\n\n  onKeydown() {\n    // noop\n  }\n\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n\n  focusItem() {\n    // noop\n  }\n}\n\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport function NOOP_TREE_KEY_MANAGER_FACTORY<\n  T extends TreeKeyManagerItem,\n>(): TreeKeyManagerFactory<T> {\n  return () => new NoopTreeKeyManager<T>();\n}\n\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport const NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER: Provider = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {Injectable, inject} from '@angular/core';\n\n/**\n * Configuration for the isFocusable method.\n */\nexport class IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility: boolean = false;\n}\n\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\n@Injectable({providedIn: 'root'})\nexport class InteractivityChecker {\n  private _platform = inject(Platform);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element: HTMLElement): boolean {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element: HTMLElement): boolean {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element: HTMLElement): boolean {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n\n    return element.tabIndex >= 0;\n  }\n\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element: HTMLElement, config?: IsFocusableConfig): boolean {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return (\n      isPotentiallyFocusable(element) &&\n      !this.isDisabled(element) &&\n      (config?.ignoreVisibility || this.isVisible(element))\n    );\n  }\n}\n\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window: Window) {\n  try {\n    return window.frameElement as HTMLElement;\n  } catch {\n    return null;\n  }\n}\n\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element: HTMLElement): boolean {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(\n    element.offsetWidth ||\n    element.offsetHeight ||\n    (typeof element.getClientRects === 'function' && element.getClientRects().length)\n  );\n}\n\n/** Gets whether an element's  */\nfunction isNativeFormElement(element: Node) {\n  let nodeName = element.nodeName.toLowerCase();\n  return (\n    nodeName === 'input' ||\n    nodeName === 'select' ||\n    nodeName === 'button' ||\n    nodeName === 'textarea'\n  );\n}\n\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element: HTMLElement): boolean {\n  return isInputElement(element) && element.type == 'hidden';\n}\n\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element: HTMLElement): boolean {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n\n/** Gets whether an element is an input element. */\nfunction isInputElement(element: HTMLElement): element is HTMLInputElement {\n  return element.nodeName.toLowerCase() == 'input';\n}\n\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element: HTMLElement): element is HTMLAnchorElement {\n  return element.nodeName.toLowerCase() == 'a';\n}\n\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element: HTMLElement): boolean {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element: HTMLElement): number | null {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element: HTMLElement): boolean {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && (element as HTMLInputElement).type;\n\n  return (\n    inputType === 'text' ||\n    inputType === 'password' ||\n    nodeName === 'select' ||\n    nodeName === 'textarea'\n  );\n}\n\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element: HTMLElement): boolean {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return (\n    isNativeFormElement(element) ||\n    isAnchorWithHref(element) ||\n    element.hasAttribute('contenteditable') ||\n    hasValidTabIndex(element)\n  );\n}\n\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node: HTMLElement): Window {\n  // ownerDocument is null if `node` itself *is* a document.\n  return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform, _getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterContentInit,\n  Directive,\n  DoCheck,\n  ElementRef,\n  Injectable,\n  Injector,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  afterNextRender,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nexport class FocusTrap {\n  private _startAnchor: HTMLElement | null;\n  private _endAnchor: HTMLElement | null;\n  private _hasAttached = false;\n\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  protected startAnchorListener = () => this.focusLastTabbableElement();\n  protected endAnchorListener = () => this.focusFirstTabbableElement();\n\n  /** Whether the focus trap is active. */\n  get enabled(): boolean {\n    return this._enabled;\n  }\n  set enabled(value: boolean) {\n    this._enabled = value;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  protected _enabled: boolean = true;\n\n  constructor(\n    readonly _element: HTMLElement,\n    private _checker: InteractivityChecker,\n    readonly _ngZone: NgZone,\n    readonly _document: Document,\n    deferAnchors = false,\n    /** @breaking-change 20.0.0 param to become required */\n    readonly _injector?: Injector,\n  ) {\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors(): boolean {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor!.addEventListener('focus', this.startAnchorListener);\n      }\n\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor!.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor!, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor!, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n\n    return this._hasAttached;\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  private _getRegionBoundary(bound: 'start' | 'end'): HTMLElement | null {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(\n      `[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`,\n    ) as NodeListOf<HTMLElement>;\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(\n            `Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n              `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n              `attribute will be removed in 8.0.0.`,\n            markers[i],\n          );\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(\n            `Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n              `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n              `will be removed in 8.0.0.`,\n            markers[i],\n          );\n        }\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length\n      ? markers[markers.length - 1]\n      : this._getLastTabbableElement(this._element);\n  }\n\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options?: FocusOptions): boolean {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(\n      `[cdk-focus-initial], ` + `[cdkFocusInitial]`,\n    ) as HTMLElement;\n\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if (\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        redirectToElement.hasAttribute(`cdk-focus-initial`)\n      ) {\n        console.warn(\n          `Found use of deprecated attribute 'cdk-focus-initial', ` +\n            `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n            `will be removed in 8.0.0`,\n          redirectToElement,\n        );\n      }\n\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if (\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        !this._checker.isFocusable(redirectToElement)\n      ) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement) as HTMLElement;\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n\n      redirectToElement.focus(options);\n      return true;\n    }\n\n    return this.focusFirstTabbableElement(options);\n  }\n\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options?: FocusOptions): boolean {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options?: FocusOptions): boolean {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached(): boolean {\n    return this._hasAttached;\n  }\n\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  private _getFirstTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    const children = root.children;\n\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild =\n        children[i].nodeType === this._document.ELEMENT_NODE\n          ? this._getFirstTabbableElement(children[i] as HTMLElement)\n          : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  private _getLastTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in reverse DOM order.\n    const children = root.children;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild =\n        children[i].nodeType === this._document.ELEMENT_NODE\n          ? this._getLastTabbableElement(children[i] as HTMLElement)\n          : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Creates an anchor element. */\n  private _createAnchor(): HTMLElement {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  private _toggleAnchorTabIndex(isEnabled: boolean, anchor: HTMLElement) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  protected toggleAnchors(enabled: boolean) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n\n  /** Executes a function when the zone is stable. */\n  private _executeOnStable(fn: () => any): void {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {injector: this._injector});\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n\n/**\n * Factory that allows easy instantiation of focus traps.\n */\n@Injectable({providedIn: 'root'})\nexport class FocusTrapFactory {\n  private _checker = inject(InteractivityChecker);\n  private _ngZone = inject(NgZone);\n\n  private _document = inject(DOCUMENT);\n  private _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean = false): FocusTrap {\n    return new FocusTrap(\n      element,\n      this._checker,\n      this._ngZone,\n      this._document,\n      deferCaptureElements,\n      this._injector,\n    );\n  }\n}\n\n/** Directive for trapping focus within a region. */\n@Directive({\n  selector: '[cdkTrapFocus]',\n  exportAs: 'cdkTrapFocus',\n})\nexport class CdkTrapFocus implements OnDestroy, AfterContentInit, OnChanges, DoCheck {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _focusTrapFactory = inject(FocusTrapFactory);\n\n  /** Underlying FocusTrap instance. */\n  focusTrap: FocusTrap;\n\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  private _previouslyFocusedElement: HTMLElement | null = null;\n\n  /** Whether the focus trap is active. */\n  @Input({alias: 'cdkTrapFocus', transform: booleanAttribute})\n  get enabled(): boolean {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value: boolean) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  @Input({alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute}) autoCapture: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const platform = inject(Platform);\n\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const autoCaptureChange = changes['autoCapture'];\n\n    if (\n      autoCaptureChange &&\n      !autoCaptureChange.firstChange &&\n      this.autoCapture &&\n      this.focusTrap?.hasAttached()\n    ) {\n      this._captureFocus();\n    }\n  }\n\n  private _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injector, NgZone} from '@angular/core';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {ConfigurableFocusTrapConfig} from './configurable-focus-trap-config';\nimport {FocusTrap} from './focus-trap';\nimport {FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {FocusTrapManager, ManagedFocusTrap} from './focus-trap-manager';\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nexport class ConfigurableFocusTrap extends FocusTrap implements ManagedFocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  override get enabled(): boolean {\n    return this._enabled;\n  }\n  override set enabled(value: boolean) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n\n  constructor(\n    _element: HTMLElement,\n    _checker: InteractivityChecker,\n    _ngZone: NgZone,\n    _document: Document,\n    private _focusTrapManager: FocusTrapManager,\n    private _inertStrategy: FocusTrapInertStrategy,\n    config: ConfigurableFocusTrapConfig,\n    injector?: Injector,\n  ) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager.register(this);\n  }\n\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  override destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {ConfigurableFocusTrap} from './configurable-focus-trap';\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nexport class EventListenerFocusTrapInertStrategy implements FocusTrapInertStrategy {\n  /** Focus event handler. */\n  private _listener: ((e: FocusEvent) => void) | null = null;\n\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap: ConfigurableFocusTrap): void {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener!, true);\n    }\n\n    this._listener = (e: FocusEvent) => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener!, true);\n    });\n  }\n\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap: ConfigurableFocusTrap): void {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener!, true);\n    this._listener = null;\n  }\n\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  private _trapFocus(focusTrap: ConfigurableFocusTrap, event: FocusEvent) {\n    const target = event.target as HTMLElement;\n    const focusTrapRoot = focusTrap._element;\n\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {FocusTrap} from './focus-trap';\n\n/** The injection token used to specify the inert strategy. */\nexport const FOCUS_TRAP_INERT_STRATEGY = new InjectionToken<FocusTrapInertStrategy>(\n  'FOCUS_TRAP_INERT_STRATEGY',\n);\n\n/**\n * A strategy that dictates how FocusTrap should prevent elements\n * outside of the FocusTrap from being focused.\n */\nexport interface FocusTrapInertStrategy {\n  /** Makes all elements outside focusTrap unfocusable. */\n  preventFocus(focusTrap: FocusTrap): void;\n  /** Reverts elements made unfocusable by preventFocus to their previous state. */\n  allowFocus(focusTrap: FocusTrap): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\n\n/**\n * A FocusTrap managed by FocusTrapManager.\n * Implemented by ConfigurableFocusTrap to avoid circular dependency.\n */\nexport interface ManagedFocusTrap {\n  _enable(): void;\n  _disable(): void;\n  focusInitialElementWhenReady(): Promise<boolean>;\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\n@Injectable({providedIn: 'root'})\nexport class FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  private _focusTrapStack: ManagedFocusTrap[] = [];\n\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap: ManagedFocusTrap): void {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n\n    let stack = this._focusTrapStack;\n\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap: ManagedFocusTrap): void {\n    focusTrap._disable();\n\n    const stack = this._focusTrapStack;\n\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Injectable, Injector, NgZone, inject} from '@angular/core';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {ConfigurableFocusTrap} from './configurable-focus-trap';\nimport {ConfigurableFocusTrapConfig} from './configurable-focus-trap-config';\nimport {EventListenerFocusTrapInertStrategy} from './event-listener-inert-strategy';\nimport {FOCUS_TRAP_INERT_STRATEGY, FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {FocusTrapManager} from './focus-trap-manager';\n\n/** Factory that allows easy instantiation of configurable focus traps. */\n@Injectable({providedIn: 'root'})\nexport class ConfigurableFocusTrapFactory {\n  private _checker = inject(InteractivityChecker);\n  private _ngZone = inject(NgZone);\n  private _focusTrapManager = inject(FocusTrapManager);\n\n  private _document = inject(DOCUMENT);\n  private _inertStrategy: FocusTrapInertStrategy;\n\n  private readonly _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {optional: true});\n\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param config The focus trap configuration.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, config?: ConfigurableFocusTrapConfig): ConfigurableFocusTrap;\n\n  /**\n   * @deprecated Pass a config object instead of the `deferCaptureElements` flag.\n   * @breaking-change 11.0.0\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean): ConfigurableFocusTrap;\n\n  create(\n    element: HTMLElement,\n    config: ConfigurableFocusTrapConfig | boolean = {defer: false},\n  ): ConfigurableFocusTrap {\n    let configObject: ConfigurableFocusTrapConfig;\n    if (typeof config === 'boolean') {\n      configObject = {defer: config};\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(\n      element,\n      this._checker,\n      this._ngZone,\n      this._document,\n      this._focusTrapManager,\n      this._inertStrategy,\n      configObject,\n      this._injector,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nexport function isFakeMousedownFromScreenReader(event: MouseEvent): boolean {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nexport function isFakeTouchstartFromScreenReader(event: TouchEvent): boolean {\n  const touch: Touch | undefined =\n    (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return (\n    !!touch &&\n    touch.identifier === -1 &&\n    (touch.radiusX == null || touch.radiusX === 1) &&\n    (touch.radiusY == null || touch.radiusY === 1)\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ALT, CONTROL, MAC_META, META, SHIFT} from '@angular/cdk/keycodes';\nimport {\n  Injectable,\n  InjectionToken,\n  OnDestroy,\n  NgZone,\n  inject,\n  RendererFactory2,\n} from '@angular/core';\nimport {Platform, _bindEventWithOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {BehaviorSubject, Observable} from 'rxjs';\nimport {distinctUntilChanged, skip} from 'rxjs/operators';\nimport {\n  isFakeMousedownFromScreenReader,\n  isFakeTouchstartFromScreenReader,\n} from '../fake-event-detection';\n\n/**\n * The input modalities detected by this service. Null is used if the input modality is unknown.\n */\nexport type InputModality = 'keyboard' | 'mouse' | 'touch' | null;\n\n/** Options to configure the behavior of the InputModalityDetector. */\nexport interface InputModalityDetectorOptions {\n  /** Keys to ignore when detecting keyboard input modality. */\n  ignoreKeys?: number[];\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nexport const INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken<InputModalityDetectorOptions>(\n  'cdk-input-modality-detector-options',\n);\n\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nexport const INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS: InputModalityDetectorOptions = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nexport const TOUCH_BUFFER_MS = 650;\n\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true,\n};\n\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\n@Injectable({providedIn: 'root'})\nexport class InputModalityDetector implements OnDestroy {\n  private readonly _platform = inject(Platform);\n  private readonly _listenerCleanups: (() => void)[] | undefined;\n\n  /** Emits whenever an input modality is detected. */\n  readonly modalityDetected: Observable<InputModality>;\n\n  /** Emits when the input modality changes. */\n  readonly modalityChanged: Observable<InputModality>;\n\n  /** The most recently detected input modality. */\n  get mostRecentModality(): InputModality {\n    return this._modality.value;\n  }\n\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget: HTMLElement | null = null;\n\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  private readonly _modality = new BehaviorSubject<InputModality>(null);\n\n  /** Options for this InputModalityDetector. */\n  private readonly _options: InputModalityDetectorOptions;\n\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  private _lastTouchMs = 0;\n\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  private _onKeydown = (event: KeyboardEvent) => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  private _onMousedown = (event: MouseEvent) => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  private _onTouchstart = (event: TouchEvent) => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject<Document>(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {optional: true});\n\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'keydown',\n            this._onKeydown,\n            modalityEventListenerOptions,\n          ),\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'mousedown',\n            this._onMousedown,\n            modalityEventListenerOptions,\n          ),\n          _bindEventWithOptions(\n            renderer,\n            document,\n            'touchstart',\n            this._onTouchstart,\n            modalityEventListenerOptions,\n          ),\n        ];\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n// The tokens for the live announcer are defined in a separate file from LiveAnnouncer\n// as a workaround for https://github.com/angular/angular/issues/22559\n\n/** Possible politeness levels. */\nexport type AriaLivePoliteness = 'off' | 'polite' | 'assertive';\n\nexport const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken<HTMLElement | null>(\n  'liveAnnouncerElement',\n  {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n  },\n);\n\n/** @docs-private */\nexport function LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY(): null {\n  return null;\n}\n\n/** Object that can be used to configure the default options for the LiveAnnouncer. */\nexport interface LiveAnnouncerDefaultOptions {\n  /** Default politeness for the announcements. */\n  politeness?: AriaLivePoliteness;\n\n  /** Default duration for the announcement messages. */\n  duration?: number;\n}\n\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nexport const LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken<LiveAnnouncerDefaultOptions>(\n  'LIVE_ANNOUNCER_DEFAULT_OPTIONS',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ContentObserver} from '@angular/cdk/observers';\nimport {DOCUMENT} from '@angular/common';\nimport {Directive, ElementRef, Injectable, Input, NgZone, OnDestroy, inject} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {\n  AriaLivePoliteness,\n  LiveAnnouncerDefaultOptions,\n  LIVE_ANNOUNCER_ELEMENT_TOKEN,\n  LIVE_ANNOUNCER_DEFAULT_OPTIONS,\n} from './live-announcer-tokens';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\nlet uniqueIds = 0;\n\n@Injectable({providedIn: 'root'})\nexport class LiveAnnouncer implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _defaultOptions = inject<LiveAnnouncerDefaultOptions>(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  private _liveElement: HTMLElement;\n  private _document = inject(DOCUMENT);\n  private _previousTimeout: ReturnType<typeof setTimeout>;\n  private _currentPromise: Promise<void> | undefined;\n  private _currentResolve: (() => void) | undefined;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {optional: true});\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param politeness The politeness of the announcer element.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness?: AriaLivePoliteness): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param duration Time in milliseconds after which to clear out the announcer element. Note\n   *   that this takes effect after the message has been added to the DOM, which can be up to\n   *   100ms after `announce` has been called.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, duration?: number): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param politeness The politeness of the announcer element.\n   * @param duration Time in milliseconds after which to clear out the announcer element. Note\n   *   that this takes effect after the message has been added to the DOM, which can be up to\n   *   100ms after `announce` has been called.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness?: AriaLivePoliteness, duration?: number): Promise<void>;\n\n  announce(message: string, ...args: any[]): Promise<void> {\n    const defaultOptions = this._defaultOptions;\n    let politeness: AriaLivePoliteness | undefined;\n    let duration: number | undefined;\n\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n\n    this.clear();\n    clearTimeout(this._previousTimeout);\n\n    if (!politeness) {\n      politeness =\n        defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n      }\n\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n\n      return this._currentPromise;\n    });\n  }\n\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null!;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n\n  private _createLiveElement(): HTMLElement {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  private _exposeAnnouncerToModals(id: string) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll(\n      'body > .cdk-overlay-container [aria-modal=\"true\"]',\n    );\n\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n}\n\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\n@Directive({\n  selector: '[cdkAriaLive]',\n  exportAs: 'cdkAriaLive',\n})\nexport class CdkAriaLive implements OnDestroy {\n  private _elementRef = inject(ElementRef);\n  private _liveAnnouncer = inject(LiveAnnouncer);\n  private _contentObserver = inject(ContentObserver);\n  private _ngZone = inject(NgZone);\n\n  /** The aria-live politeness level to use when announcing messages. */\n  @Input('cdkAriaLive')\n  get politeness(): AriaLivePoliteness {\n    return this._politeness;\n  }\n  set politeness(value: AriaLivePoliteness) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  private _politeness: AriaLivePoliteness = 'polite';\n\n  /** Time in milliseconds after which to clear out the announcer element. */\n  @Input('cdkAriaLiveDuration') duration: number;\n\n  private _previousAnnouncedText?: string;\n  private _subscription: Subscription | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Platform,\n  normalizePassiveListenerOptions,\n  _getShadowRoot,\n  _getEventTarget,\n} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  InjectionToken,\n  NgZone,\n  OnDestroy,\n  Output,\n  AfterViewInit,\n  inject,\n} from '@angular/core';\nimport {Observable, of as observableOf, Subject, Subscription} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {DOCUMENT} from '@angular/common';\nimport {InputModalityDetector, TOUCH_BUFFER_MS} from '../input-modality/input-modality-detector';\n\nexport type FocusOrigin = 'touch' | 'mouse' | 'keyboard' | 'program' | null;\n\n/**\n * Corresponds to the options that can be passed to the native `focus` event.\n * via https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/focus\n */\nexport interface FocusOptions {\n  /** Whether the browser should scroll to the element when it is focused. */\n  preventScroll?: boolean;\n}\n\n/** Detection mode used for attributing the origin of a focus event. */\nexport enum FocusMonitorDetectionMode {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  IMMEDIATE,\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  EVENTUAL,\n}\n\n/** Injectable service-level options for FocusMonitor. */\nexport interface FocusMonitorOptions {\n  detectionMode?: FocusMonitorDetectionMode;\n}\n\n/** InjectionToken for FocusMonitorOptions. */\nexport const FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken<FocusMonitorOptions>(\n  'cdk-focus-monitor-default-options',\n);\n\ntype MonitoredElementInfo = {\n  checkChildren: boolean;\n  readonly subject: Subject<FocusOrigin>;\n  rootNode: HTMLElement | ShadowRoot | Document;\n};\n\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true,\n});\n\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n@Injectable({providedIn: 'root'})\nexport class FocusMonitor implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private readonly _inputModalityDetector = inject(InputModalityDetector);\n\n  /** The focus origin that the next focus event is a result of. */\n  private _origin: FocusOrigin = null;\n\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  private _lastFocusOrigin: FocusOrigin;\n\n  /** Whether the window has just been focused. */\n  private _windowFocused = false;\n\n  /** The timeout id of the window focus timeout. */\n  private _windowFocusTimeoutId: ReturnType<typeof setTimeout>;\n\n  /** The timeout id of the origin clearing timeout. */\n  private _originTimeoutId: ReturnType<typeof setTimeout>;\n\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  private _originFromTouchInteraction = false;\n\n  /** Map of elements being monitored to their info. */\n  private _elementInfo = new Map<HTMLElement, MonitoredElementInfo>();\n\n  /** The number of elements currently being monitored. */\n  private _monitoredElementCount = 0;\n\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  private _rootNodeFocusListenerCount = new Map<HTMLElement | Document | ShadowRoot, number>();\n\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  private readonly _detectionMode: FocusMonitorDetectionMode;\n\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  private _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => (this._windowFocused = false));\n  };\n\n  /** Used to reference correct document/window */\n  protected _document? = inject(DOCUMENT, {optional: true});\n\n  /** Subject for stopping our InputModalityDetector subscription. */\n  private readonly _stopInputModalityDetector = new Subject<void>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const options = inject<FocusMonitorOptions | null>(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true,\n    });\n\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  private _rootNodeFocusAndBlurListener = (event: Event) => {\n    const target = _getEventTarget<HTMLElement>(event);\n\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event as FocusEvent, element);\n      } else {\n        this._onBlur(event as FocusEvent, element);\n      }\n    }\n  };\n\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: HTMLElement, checkChildren?: boolean): Observable<FocusOrigin>;\n\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: ElementRef<HTMLElement>, checkChildren?: boolean): Observable<FocusOrigin>;\n\n  monitor(\n    element: HTMLElement | ElementRef<HTMLElement>,\n    checkChildren: boolean = false,\n  ): Observable<FocusOrigin> {\n    const nativeElement = coerceElement(element);\n\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return observableOf();\n    }\n\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n\n      return cachedInfo.subject;\n    }\n\n    // Create monitored element info.\n    const info: MonitoredElementInfo = {\n      checkChildren: checkChildren,\n      subject: new Subject<FocusOrigin>(),\n      rootNode,\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n\n    return info.subject;\n  }\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: HTMLElement): void;\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: ElementRef<HTMLElement>): void;\n\n  stopMonitoring(element: HTMLElement | ElementRef<HTMLElement>): void {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n\n    if (elementInfo) {\n      elementInfo.subject.complete();\n\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element Element to focus.\n   * @param origin Focus origin.\n   * @param options Options that can be used to configure the focus behavior.\n   */\n  focusVia(element: HTMLElement, origin: FocusOrigin, options?: FocusOptions): void;\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element Element to focus.\n   * @param origin Focus origin.\n   * @param options Options that can be used to configure the focus behavior.\n   */\n  focusVia(element: ElementRef<HTMLElement>, origin: FocusOrigin, options?: FocusOptions): void;\n\n  focusVia(\n    element: HTMLElement | ElementRef<HTMLElement>,\n    origin: FocusOrigin,\n    options?: FocusOptions,\n  ): void {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) =>\n        this._originChanged(currentElement, origin, info),\n      );\n    } else {\n      this._setOrigin(origin);\n\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n  /** Access injected document if available or fallback to global document reference */\n  private _getDocument(): Document {\n    return this._document || document;\n  }\n\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  private _getWindow(): Window {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n\n  private _getFocusOrigin(focusEventTarget: HTMLElement | null): FocusOrigin {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n\n    return 'program';\n  }\n\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  private _shouldBeAttributedToTouch(focusEventTarget: HTMLElement | null): boolean {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return (\n      this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n      !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget)\n    );\n  }\n\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  private _setClasses(element: HTMLElement, origin?: FocusOrigin): void {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  private _setOrigin(origin: FocusOrigin, isFromInteraction = false): void {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n      }\n    });\n  }\n\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  private _onFocus(event: FocusEvent, element: HTMLElement) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget<HTMLElement>(event);\n    if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n      return;\n    }\n\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event: FocusEvent, element: HTMLElement) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (\n      !elementInfo ||\n      (elementInfo.checkChildren &&\n        event.relatedTarget instanceof Node &&\n        element.contains(event.relatedTarget))\n    ) {\n      return;\n    }\n\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n\n  private _emitOrigin(info: MonitoredElementInfo, origin: FocusOrigin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n\n  private _registerGlobalListeners(elementInfo: MonitoredElementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener(\n          'focus',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        rootNode.addEventListener(\n          'blur',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n      });\n    }\n\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected\n        .pipe(takeUntil(this._stopInputModalityDetector))\n        .subscribe(modality => {\n          this._setOrigin(modality, true /* isFromInteraction */);\n        });\n    }\n  }\n\n  private _removeGlobalListeners(elementInfo: MonitoredElementInfo) {\n    const rootNode = elementInfo.rootNode;\n\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode)!;\n\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener(\n          'focus',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        rootNode.removeEventListener(\n          'blur',\n          this._rootNodeFocusAndBlurListener,\n          captureEventListenerOptions,\n        );\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n\n    // Unregister global listeners when last element is unmonitored.\n    if (!--this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n\n  /** Updates all the state on an element once its focus origin has changed. */\n  private _originChanged(\n    element: HTMLElement,\n    origin: FocusOrigin,\n    elementInfo: MonitoredElementInfo,\n  ) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  private _getClosestElementsInfo(element: HTMLElement): [HTMLElement, MonitoredElementInfo][] {\n    const results: [HTMLElement, MonitoredElementInfo][] = [];\n\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n        results.push([currentElement, info]);\n      }\n    });\n\n    return results;\n  }\n\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  private _isLastInteractionFromInputLabel(focusEventTarget: HTMLElement): boolean {\n    const {_mostRecentTarget: mostRecentTarget, mostRecentModality} = this._inputModalityDetector;\n\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (\n      mostRecentModality !== 'mouse' ||\n      !mostRecentTarget ||\n      mostRecentTarget === focusEventTarget ||\n      (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n      (focusEventTarget as HTMLInputElement | HTMLTextAreaElement).disabled\n    ) {\n      return false;\n    }\n\n    const labels = (focusEventTarget as HTMLInputElement | HTMLTextAreaElement).labels;\n\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n}\n\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n@Directive({\n  selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n  exportAs: 'cdkMonitorFocus',\n})\nexport class CdkMonitorFocus implements AfterViewInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _focusMonitor = inject(FocusMonitor);\n\n  private _monitorSubscription: Subscription;\n  private _focusOrigin: FocusOrigin = null;\n\n  @Output() readonly cdkFocusChange = new EventEmitter<FocusOrigin>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  get focusOrigin(): FocusOrigin {\n    return this._focusOrigin;\n  }\n\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor\n      .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n      .subscribe(origin => {\n        this._focusOrigin = origin;\n        this.cdkFocusChange.emit(origin);\n      });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, OnDestroy} from '@angular/core';\nimport {BreakpointObserver} from '@angular/cdk/layout';\nimport {Platform} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {Subscription} from 'rxjs';\n\n/** Set of possible high-contrast mode backgrounds. */\nexport enum HighContrastMode {\n  NONE,\n  BLACK_ON_WHITE,\n  WHITE_ON_BLACK,\n}\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nexport const BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nexport const WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n\n/** CSS class applied to the document body when in high-contrast mode. */\nexport const HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\n@Injectable({providedIn: 'root'})\nexport class HighContrastModeDetector implements OnDestroy {\n  private _platform = inject(Platform);\n\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  private _hasCheckedHighContrastMode: boolean;\n  private _document = inject(DOCUMENT);\n  private _breakpointSubscription: Subscription;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver)\n      .observe('(forced-colors: active)')\n      .subscribe(() => {\n        if (this._hasCheckedHighContrastMode) {\n          this._hasCheckedHighContrastMode = false;\n          this._applyBodyHighContrastModeCssClasses();\n        }\n      });\n  }\n\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode(): HighContrastMode {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle =\n      documentWindow && documentWindow.getComputedStyle\n        ? documentWindow.getComputedStyle(testElement)\n        : null;\n    const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(\n      / /g,\n      '',\n    );\n    testElement.remove();\n\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n\n  ngOnDestroy(): void {\n    this._breakpointSubscription.unsubscribe();\n  }\n\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses(): void {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(\n        HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS,\n        BLACK_ON_WHITE_CSS_CLASS,\n        WHITE_ON_BLACK_CSS_CLASS,\n      );\n      this._hasCheckedHighContrastMode = true;\n\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ObserversModule} from '@angular/cdk/observers';\nimport {NgModule, inject} from '@angular/core';\nimport {CdkMonitorFocus} from './focus-monitor/focus-monitor';\nimport {CdkTrapFocus} from './focus-trap/focus-trap';\nimport {HighContrastModeDetector} from './high-contrast-mode/high-contrast-mode-detector';\nimport {CdkAriaLive} from './live-announcer/live-announcer';\n\n@NgModule({\n  imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n  exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n})\nexport class A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {APP_ID, inject, Injectable} from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters: Record<string, number> = {};\n\n/** Service that generates unique IDs for DOM nodes. */\n@Injectable({providedIn: 'root'})\nexport class _IdGenerator {\n  private _appId = inject(APP_ID);\n\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix: string): string {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n\n    return `${prefix}${counters[prefix]++}`;\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;AAQA;AACA,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB;;;AAGG;SACa,mBAAmB,CAAC,EAAW,EAAE,IAAsB,EAAE,EAAU,EAAA;IACjF,MAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC1C,IAAA,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACf,IAAA,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;QACpD,OAAO;KACR;AACD,IAAA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAEb,IAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;;;AAGG;SACa,sBAAsB,CAAC,EAAW,EAAE,IAAsB,EAAE,EAAU,EAAA;IACpF,MAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC1C,IAAA,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACf,IAAA,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC;AAElD,IAAA,IAAI,WAAW,CAAC,MAAM,EAAE;AACtB,QAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KACvD;SAAM;AACL,QAAA,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KAC1B;AACH,CAAC;AAED;;;AAGG;AACa,SAAA,mBAAmB,CAAC,EAAW,EAAE,IAAY,EAAA;;IAE3D,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACxC,OAAO,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACxC;;ACxBA;;;;AAIG;AACI,MAAM,qBAAqB,GAAG,oCAAoC;AAEzE;;;;AAIG;AACI,MAAM,yBAAyB,GAAG,0BAA0B;AAEnE;;;;AAIG;AACI,MAAM,8BAA8B,GAAG,uBAAuB;AAErE;AACA,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf;;;;AAIG;MAEU,aAAa,CAAA;AAChB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;;AAG7B,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAuC,CAAC;;IAGlE,kBAAkB,GAAuB,IAAI,CAAC;;AAGrC,IAAA,GAAG,GAAG,CAAA,EAAG,MAAM,EAAE,EAAE,CAAC;AAIrC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,EAAE,CAAC;KAC5C;AAcD,IAAA,QAAQ,CAAC,WAAoB,EAAE,OAA6B,EAAE,IAAa,EAAA;QACzE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO;SACR;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAElC,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;;AAE/B,YAAA,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,EAAC,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC,CAAC;SAC9E;aAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;AACxD,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;SAC7C;KACF;AAQD,IAAA,iBAAiB,CAAC,WAAoB,EAAE,OAA6B,EAAE,IAAa,EAAA;QAClF,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;YACjD,OAAO;SACR;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;AACvD,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;SAChD;;;AAID,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACzD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,KAAK,CAAC,EAAE;AAC/D,gBAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;aACjC;SACF;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAChC;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACvD,CAAI,CAAA,EAAA,8BAA8B,KAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAI,CACpD,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;SACtE;AAED,QAAA,IAAI,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/B;AAED;;;AAGG;IACK,qBAAqB,CAAC,OAAe,EAAE,IAAa,EAAA;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,QAAA,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,QAAA,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;QAErC,IAAI,IAAI,EAAE;AACR,YAAA,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,kBAAmB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAC,cAAc,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC,CAAC;KACvF;;AAGO,IAAA,qBAAqB,CAAC,GAAqB,EAAA;AACjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;AACzD,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACnC;;IAGO,wBAAwB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO;SACR;QAED,MAAM,kBAAkB,GAAG,mCAAmC,CAAC;AAC/D,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACtD,CAAI,CAAA,EAAA,kBAAkB,CAAqB,mBAAA,CAAA,CAC5C,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;;;;AAKhD,YAAA,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC9B;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;;;;AAM9D,QAAA,iBAAiB,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;;;AAG9C,QAAA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACpD,QAAA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAEvD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,iBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;KAC7C;;AAGO,IAAA,iCAAiC,CAAC,OAAgB,EAAA;;QAExD,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC,MAAM,CAClF,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CACjD,CAAC;AACF,QAAA,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1E;AAED;;;AAGG;IACK,oBAAoB,CAAC,OAAgB,EAAE,GAAqB,EAAA;QAClE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;;;QAI1D,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACtF,OAAO,CAAC,YAAY,CAAC,8BAA8B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,iBAAiB,CAAC,cAAc,EAAE,CAAC;KACpC;AAED;;;AAGG;IACK,uBAAuB,CAAC,OAAgB,EAAE,GAAqB,EAAA;QACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;QAC1D,iBAAiB,CAAC,cAAc,EAAE,CAAC;QAEnC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AACzF,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;KACzD;;IAGO,4BAA4B,CAAC,OAAgB,EAAE,GAAqB,EAAA;QAC1E,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;AAE3E,QAAA,OAAO,CAAC,CAAC,SAAS,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;KAC7D;;IAGO,eAAe,CAAC,OAAgB,EAAE,OAAoC,EAAA;QAC5E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACjC,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;;;;AAI1C,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,MAAM,cAAc,GAAG,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,CAAG,EAAA,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;QAClE,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;;;AAIrD,QAAA,OAAO,cAAc,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,cAAc,GAAG,KAAK,CAAC;KACnF;;AAGO,IAAA,cAAc,CAAC,OAAa,EAAA;QAClC,OAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;KACzD;uGAvOU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cADD,MAAM,EAAA,CAAA,CAAA;;2FAClB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA2OhC;AACA,SAAS,MAAM,CAAC,OAAyB,EAAE,IAAa,EAAA;AACtD,IAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,GAAG,CAAG,EAAA,IAAI,IAAI,EAAE,IAAI,OAAO,CAAA,CAAE,GAAG,OAAO,CAAC;AAC5E,CAAC;AAED;AACA,SAAS,YAAY,CAAC,OAAoB,EAAE,SAAiB,EAAA;AAC3D,IAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;QACf,OAAO,CAAC,EAAE,GAAG,CAAG,EAAA,yBAAyB,CAAI,CAAA,EAAA,SAAS,CAAI,CAAA,EAAA,MAAM,EAAE,CAAA,CAAE,CAAC;KACtE;AACH;;AChSA,MAAM,sCAAsC,GAAG,GAAG,CAAC;AAWnD;;;AAGG;MACU,SAAS,CAAA;AACH,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAAU,CAAC;IAClD,MAAM,GAAiB,EAAE,CAAC;IAC1B,kBAAkB,GAAG,CAAC,CAAC,CAAC;;IAGxB,eAAe,GAAa,EAAE,CAAC;AAE/B,IAAA,gBAAgB,CAAoC;AAE3C,IAAA,aAAa,GAAG,IAAI,OAAO,EAAK,CAAC;AACzC,IAAA,YAAY,GAAkB,IAAI,CAAC,aAAa,CAAC;IAE1D,WAAY,CAAA,YAA0B,EAAE,MAA2B,EAAA;AACjE,QAAA,MAAM,iBAAiB,GACrB,OAAO,MAAM,EAAE,gBAAgB,KAAK,QAAQ;cACxC,MAAM,CAAC,gBAAgB;cACvB,sCAAsC,CAAC;AAE7C,QAAA,IAAI,MAAM,EAAE,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa,CAAC;SAC9C;AAED,QAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC9C,YAAA,YAAY,CAAC,MAAM;AACnB,YAAA,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,EAC9D;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;SAC7F;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;KAC1C;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC/B;AAED,IAAA,2BAA2B,CAAC,KAAa,EAAA;AACvC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KACjC;AAED,IAAA,QAAQ,CAAC,KAAmB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;;;AAI9B,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;SAC3D;aAAM,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;SAC1D;KACF;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;KACxC;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;KAC3B;AAEO,IAAA,gBAAgB,CAAC,iBAAyB,EAAA;;;;AAIhD,QAAA,IAAI,CAAC,gBAAgB;aAClB,IAAI,CACH,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAChD,YAAY,CAAC,iBAAiB,CAAC,EAC/B,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7C,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAC7D;aACA,SAAS,CAAC,WAAW,IAAG;;;AAGvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,gBAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBACjE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEhC,gBAAA,IACE,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC9B,oBAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,EACvE;AACA,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC9B,MAAM;iBACP;aACF;AAED,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC5B,SAAC,CAAC,CAAC;KACN;AACF;;AC5FD;;;AAGG;MACU,cAAc,CAAA;AAuBf,IAAA,MAAA,CAAA;IAtBF,gBAAgB,GAAG,CAAC,CAAC,CAAC;AACtB,IAAA,WAAW,GAAG,MAAM,CAAW,IAAI,CAAC,CAAC;IACrC,KAAK,GAAG,KAAK,CAAC;AACd,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC;AAC5C,IAAA,wBAAwB,CAAgB;IACxC,SAAS,GAAG,IAAI,CAAC;AACjB,IAAA,WAAW,CAAuB;IAClC,oBAAoB,GAAgC,EAAE,CAAC;IACvD,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC;AAC7C,IAAA,UAAU,CAAwB;AAClC,IAAA,UAAU,CAAgB;AAElC;;;AAGG;IACK,gBAAgB,GAAG,CAAC,IAAO,KAAK,IAAI,CAAC,QAAQ,CAAC;IAItD,WACU,CAAA,MAA8E,EACtF,QAAmB,EAAA;QADX,IAAM,CAAA,MAAA,GAAN,MAAM,CAAwE;;;;AAMtF,QAAA,IAAI,MAAM,YAAY,SAAS,EAAE;YAC/B,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAsB,KAC9E,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CACvC,CAAC;SACH;AAAM,aAAA,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAChE,gBAAA,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;aACtF;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAC,QAAQ,EAAC,CAAC,CAAC;SAC1E;KACF;AAED;;;AAGG;AACM,IAAA,MAAM,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAG7B,IAAA,MAAM,GAAG,IAAI,OAAO,EAAU,CAAC;AAExC;;;;AAIG;AACH,IAAA,aAAa,CAAC,SAA+B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;AAClC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,QAAQ,CAAC,UAAU,GAAG,IAAI,EAAA;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,uBAAuB,CAAC,UAAmB,IAAI,EAAA;AAC7C,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AACzB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,yBAAyB,CAAC,SAA+B,EAAA;AACvD,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC7B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACH,IAAA,uBAAuB,CAAC,IAAiC,EAAA;AACvD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,aAAa,CAAC,mBAA2B,GAAG,EAAA;AAC1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;AAC/E,gBAAA,MAAM,KAAK,CAAC,8EAA8E,CAAC,CAAC;aAC7F;SACF;AAED,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAE1C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE;AACrC,YAAA,gBAAgB,EAAE,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,SAAS;YACrF,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnD,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG;AAC1E,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC3B,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AACzB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,cAAc,CAAC,UAAmB,IAAI,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,OAAA,GAAmB,IAAI,EAAE,QAAgB,EAAE,EAAA;QACxD,IAAI,CAAC,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC,CAAC;AACvC,QAAA,OAAO,IAAI,CAAC;KACb;AAcD,IAAA,aAAa,CAAC,IAAS,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAE9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAE5B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,kBAAkB,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACzC;KACF;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,MAAM,SAAS,GAAgC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAC5F,MAAM,iBAAiB,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,IAAG;AACnD,YAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9E,SAAC,CAAC,CAAC;QAEH,QAAQ,OAAO;AACb,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO;AAET,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,EAAE;oBACvC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,EAAE;oBACvC,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACzC,oBAAA,IAAI,CAAC,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACrF,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACzC,oBAAA,IAAI,CAAC,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBACrF,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,IAAI;AACP,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;oBACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;oBACzC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,iBAAiB,EAAE;oBACpD,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AACtE,oBAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjE,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,iBAAiB,EAAE;oBACpD,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;oBACtE,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;oBACjD,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC1F,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;AAEH,YAAA;gBACE,IAAI,iBAAiB,IAAI,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;AAC1D,oBAAA,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;iBACnC;;;gBAID,OAAO;SACV;AAED,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;QACzB,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB;;AAGD,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KACxD;;IAGD,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClE;;IAGD,iBAAiB,GAAA;QACf,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;KACvF;;IAGD,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK;AACrC,cAAE,IAAI,CAAC,iBAAiB,EAAE;cACxB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC;AAcD,IAAA,gBAAgB,CAAC,IAAS,EAAA;AACxB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACxC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACxE,QAAA,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;;AAGpC,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,GAAG,IAAI,GAAG,UAAU,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,KAAK,CAAC,CAAC;KACrD;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE,CAAC;AAC7C,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;AAED;;;;AAIG;AACK,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACzC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;KACrF;AAED;;;;AAIG;AACK,IAAA,oBAAoB,CAAC,KAAa,EAAA;AACxC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAEpC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;AAChF,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAChC,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,OAAO;aACR;SACF;KACF;AAED;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,KAAa,EAAA;QAC3C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,EAAE,KAAK,CAAC,CAAC;KAClE;AAED;;;;AAIG;IACK,qBAAqB,CAAC,KAAa,EAAE,aAAqB,EAAA;AAChE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAEpC,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,OAAO;SACR;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,KAAK,IAAI,aAAa,CAAC;AAEvB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjB,OAAO;aACR;SACF;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC3B;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;SACtB;QAED,OAAO,IAAI,CAAC,MAAM,YAAY,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;KAC/E;;AAGO,IAAA,aAAa,CAAC,QAA4B,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,UAAU,EAAE;YACd,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE9C,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACvD,gBAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AACjC,gBAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,QAAQ,CAAC,CAAC;aACxD;SACF;KACF;AACF;;ACzbK,MAAO,0BAA8B,SAAQ,cAAiC,CAAA;AAiBzE,IAAA,aAAa,CAAC,KAAU,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;SACrC;AACD,QAAA,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;SACnC;KACF;AACF;;AC5BK,MAAO,eAAmB,SAAQ,cAAmC,CAAA;IACjE,OAAO,GAAgB,SAAS,CAAC;AAEzC;;;AAGG;AACH,IAAA,cAAc,CAAC,MAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,OAAO,IAAI,CAAC;KACb;AAeQ,IAAA,aAAa,CAAC,IAAS,EAAA;AAC9B,QAAA,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrC;KACF;AACF;;ACjCD;;;;AAIG;MACU,cAAc,CAAA;;IAEjB,gBAAgB,GAAG,CAAC,CAAC,CAAC;;IAEtB,WAAW,GAAa,IAAI,CAAC;;IAE7B,4BAA4B,GAAG,KAAK,CAAC;AAC7C;;;AAGG;IACK,sBAAsB,GAAkB,KAAK,CAAC;AAEtD;;;;;;;AAOG;AACK,IAAA,gBAAgB,GAAG,CAAC,KAAQ,KAAK,KAAK,CAAC;;AAGvC,IAAA,UAAU,GAAyB,CAAC,IAAO,KAAK,IAAI,CAAC;;IAGrD,MAAM,GAAQ,EAAE,CAAC;AAEjB,IAAA,UAAU,CAAgB;AAC1B,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC;IAE5C,kBAAkB,GAAG,KAAK,CAAC;IAE3B,gBAAgB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvD,OAAO;SACR;QAED,IAAI,WAAW,GAAG,CAAC,CAAC;AACpB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnF,WAAW,GAAG,CAAC,CAAC;gBAChB,MAAM;aACP;SACF;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;;;AAI5C,QAAA,IAAI,UAAU,CAAC,aAAa,EAAE;AAC5B,YAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;AACpC,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,YAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,WAAW,CAAC,CAAC;YAC1D,UAAU,CAAC,aAAa,EAAE,CAAC;SAC5B;aAAM;;AAEL,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SAC7B;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAChC;AAED;;;;;;;AAOG;IACH,WAAY,CAAA,KAA2C,EAAE,MAAgC,EAAA;;;;AAIvF,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YAC9B,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAsB,KAAI;AACjD,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;SACJ;AAAM,aAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC9B,YAAA,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAG;AACzB,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvB,gBAAA,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;AAED,QAAA,IAAI,OAAO,MAAM,CAAC,2BAA2B,KAAK,SAAS,EAAE;AAC3D,YAAA,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,2BAA2B,CAAC;SACxE;AACD,QAAA,IAAI,MAAM,CAAC,qBAAqB,EAAE;AAChC,YAAA,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC;SAC5D;AACD,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa,CAAC;SAC9C;AACD,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;SAClC;AACD,QAAA,IAAI,OAAO,MAAM,CAAC,yBAAyB,KAAK,WAAW,EAAE;AAC3D,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;SACtD;KACF;;AAGQ,IAAA,MAAM,GAAG,IAAI,OAAO,EAAY,CAAC;;IAG1C,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QAEtB,QAAQ,GAAG;AACT,YAAA,KAAK,KAAK;;gBAER,OAAO;AAET,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;AAER,YAAA,KAAK,SAAS;gBACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;AAER,YAAA,KAAK,YAAY;gBACf,IAAI,CAAC,sBAAsB,KAAK,KAAK;AACnC,sBAAE,IAAI,CAAC,oBAAoB,EAAE;AAC7B,sBAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,MAAM;AAER,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,sBAAsB,KAAK,KAAK;AACnC,sBAAE,IAAI,CAAC,kBAAkB,EAAE;AAC3B,sBAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChC,MAAM;AAER,YAAA,KAAK,MAAM;gBACT,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;AAER,YAAA,KAAK,KAAK;gBACR,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;AAER,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,GAAG;gBACN,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,MAAM;AAER,YAAA;AACE,gBAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;oBACrB,IAAI,CAAC,iCAAiC,EAAE,CAAC;oBACzC,MAAM;iBACP;AAED,gBAAA,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;;;gBAGlC,OAAO;SACV;;AAGD,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;QACzB,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB;;IAGD,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;;IAGD,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;;IAGO,eAAe,GAAA;QACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtD;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;KAC1E;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;KACzE;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;KAC7E;AAUD,IAAA,SAAS,CAAC,WAAuB,EAAE,OAAA,GAAuC,EAAE,EAAA;;AAE1E,QAAA,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC;AAEjC,QAAA,IAAI,KAAK,GACP,OAAO,WAAW,KAAK,QAAQ;AAC7B,cAAE,WAAW;cACX,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5F,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5C,OAAO;SACR;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;AAGtC,QAAA,IACE,IAAI,CAAC,WAAW,KAAK,IAAI;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EACjE;YACA,OAAO;SACR;AAED,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC;AAC5C,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,KAAK,CAAC,CAAC;AAEpD,QAAA,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;QAC1B,kBAAkB,EAAE,OAAO,EAAE,CAAC;AAE9B,QAAA,IAAI,OAAO,CAAC,eAAe,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;AAED,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;KACF;AAEO,IAAA,sBAAsB,CAAC,QAAa,EAAA;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE;YACf,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CACjC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAC9D,CAAC;QAEF,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACvD,YAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AACjC,YAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,QAAQ,CAAC,CAAC;SACxD;KACF;AAEO,IAAA,aAAa,CAAC,gBAAkC,EAAA;QACtD,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;AAC3C,YAAA,gBAAgB,EAAE,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,SAAS;YACrF,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnD,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG;AAC1E,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,2BAA2B,CAAC,aAAqB,EAAA;AACvD,QAAA,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3D,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,CAAC,CAAC;aACV;SACF;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;AAEO,IAAA,+BAA+B,CAAC,aAAqB,EAAA;AAC3D,QAAA,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3C,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,CAAC,CAAC;aACV;SACF;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;AAED;;AAEG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACR;AAED,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;SAC7B;aAAM;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAW,CAAC,EAAE;gBACjD,OAAO;aACR;AACD,YAAA,IAAI,CAAC,SAAS,CAAC,MAAW,CAAC,CAAC;SAC7B;KACF;AAED;;AAEG;IACK,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACR;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC3B;aAAM;AACL,YAAA,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC7C,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACb,SAAS,CAAC,QAAQ,IAAG;AACpB,gBAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAU,CAAC,CAAC,CAAC;gBAC9E,IAAI,CAAC,UAAU,EAAE;oBACf,OAAO;iBACR;AACD,gBAAA,IAAI,CAAC,SAAS,CAAC,UAAe,CAAC,CAAC;AAClC,aAAC,CAAC,CAAC;SACN;KACF;IAEO,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK,SAAS;AACrD,cAAE,IAAI,CAAC,WAAW,CAAC,UAAU;AAC7B,cAAE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;KACnC;AAEO,IAAA,eAAe,CAAC,IAAwB,EAAA;QAC9C,OAAO,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;KACrF;;IAGO,iCAAiC,GAAA;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;AAC5C,QAAA,IAAI,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,EAAE;YACX,aAAa,GAAGA,EAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;SACrF;aAAM;YACL,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SACxD;AAED,QAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC5C,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;AACH,SAAC,CAAC,CAAC;KACJ;IAEO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC;KAC9B;AACF,CAAA;AAED;SACgB,wBAAwB,GAAA;AACtC,IAAA,OAAO,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED;MACa,gBAAgB,GAAG,IAAI,cAAc,CAA6B,kBAAkB,EAAE;AACjG,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,wBAAwB;AAClC,CAAA,EAAE;AAEH;AACa,MAAA,iCAAiC,GAAG;AAC/C,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,UAAU,EAAE,wBAAwB;;;AC1ZtC;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAcG;MACU,kBAAkB,CAAA;IACpB,qBAAqB,GAAG,IAAI,CAAC;;;AAI7B,IAAA,MAAM,GAAG,IAAI,OAAO,EAAY,CAAC;IAE1C,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;IAED,SAAS,GAAA;;KAER;IAED,kBAAkB,GAAA;;;AAGhB,QAAA,OAAO,IAAI,CAAC;KACb;IAED,aAAa,GAAA;;;AAGX,QAAA,OAAO,IAAI,CAAC;KACb;IAED,SAAS,GAAA;;KAER;AACF,CAAA;AAED;;;;;;;;;;;;;;AAcG;SACa,6BAA6B,GAAA;AAG3C,IAAA,OAAO,MAAM,IAAI,kBAAkB,EAAK,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACU,MAAA,sCAAsC,GAAa;AAC9D,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,UAAU,EAAE,6BAA6B;;;AChG3C;;AAEG;MACU,iBAAiB,CAAA;AAC5B;;AAEG;IACH,gBAAgB,GAAY,KAAK,CAAC;AACnC,CAAA;AAED;AACA;AACA;AAEA;;;AAGG;MAEU,oBAAoB,CAAA;AACvB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAGrC,IAAA,WAAA,GAAA,GAAgB;AAEhB;;;;;AAKG;AACH,IAAA,UAAU,CAAC,OAAoB,EAAA;;;AAG7B,QAAA,OAAO,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;KACzC;AAED;;;;;;;AAOG;AACH,IAAA,SAAS,CAAC,OAAoB,EAAA;AAC5B,QAAA,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC;KACnF;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CAAC,OAAoB,EAAA;;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,OAAO,KAAK,CAAC;SACd;QAED,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAEzD,IAAI,YAAY,EAAE;;YAEhB,IAAI,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;AACzC,gBAAA,OAAO,KAAK,CAAC;aACd;;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;AACjC,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;QAED,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC9C,QAAA,IAAI,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAE9C,QAAA,IAAI,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;AAC3C,YAAA,OAAO,aAAa,KAAK,CAAC,CAAC,CAAC;SAC7B;QAED,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;;AAIlD,YAAA,OAAO,KAAK,CAAC;SACd;;AAGD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;AACrF,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;;;YAGxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AACrC,gBAAA,OAAO,KAAK,CAAC;aACd;;;AAGD,YAAA,OAAO,aAAa,KAAK,CAAC,CAAC,CAAC;SAC7B;AAED,QAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;;;;;AAKxB,YAAA,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACxB,gBAAA,OAAO,KAAK,CAAC;aACd;;;AAGD,YAAA,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,gBAAA,OAAO,IAAI,CAAC;aACb;;;;AAID,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACnE;AAED,QAAA,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC9B;AAED;;;;;;AAMG;IACH,WAAW,CAAC,OAAoB,EAAE,MAA0B,EAAA;;;AAG1D,QAAA,QACE,sBAAsB,CAAC,OAAO,CAAC;AAC/B,YAAA,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACzB,aAAC,MAAM,EAAE,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EACrD;KACH;uGA5HU,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAApB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cADR,MAAM,EAAA,CAAA,CAAA;;2FAClB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAgIhC;;;;AAIG;AACH,SAAS,eAAe,CAAC,MAAc,EAAA;AACrC,IAAA,IAAI;QACF,OAAO,MAAM,CAAC,YAA2B,CAAC;KAC3C;AAAC,IAAA,MAAM;AACN,QAAA,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED;AACA,SAAS,WAAW,CAAC,OAAoB,EAAA;;;AAGvC,IAAA,OAAO,CAAC,EACN,OAAO,CAAC,WAAW;AACnB,QAAA,OAAO,CAAC,YAAY;AACpB,SAAC,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAClF,CAAC;AACJ,CAAC;AAED;AACA,SAAS,mBAAmB,CAAC,OAAa,EAAA;IACxC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,QACE,QAAQ,KAAK,OAAO;AACpB,QAAA,QAAQ,KAAK,QAAQ;AACrB,QAAA,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU,EACvB;AACJ,CAAC;AAED;AACA,SAAS,aAAa,CAAC,OAAoB,EAAA;IACzC,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC;AAC7D,CAAC;AAED;AACA,SAAS,gBAAgB,CAAC,OAAoB,EAAA;IAC5C,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAClE,CAAC;AAED;AACA,SAAS,cAAc,CAAC,OAAoB,EAAA;IAC1C,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,OAAO,CAAC;AACnD,CAAC;AAED;AACA,SAAS,eAAe,CAAC,OAAoB,EAAA;IAC3C,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;AAC/C,CAAC;AAED;AACA,SAAS,gBAAgB,CAAC,OAAoB,EAAA;AAC5C,IAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;AACvE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AAChD,IAAA,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC;AAED;;;AAGG;AACH,SAAS,gBAAgB,CAAC,OAAoB,EAAA;AAC5C,IAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAEtE,IAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;AACzC,CAAC;AAED;AACA,SAAS,wBAAwB,CAAC,OAAoB,EAAA;IACpD,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,IAAI,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAK,OAA4B,CAAC,IAAI,CAAC;IAE3E,QACE,SAAS,KAAK,MAAM;AACpB,QAAA,SAAS,KAAK,UAAU;AACxB,QAAA,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU,EACvB;AACJ,CAAC;AAED;;;AAGG;AACH,SAAS,sBAAsB,CAAC,OAAoB,EAAA;;AAElD,IAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,QACE,mBAAmB,CAAC,OAAO,CAAC;QAC5B,gBAAgB,CAAC,OAAO,CAAC;AACzB,QAAA,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC;AACvC,QAAA,gBAAgB,CAAC,OAAO,CAAC,EACzB;AACJ,CAAC;AAED;AACA,SAAS,SAAS,CAAC,IAAiB,EAAA;;AAElC,IAAA,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,MAAM,CAAC;AAC1E;;ACnPA;;;;;;AAMG;MACU,SAAS,CAAA;AAwBT,IAAA,QAAA,CAAA;AACD,IAAA,QAAA,CAAA;AACC,IAAA,OAAA,CAAA;AACA,IAAA,SAAA,CAAA;AAGA,IAAA,SAAA,CAAA;AA7BH,IAAA,YAAY,CAAqB;AACjC,IAAA,UAAU,CAAqB;IAC/B,YAAY,GAAG,KAAK,CAAC;;IAGnB,mBAAmB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAC5D,iBAAiB,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;;AAGrE,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SACpD;KACF;IACS,QAAQ,GAAY,IAAI,CAAC;IAEnC,WACW,CAAA,QAAqB,EACtB,QAA8B,EAC7B,OAAe,EACf,SAAmB,EAC5B,YAAY,GAAG,KAAK;;IAEX,SAAoB,EAAA;QANpB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAa;QACtB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAGnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAE7B,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;;IAGD,OAAO,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;AACtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAElC,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnE,WAAW,CAAC,MAAM,EAAE,CAAC;SACtB;QAED,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/D,SAAS,CAAC,MAAM,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3C,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;KAC3B;AAED;;;;;AAKG;IACH,aAAa,GAAA;;AAEX,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACzC,IAAI,CAAC,YAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACxE;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvC,IAAI,CAAC,UAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACpE;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAC5B,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzE,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAED;;;;AAIG;AACH,IAAA,4BAA4B,CAAC,OAAsB,EAAA;AACjD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1E,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,kCAAkC,CAAC,OAAsB,EAAA;AACvD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAChF,SAAC,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,iCAAiC,CAAC,OAAsB,EAAA;AACtD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/E,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;AACK,IAAA,kBAAkB,CAAC,KAAsB,EAAA;;QAE/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC5C,CAAqB,kBAAA,EAAA,KAAK,KAAK,GAAG,CAAA,eAAA,EAAkB,KAAK,CAAK,GAAA,CAAA,GAAG,cAAc,KAAK,CAAA,CAAA,CAAG,CAC7D,CAAC;AAE7B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEvC,gBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA,UAAA,EAAa,KAAK,CAAA,CAAE,CAAC,EAAE;AACjD,oBAAA,OAAO,CAAC,IAAI,CACV,CAAA,6CAAA,EAAgD,KAAK,CAAK,GAAA,CAAA;AACxD,wBAAA,CAAA,mBAAA,EAAsB,KAAK,CAA4B,0BAAA,CAAA;AACvD,wBAAA,CAAA,mCAAA,CAAqC,EACvC,OAAO,CAAC,CAAC,CAAC,CACX,CAAC;iBACH;AAAM,qBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA,iBAAA,EAAoB,KAAK,CAAA,CAAE,CAAC,EAAE;AAC/D,oBAAA,OAAO,CAAC,IAAI,CACV,CAAA,oDAAA,EAAuD,KAAK,CAAK,GAAA,CAAA;AAC/D,wBAAA,CAAA,mBAAA,EAAsB,KAAK,CAAsC,oCAAA,CAAA;AACjE,wBAAA,CAAA,yBAAA,CAA2B,EAC7B,OAAO,CAAC,CAAC,CAAC,CACX,CAAC;iBACH;aACF;SACF;AAED,QAAA,IAAI,KAAK,IAAI,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnF;QACD,OAAO,OAAO,CAAC,MAAM;cACjB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;cAC3B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACjD;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,OAAsB,EAAA;;AAExC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CACnD,CAAuB,qBAAA,CAAA,GAAG,CAAmB,iBAAA,CAAA,CAC/B,CAAC;QAEjB,IAAI,iBAAiB,EAAE;;AAErB,YAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC9C,gBAAA,iBAAiB,CAAC,YAAY,CAAC,CAAmB,iBAAA,CAAA,CAAC,EACnD;gBACA,OAAO,CAAC,IAAI,CACV,CAAyD,uDAAA,CAAA;oBACvD,CAA0D,wDAAA,CAAA;oBAC1D,CAA0B,wBAAA,CAAA,EAC5B,iBAAiB,CAClB,CAAC;aACH;;;AAID,YAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC9C,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAC7C;AACA,gBAAA,OAAO,CAAC,IAAI,CAAC,wDAAwD,EAAE,iBAAiB,CAAC,CAAC;aAC3F;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;gBACjD,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAgB,CAAC;AACvF,gBAAA,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC/B,OAAO,CAAC,CAAC,cAAc,CAAC;aACzB;AAED,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjC,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;KAChD;AAED;;;AAGG;AACH,IAAA,yBAAyB,CAAC,OAAsB,EAAA;QAC9C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,iBAAiB,EAAE;AACrB,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAClC;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B;AAED;;;AAGG;AACH,IAAA,wBAAwB,CAAC,OAAsB,EAAA;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,iBAAiB,EAAE;AACrB,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAClC;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B;AAED;;AAEG;IACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;AAGO,IAAA,wBAAwB,CAAC,IAAiB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACrE,YAAA,OAAO,IAAI,CAAC;SACb;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,aAAa,GACjB,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;kBAChD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAgB,CAAC;kBACzD,IAAI,CAAC;YAEX,IAAI,aAAa,EAAE;AACjB,gBAAA,OAAO,aAAa,CAAC;aACtB;SACF;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;AAGO,IAAA,uBAAuB,CAAC,IAAiB,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACrE,YAAA,OAAO,IAAI,CAAC;SACb;;AAGD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE/B,QAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,aAAa,GACjB,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;kBAChD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAgB,CAAC;kBACxD,IAAI,CAAC;YAEX,IAAI,aAAa,EAAE;AACjB,gBAAA,OAAO,aAAa,CAAC;aACtB;SACF;AAED,QAAA,OAAO,IAAI,CAAC;KACb;;IAGO,aAAa,GAAA;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAClD,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAC5C,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAC9C,QAAA,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;AAIG;IACK,qBAAqB,CAAC,SAAkB,EAAE,MAAmB,EAAA;;;QAGnE,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KACvF;AAED;;;AAGG;AACO,IAAA,aAAa,CAAC,OAAgB,EAAA;QACtC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACvD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SACtD;KACF;;AAGO,IAAA,gBAAgB,CAAC,EAAa,EAAA;;AAEpC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,eAAe,CAAC,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC,CAAC;SACjD;aAAM;YACL,UAAU,CAAC,EAAE,CAAC,CAAC;SAChB;KACF;AACF,CAAA;AAED;;AAEG;MAEU,gBAAgB,CAAA;AACnB,IAAA,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAEzB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAGrC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;KAC5D;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,OAAoB,EAAE,oBAAA,GAAgC,KAAK,EAAA;QAChE,OAAO,IAAI,SAAS,CAClB,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,oBAAoB,EACpB,IAAI,CAAC,SAAS,CACf,CAAC;KACH;uGA5BU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAgChC;MAKa,YAAY,CAAA;AACf,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;AAC1D,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;;AAGrD,IAAA,SAAS,CAAY;;IAGb,yBAAyB,GAAuB,IAAI,CAAC;;AAG7D,IAAA,IACI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,KAAK,CAAC;KACzC;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;SAChC;KACF;AAED;;;AAGG;AACqE,IAAA,WAAW,CAAU;AAI7F,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAElC,QAAA,IAAI,QAAQ,CAAC,SAAS,EAAE;AACtB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SACtF;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;;;AAI1B,QAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,YAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACvC;KACF;IAED,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;AAEhC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IAED,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE;AACnD,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;SAChC;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAEjD,QAAA,IACE,iBAAiB;YACjB,CAAC,iBAAiB,CAAC,WAAW;AAC9B,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,EAC7B;YACA,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IAEO,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,yBAAyB,GAAG,iCAAiC,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,SAAS,EAAE,4BAA4B,EAAE,CAAC;KAChD;uGA9EU,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,SAAA,EAWmB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,yBAAA,EAAA,aAAA,EAcL,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAzB1D,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACzB,iBAAA,CAAA;wDAaK,OAAO,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAca,WAAW,EAAA,CAAA;sBAAlF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,yBAAyB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ACnaxE;;;;;AAKG;AACG,MAAO,qBAAsB,SAAQ,SAAS,CAAA;AAmBxC,IAAA,iBAAA,CAAA;AACA,IAAA,cAAA,CAAA;;AAlBV,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAa,OAAO,CAAC,KAAc,EAAA;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACvC;aAAM;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACzC;KACF;AAED,IAAA,WAAA,CACE,QAAqB,EACrB,QAA8B,EAC9B,OAAe,EACf,SAAmB,EACX,iBAAmC,EACnC,cAAsC,EAC9C,MAAmC,EACnC,QAAmB,EAAA;AAEnB,QAAA,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAL9D,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;AAK9C,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACvC;;IAGQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACxC,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC1B;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC3B;AACF;;ACvDD;;;AAGG;MACU,mCAAmC,CAAA;;IAEtC,SAAS,GAAqC,IAAI,CAAC;;AAG3D,IAAA,YAAY,CAAC,SAAgC,EAAA;;AAE3C,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,CAAC;SACzE;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAa,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAClE,QAAA,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACvC,YAAA,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,CAAC;AACvE,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,UAAU,CAAC,SAAgC,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO;SACR;AACD,QAAA,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;AAED;;;;;;AAMG;IACK,UAAU,CAAC,SAAgC,EAAE,KAAiB,EAAA;AACpE,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB,CAAC;AAC3C,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC;;;AAIzC,QAAA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,sBAAsB,CAAC,EAAE;;;;YAI1F,UAAU,CAAC,MAAK;;AAEd,gBAAA,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;oBACnF,SAAS,CAAC,yBAAyB,EAAE,CAAC;iBACvC;AACH,aAAC,CAAC,CAAC;SACJ;KACF;AACF;;ACxDD;MACa,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B;;ACQ7B;MAEa,gBAAgB,CAAA;;;IAGnB,eAAe,GAAuB,EAAE,CAAC;AAEjD;;;AAGG;AACH,IAAA,QAAQ,CAAC,SAA2B,EAAA;;AAElC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC;AAE3E,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;AAEjC,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;SACpC;AAED,QAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtB,SAAS,CAAC,OAAO,EAAE,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,SAA2B,EAAA;QACpC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAErB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;QAEnC,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACZ,YAAA,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,YAAA,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChB,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aACnC;SACF;KACF;uGAvCU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACJhC;MAEa,4BAA4B,CAAA;AAC/B,IAAA,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAE7C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,cAAc,CAAyB;AAE9B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;QAG1E,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,IAAI,mCAAmC,EAAE,CAAC;KAClF;IAgBD,MAAM,CACJ,OAAoB,EACpB,MAAA,GAAgD,EAAC,KAAK,EAAE,KAAK,EAAC,EAAA;AAE9D,QAAA,IAAI,YAAyC,CAAC;AAC9C,QAAA,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;AAC/B,YAAA,YAAY,GAAG,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC;SAChC;aAAM;YACL,YAAY,GAAG,MAAM,CAAC;SACvB;AACD,QAAA,OAAO,IAAI,qBAAqB,CAC9B,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,cAAc,EACnB,YAAY,EACZ,IAAI,CAAC,SAAS,CACf,CAAC;KACH;uGArDU,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAA5B,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,4BAA4B,cADhB,MAAM,EAAA,CAAA,CAAA;;2FAClB,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBADxC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACVhC;AACM,SAAU,+BAA+B,CAAC,KAAiB,EAAA;;;;;;IAM/D,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AACnD,CAAC;AAED;AACM,SAAU,gCAAgC,CAAC,KAAiB,EAAA;IAChE,MAAM,KAAK,GACT,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;IAM3F,QACE,CAAC,CAAC,KAAK;AACP,QAAA,KAAK,CAAC,UAAU,KAAK,CAAC,CAAC;SACtB,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;AAC9C,SAAC,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC,EAC9C;AACJ;;ACIA;;;AAGG;MACU,+BAA+B,GAAG,IAAI,cAAc,CAC/D,qCAAqC,EACrC;AAEF;;;;;;;;;;;;;;;AAeG;AACU,MAAA,uCAAuC,GAAiC;IACnF,UAAU,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;EACjD;AAEF;;;;;;AAMG;AACI,MAAM,eAAe,GAAG,GAAG,CAAC;AAEnC;;;AAGG;AACH,MAAM,4BAA4B,GAAG;AACnC,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;CACd,CAAC;AAEF;;;;;;;;;;;;;AAaG;MAEU,qBAAqB,CAAA;AACf,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,iBAAiB,CAA6B;;AAGtD,IAAA,gBAAgB,CAA4B;;AAG5C,IAAA,eAAe,CAA4B;;AAGpD,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;AAED;;;AAGG;IACH,iBAAiB,GAAuB,IAAI,CAAC;;AAG5B,IAAA,SAAS,GAAG,IAAI,eAAe,CAAgB,IAAI,CAAC,CAAC;;AAGrD,IAAA,QAAQ,CAA+B;AAExD;;;AAGG;IACK,YAAY,GAAG,CAAC,CAAC;AAEzB;;;AAGG;AACK,IAAA,UAAU,GAAG,CAAC,KAAoB,KAAI;;;AAG5C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE;YACzE,OAAO;SACR;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,KAAC,CAAC;AAEF;;;AAGG;AACK,IAAA,YAAY,GAAG,CAAC,KAAiB,KAAI;;;;QAI3C,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,eAAe,EAAE;YACpD,OAAO;SACR;;;AAID,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC,CAAC;AACnF,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,KAAC,CAAC;AAEF;;;AAGG;AACK,IAAA,aAAa,GAAG,CAAC,KAAiB,KAAI;;;AAG5C,QAAA,IAAI,gCAAgC,CAAC,KAAK,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChC,OAAO;SACR;;;AAID,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,KAAC,CAAC;AAIF,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAW,QAAQ,CAAC,CAAC;AAC5C,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,+BAA+B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,QAAQ,GAAG;AACd,YAAA,GAAG,uCAAuC;AAC1C,YAAA,GAAG,OAAO;SACX,CAAC;;AAGF,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;;;AAI1E,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBACrD,OAAO;AACL,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,UAAU,EACf,4BAA4B,CAC7B;AACD,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,IAAI,CAAC,YAAY,EACjB,4BAA4B,CAC7B;AACD,oBAAA,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;iBACF,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC,CAAC;KACvD;uGA1IU,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,cADT,MAAM,EAAA,CAAA,CAAA;;2FAClB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;MCjFnB,4BAA4B,GAAG,IAAI,cAAc,CAC5D,sBAAsB,EACtB;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oCAAoC;AAC9C,CAAA,EACD;AAEF;SACgB,oCAAoC,GAAA;AAClD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAWD;MACa,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC;;ACpBlC,IAAI,SAAS,GAAG,CAAC,CAAC;MAGL,aAAa,CAAA;AAChB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,IAAA,eAAe,GAAG,MAAM,CAA8B,8BAA8B,EAAE;AAC5F,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;AAEK,IAAA,YAAY,CAAc;AAC1B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,gBAAgB,CAAgC;AAChD,IAAA,eAAe,CAA4B;AAC3C,IAAA,eAAe,CAA2B;AAIlD,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,4BAA4B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC/D;AAsCD,IAAA,QAAQ,CAAC,OAAe,EAAE,GAAG,IAAW,EAAA;AACtC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;AAC5C,QAAA,IAAI,UAA0C,CAAC;AAC/C,QAAA,IAAI,QAA4B,CAAC;AAEjC,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACpD,YAAA,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SACpB;aAAM;AACL,YAAA,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,QAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEpC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU;AACR,gBAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC;SACtF;AAED,QAAA,IAAI,QAAQ,IAAI,IAAI,IAAI,cAAc,EAAE;AACtC,YAAA,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;SACpC;;QAGD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAExD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;YACxB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;SACrD;;;;;;AAOD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACzC,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC;aACjF;AAED,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAK;AACtC,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;AAExC,gBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAChC,oBAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;iBAClE;;;AAID,gBAAA,IAAI,CAAC,eAAe,IAAI,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;aACzD,EAAE,GAAG,CAAC,CAAC;YAER,OAAO,IAAI,CAAC,eAAe,CAAC;AAC9B,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACH,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,EAAE,CAAC;SACpC;KACF;IAED,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,eAAe,IAAI,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;KACzD;IAEO,kBAAkB,GAAA;QACxB,MAAM,YAAY,GAAG,4BAA4B,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC7E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;AAGnD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC9B;AAED,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACnC,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAE5C,QAAA,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC3C,QAAA,MAAM,CAAC,EAAE,GAAG,sBAAsB,SAAS,EAAE,EAAE,CAAC;QAEhD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAExC,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;AAIG;AACK,IAAA,wBAAwB,CAAC,EAAU,EAAA;;;;;;;QAOzC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC5C,mDAAmD,CACpD,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjD,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;aACtD;SACF;KACF;uGApLU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cADD,MAAM,EAAA,CAAA,CAAA;;2FAClB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAwLhC;;;AAGG;MAKU,WAAW,CAAA;AACd,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACjC,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AACvC,IAAA,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;AAC3C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;AAGjC,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,KAAyB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,WAAW,GAAG,KAAK,GAAG,QAAQ,CAAC;AAC/E,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;AAC9B,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;AACjC,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;SACF;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACvD,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,MAAK;;oBAEpE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC;;;AAI/D,oBAAA,IAAI,WAAW,KAAK,IAAI,CAAC,sBAAsB,EAAE;AAC/C,wBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3E,wBAAA,IAAI,CAAC,sBAAsB,GAAG,WAAW,CAAC;qBAC3C;AACH,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;KACF;IACO,WAAW,GAAuB,QAAQ,CAAC;;AAGrB,IAAA,QAAQ,CAAS;AAEvC,IAAA,sBAAsB,CAAU;AAChC,IAAA,aAAa,CAAsB;AAI3C,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;KAC5D;IAED,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;SAClC;KACF;uGApDU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,aAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,EAAA,UAAA,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACxB,iBAAA,CAAA;wDASK,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,aAAa,CAAA;gBA8BU,QAAQ,EAAA,CAAA;sBAArC,KAAK;uBAAC,qBAAqB,CAAA;;;AChN9B;IACY,0BAYX;AAZD,CAAA,UAAY,yBAAyB,EAAA;AACnC;;;;AAIG;AACH,IAAA,yBAAA,CAAA,yBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT;;;AAGG;AACH,IAAA,yBAAA,CAAA,yBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EAZW,yBAAyB,KAAzB,yBAAyB,GAYpC,EAAA,CAAA,CAAA,CAAA;AAOD;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,mCAAmC,EACnC;AAQF;;;AAGG;AACH,MAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAClE,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACd,CAAA,CAAC,CAAC;AAEH;MAEa,YAAY,CAAA;AACf,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACpB,IAAA,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;;IAGhE,OAAO,GAAgB,IAAI,CAAC;;AAG5B,IAAA,gBAAgB,CAAc;;IAG9B,cAAc,GAAG,KAAK,CAAC;;AAGvB,IAAA,qBAAqB,CAAgC;;AAGrD,IAAA,gBAAgB,CAAgC;AAExD;;;AAGG;IACK,2BAA2B,GAAG,KAAK,CAAC;;AAGpC,IAAA,YAAY,GAAG,IAAI,GAAG,EAAqC,CAAC;;IAG5D,sBAAsB,GAAG,CAAC,CAAC;AAEnC;;;;;AAKG;AACK,IAAA,2BAA2B,GAAG,IAAI,GAAG,EAA+C,CAAC;AAE7F;;;AAGG;AACc,IAAA,cAAc,CAA4B;AAE3D;;;AAGG;IACK,oBAAoB,GAAG,MAAK;;;AAGlC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;AAC/E,KAAC,CAAC;;IAGQ,SAAS,GAAI,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAGzC,IAAA,0BAA0B,GAAG,IAAI,OAAO,EAAQ,CAAC;AAIlE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,OAAO,GAAG,MAAM,CAA6B,6BAA6B,EAAE;AAChF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,aAAa,IAAI,yBAAyB,CAAC,SAAS,CAAC;KACrF;AACD;;;AAGG;AACK,IAAA,6BAA6B,GAAG,CAAC,KAAY,KAAI;AACvD,QAAA,MAAM,MAAM,GAAG,eAAe,CAAc,KAAK,CAAC,CAAC;;AAGnD,QAAA,KAAK,IAAI,OAAO,GAAG,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,aAAa,EAAE;AACnE,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAmB,EAAE,OAAO,CAAC,CAAC;aAC7C;iBAAM;AACL,gBAAA,IAAI,CAAC,OAAO,CAAC,KAAmB,EAAE,OAAO,CAAC,CAAC;aAC5C;SACF;AACH,KAAC,CAAC;AAoBF,IAAA,OAAO,CACL,OAA8C,EAC9C,aAAA,GAAyB,KAAK,EAAA;AAE9B,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;;AAG7C,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,aAAa,CAAC,QAAQ,KAAK,CAAC,EAAE;;YAE7D,OAAOA,EAAY,EAAE,CAAC;SACvB;;;;QAKD,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;;QAGxD,IAAI,UAAU,EAAE;YACd,IAAI,aAAa,EAAE;;;;AAIjB,gBAAA,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;aACjC;YAED,OAAO,UAAU,CAAC,OAAO,CAAC;SAC3B;;AAGD,QAAA,MAAM,IAAI,GAAyB;AACjC,YAAA,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,IAAI,OAAO,EAAe;YACnC,QAAQ;SACT,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAcD,IAAA,cAAc,CAAC,OAA8C,EAAA;AAC3D,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAEzD,IAAI,WAAW,EAAE;AACf,YAAA,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AAE/B,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACxC,YAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;SAC1C;KACF;AAkBD,IAAA,QAAQ,CACN,OAA8C,EAC9C,MAAmB,EACnB,OAAsB,EAAA;AAEtB,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC;;;;AAKzD,QAAA,IAAI,aAAa,KAAK,cAAc,EAAE;AACpC,YAAA,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,KACzE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAClD,CAAC;SACH;aAAM;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;AAGxB,YAAA,IAAI,OAAO,aAAa,CAAC,KAAK,KAAK,UAAU,EAAE;AAC7C,gBAAA,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aAC9B;SACF;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;KAC7E;;IAGO,YAAY,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC;KACnC;;IAGO,UAAU,GAAA;AAChB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAChC,QAAA,OAAO,GAAG,CAAC,WAAW,IAAI,MAAM,CAAC;KAClC;AAEO,IAAA,eAAe,CAAC,gBAAoC,EAAA;AAC1D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;;;AAGhB,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,gBAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;aAChF;iBAAM;gBACL,OAAO,IAAI,CAAC,OAAO,CAAC;aACrB;SACF;;;;;;;;;;QAWD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAC9B;;;;;QAMD,IAAI,gBAAgB,IAAI,IAAI,CAAC,gCAAgC,CAAC,gBAAgB,CAAC,EAAE;AAC/E,YAAA,OAAO,OAAO,CAAC;SAChB;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AAED;;;;;;;AAOG;AACK,IAAA,0BAA0B,CAAC,gBAAoC,EAAA;;;;;;;;;;;AAWrE,QAAA,QACE,IAAI,CAAC,cAAc,KAAK,yBAAyB,CAAC,QAAQ;AAC1D,YAAA,CAAC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,EAC3E;KACH;AAED;;;;AAIG;IACK,WAAW,CAAC,OAAoB,EAAE,MAAoB,EAAA;QAC5D,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;QAClE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC;QACxE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;QAClE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC;KACvE;AAED;;;;;;AAMG;AACK,IAAA,UAAU,CAAC,MAAmB,EAAE,iBAAiB,GAAG,KAAK,EAAA;AAC/D,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,2BAA2B,GAAG,MAAM,KAAK,OAAO,IAAI,iBAAiB,CAAC;;;;;;YAO3E,IAAI,IAAI,CAAC,cAAc,KAAK,yBAAyB,CAAC,SAAS,EAAE;AAC/D,gBAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpC,gBAAA,MAAM,EAAE,GAAG,IAAI,CAAC,2BAA2B,GAAG,eAAe,GAAG,CAAC,CAAC;AAClE,gBAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;aACrE;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACK,QAAQ,CAAC,KAAiB,EAAE,OAAoB,EAAA;;;;;;;QAQtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACnD,QAAA,MAAM,gBAAgB,GAAG,eAAe,CAAc,KAAK,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,WAAW,KAAK,CAAC,WAAW,CAAC,aAAa,IAAI,OAAO,KAAK,gBAAgB,CAAC,EAAE;YAChF,OAAO;SACR;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,WAAW,CAAC,CAAC;KACnF;AAED;;;;AAIG;IACH,OAAO,CAAC,KAAiB,EAAE,OAAoB,EAAA;;;QAG7C,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAEnD,QAAA,IACE,CAAC,WAAW;aACX,WAAW,CAAC,aAAa;gBACxB,KAAK,CAAC,aAAa,YAAY,IAAI;gBACnC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EACxC;YACA,OAAO;SACR;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;KACrC;IAEO,WAAW,CAAC,IAA0B,EAAE,MAAmB,EAAA;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;KACF;AAEO,IAAA,wBAAwB,CAAC,WAAiC,EAAA;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO;SACR;AAED,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AACtC,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEnF,IAAI,CAAC,sBAAsB,EAAE;AAC3B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,QAAQ,CAAC,gBAAgB,CACvB,OAAO,EACP,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B,CAAC;gBACF,QAAQ,CAAC,gBAAgB,CACvB,MAAM,EACN,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,EAAE,sBAAsB,GAAG,CAAC,CAAC,CAAC;;AAG3E,QAAA,IAAI,EAAE,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;;;AAGvC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC9D,aAAC,CAAC,CAAC;;YAGH,IAAI,CAAC,sBAAsB,CAAC,gBAAgB;AACzC,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;iBAChD,SAAS,CAAC,QAAQ,IAAG;gBACpB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,yBAAyB,CAAC;AAC1D,aAAC,CAAC,CAAC;SACN;KACF;AAEO,IAAA,sBAAsB,CAAC,WAAiC,EAAA;AAC9D,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAEtC,IAAI,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAClD,MAAM,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;AAE/E,YAAA,IAAI,sBAAsB,GAAG,CAAC,EAAE;gBAC9B,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,EAAE,sBAAsB,GAAG,CAAC,CAAC,CAAC;aAC5E;iBAAM;gBACL,QAAQ,CAAC,mBAAmB,CAC1B,OAAO,EACP,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B,CAAC;gBACF,QAAQ,CAAC,mBAAmB,CAC1B,MAAM,EACN,IAAI,CAAC,6BAA6B,EAClC,2BAA2B,CAC5B,CAAC;AACF,gBAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACnD;SACF;;AAGD,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,sBAAsB,EAAE;AAClC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;;AAG/D,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,CAAC;;AAGvC,YAAA,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACzC,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACrC;KACF;;AAGO,IAAA,cAAc,CACpB,OAAoB,EACpB,MAAmB,EACnB,WAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;KAChC;AAED;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,OAAoB,EAAA;QAClD,MAAM,OAAO,GAA0C,EAAE,CAAC;QAE1D,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,cAAc,KAAI;AACjD,YAAA,IAAI,cAAc,KAAK,OAAO,KAAK,IAAI,CAAC,aAAa,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;gBAC1F,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;aACtC;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;AAIG;AACK,IAAA,gCAAgC,CAAC,gBAA6B,EAAA;QACpE,MAAM,EAAC,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;;;;QAK9F,IACE,kBAAkB,KAAK,OAAO;AAC9B,YAAA,CAAC,gBAAgB;AACjB,YAAA,gBAAgB,KAAK,gBAAgB;aACpC,gBAAgB,CAAC,QAAQ,KAAK,OAAO,IAAI,gBAAgB,CAAC,QAAQ,KAAK,UAAU,CAAC;YAClF,gBAA2D,CAAC,QAAQ,EACrE;AACA,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,MAAM,MAAM,GAAI,gBAA2D,CAAC,MAAM,CAAC;QAEnF,IAAI,MAAM,EAAE;AACV,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;AACxC,oBAAA,OAAO,IAAI,CAAC;iBACb;aACF;SACF;AAED,QAAA,OAAO,KAAK,CAAC;KACd;uGAvgBU,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA,CAAA;;2FAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA2gBhC;;;;;;;;AAQG;MAKU,eAAe,CAAA;AAClB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;AAC1D,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAErC,IAAA,oBAAoB,CAAe;IACnC,YAAY,GAAgB,IAAI,CAAC;AAEtB,IAAA,cAAc,GAAG,IAAI,YAAY,EAAe,CAAC;AAGpE,IAAA,WAAA,GAAA,GAAgB;AAEhB,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IAED,eAAe,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa;AAC3C,aAAA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;aAC1F,SAAS,CAAC,MAAM,IAAG;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;AAC3B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACN;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAEpD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;SACzC;KACF;uGAhCU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oDAAoD;AAC9D,oBAAA,QAAQ,EAAE,iBAAiB;AAC5B,iBAAA,CAAA;wDAQoB,cAAc,EAAA,CAAA;sBAAhC,MAAM;;;ACrmBT;IACY,iBAIX;AAJD,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,gBAAA,CAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AACd,IAAA,gBAAA,CAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AAChB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,GAI3B,EAAA,CAAA,CAAA,CAAA;AAED;AACO,MAAM,wBAAwB,GAAG,kCAAkC,CAAC;AAE3E;AACO,MAAM,wBAAwB,GAAG,kCAAkC,CAAC;AAE3E;AACO,MAAM,mCAAmC,GAAG,0BAA0B,CAAC;AAE9E;;;;;;;;;;AAUG;MAEU,wBAAwB,CAAA;AAC3B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAErC;;;AAGG;AACK,IAAA,2BAA2B,CAAU;AACrC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,uBAAuB,CAAe;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,kBAAkB,CAAC;aACtD,OAAO,CAAC,yBAAyB,CAAC;aAClC,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,gBAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;gBACzC,IAAI,CAAC,oCAAoC,EAAE,CAAC;aAC7C;AACH,SAAC,CAAC,CAAC;KACN;;IAGD,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO,gBAAgB,CAAC,IAAI,CAAC;SAC9B;;;;QAKD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACxD,QAAA,WAAW,CAAC,KAAK,CAAC,eAAe,GAAG,YAAY,CAAC;AACjD,QAAA,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;;;;;QAM7C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,MAAM,CAAC;AAC5D,QAAA,MAAM,aAAa,GACjB,cAAc,IAAI,cAAc,CAAC,gBAAgB;AAC/C,cAAE,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC;cAC5C,IAAI,CAAC;QACX,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa,IAAI,aAAa,CAAC,eAAe,KAAK,EAAE,EAAE,OAAO,CACpF,IAAI,EACJ,EAAE,CACH,CAAC;QACF,WAAW,CAAC,MAAM,EAAE,CAAC;QAErB,QAAQ,aAAa;;AAEnB,YAAA,KAAK,YAAY,CAAC;;AAElB,YAAA,KAAK,eAAe,CAAC;AACrB,YAAA,KAAK,eAAe;gBAClB,OAAO,gBAAgB,CAAC,cAAc,CAAC;;AAEzC,YAAA,KAAK,kBAAkB,CAAC;;AAExB,YAAA,KAAK,kBAAkB;gBACrB,OAAO,gBAAgB,CAAC,cAAc,CAAC;SAC1C;QACD,OAAO,gBAAgB,CAAC,IAAI,CAAC;KAC9B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;KAC5C;;IAGD,oCAAoC,GAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACxF,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;YAClD,WAAW,CAAC,MAAM,CAChB,mCAAmC,EACnC,wBAAwB,EACxB,wBAAwB,CACzB,CAAC;AACF,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;AAExC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACxC,YAAA,IAAI,IAAI,KAAK,gBAAgB,CAAC,cAAc,EAAE;AAC5C,gBAAA,WAAW,CAAC,GAAG,CAAC,mCAAmC,EAAE,wBAAwB,CAAC,CAAC;aAChF;AAAM,iBAAA,IAAI,IAAI,KAAK,gBAAgB,CAAC,cAAc,EAAE;AACnD,gBAAA,WAAW,CAAC,GAAG,CAAC,mCAAmC,EAAE,wBAAwB,CAAC,CAAC;aAChF;SACF;KACF;uGA3FU,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAxB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,cADZ,MAAM,EAAA,CAAA,CAAA;;2FAClB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;MCtBnB,UAAU,CAAA;AACrB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,CAAC,wBAAwB,CAAC,CAAC,oCAAoC,EAAE,CAAC;KACzE;uGAHU,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EAHX,OAAA,EAAA,CAAA,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,CAC3D,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;AAEzC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,YAHX,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAGd,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC;AACtE,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC;AACtD,iBAAA,CAAA;;;ACRD;;;;AAIG;AACH,MAAM,QAAQ,GAA2B,EAAE,CAAC;AAE5C;MAEa,YAAY,CAAA;AACf,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAEhC;;;AAGG;AACH,IAAA,KAAK,CAAC,MAAc,EAAA;;;AAGlB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AACxB,YAAA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;SACvB;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACpC,YAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACtB;QAED,OAAO,CAAA,EAAG,MAAM,CAAG,EAAA,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAA,CAAE,CAAC;KACzC;uGAnBU,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA,CAAA;;2FAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;AClBhC;;AAEG;;;;"}