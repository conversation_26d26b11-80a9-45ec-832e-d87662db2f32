{"version": 3, "file": "text-field.mjs", "sources": ["../../../../../../src/cdk/text-field/text-field-style-loader.ts", "../../../../../../src/cdk/text-field/autofill.ts", "../../../../../../src/cdk/text-field/autosize.ts", "../../../../../../src/cdk/text-field/text-field-module.ts", "../../../../../../src/cdk/text-field/text-field_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\n\n/** Component used to load the structural styles of the text field. */\n@Component({\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  styleUrl: 'text-field-prebuilt.css',\n  host: {'cdk-text-field-style-loader': ''},\n})\nexport class _CdkTextFieldStyleLoader {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform, _bindEventWithOptions} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n  RendererFactory2,\n} from '@angular/core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {coerceElement} from '@angular/cdk/coercion';\nimport {EMPTY, Observable, Subject} from 'rxjs';\nimport {_CdkTextFieldStyleLoader} from './text-field-style-loader';\n\n/** An event that is emitted when the autofill state of an input changes. */\nexport type AutofillEvent = {\n  /** The element whose autofill state changes. */\n  target: Element;\n  /** Whether the element is currently autofilled. */\n  isAutofilled: boolean;\n};\n\n/** Used to track info about currently monitored elements. */\ntype MonitoredElementInfo = {\n  readonly subject: Subject<AutofillEvent>;\n  unlisten: () => void;\n};\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = {passive: true};\n\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\n@Injectable({providedIn: 'root'})\nexport class AutofillMonitor implements OnDestroy {\n  private _platform = inject(Platform);\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n\n  private _styleLoader = inject(_CdkPrivateStyleLoader);\n  private _monitoredElements = new Map<Element, MonitoredElementInfo>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Monitor for changes in the autofill state of the given input element.\n   * @param element The element to monitor.\n   * @return A stream of autofill state changes.\n   */\n  monitor(element: Element): Observable<AutofillEvent>;\n\n  /**\n   * Monitor for changes in the autofill state of the given input element.\n   * @param element The element to monitor.\n   * @return A stream of autofill state changes.\n   */\n  monitor(element: ElementRef<Element>): Observable<AutofillEvent>;\n\n  monitor(elementOrRef: Element | ElementRef<Element>): Observable<AutofillEvent> {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n\n    this._styleLoader.load(_CdkTextFieldStyleLoader);\n\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      return info.subject;\n    }\n\n    const subject = new Subject<AutofillEvent>();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = (event: AnimationEvent) => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (\n        event.animationName === 'cdk-text-field-autofill-start' &&\n        !element.classList.contains(cssClass)\n      ) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => subject.next({target: event.target as Element, isAutofilled: true}));\n      } else if (\n        event.animationName === 'cdk-text-field-autofill-end' &&\n        element.classList.contains(cssClass)\n      ) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() =>\n          subject.next({target: event.target as Element, isAutofilled: false}),\n        );\n      }\n    };\n\n    const unlisten = this._ngZone.runOutsideAngular(() => {\n      element.classList.add('cdk-text-field-autofill-monitored');\n      return _bindEventWithOptions(\n        this._renderer,\n        element,\n        'animationstart',\n        listener,\n        listenerOptions,\n      );\n    });\n\n    this._monitoredElements.set(element, {subject, unlisten});\n    return subject;\n  }\n\n  /**\n   * Stop monitoring the autofill state of the given input element.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: Element): void;\n\n  /**\n   * Stop monitoring the autofill state of the given input element.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: ElementRef<Element>): void;\n\n  stopMonitoring(elementOrRef: Element | ElementRef<Element>): void {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n}\n\n/** A directive that can be used to monitor the autofill state of an input. */\n@Directive({\n  selector: '[cdkAutofill]',\n})\nexport class CdkAutofill implements OnDestroy, OnInit {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _autofillMonitor = inject(AutofillMonitor);\n\n  /** Emits when the autofill state of the element changes. */\n  @Output() readonly cdkAutofill = new EventEmitter<AutofillEvent>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnInit() {\n    this._autofillMonitor\n      .monitor(this._elementRef)\n      .subscribe(event => this.cdkAutofill.emit(event));\n  }\n\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NumberInput, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  AfterViewInit,\n  DoCheck,\n  OnDestroy,\n  NgZone,\n  booleanAttribute,\n  inject,\n  Renderer2,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {Platform} from '@angular/cdk/platform';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {auditTime} from 'rxjs/operators';\nimport {Subject} from 'rxjs';\nimport {_CdkTextFieldStyleLoader} from './text-field-style-loader';\n\n/** Directive to automatically resize a textarea to fit its content. */\n@Directive({\n  selector: 'textarea[cdkTextareaAutosize]',\n  exportAs: 'cdkTextareaAutosize',\n  host: {\n    'class': 'cdk-textarea-autosize',\n    // Textarea elements that have the directive applied should have a single row by default.\n    // Browsers normally show two rows by default and therefore this limits the minRows binding.\n    'rows': '1',\n    '(input)': '_noopInputHandler()',\n  },\n})\nexport class CdkTextareaAutosize implements AfterViewInit, DoCheck, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _platform = inject(Platform);\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(Renderer2);\n  private _resizeEvents = new Subject<void>();\n\n  /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n  private _previousValue?: string;\n  private _initialHeight: string | undefined;\n  private readonly _destroyed = new Subject<void>();\n  private _listenerCleanups: (() => void)[] | undefined;\n\n  private _minRows: number;\n  private _maxRows: number;\n  private _enabled: boolean = true;\n\n  /**\n   * Value of minRows as of last resize. If the minRows has decreased, the\n   * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n   * does not have the same problem because it does not affect the textarea's scrollHeight.\n   */\n  private _previousMinRows: number = -1;\n\n  private _textareaElement: HTMLTextAreaElement;\n\n  /** Minimum amount of rows in the textarea. */\n  @Input('cdkAutosizeMinRows')\n  get minRows(): number {\n    return this._minRows;\n  }\n  set minRows(value: NumberInput) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n\n  /** Maximum amount of rows in the textarea. */\n  @Input('cdkAutosizeMaxRows')\n  get maxRows(): number {\n    return this._maxRows;\n  }\n  set maxRows(value: NumberInput) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n\n  /** Whether autosizing is enabled or not */\n  @Input({alias: 'cdkTextareaAutosize', transform: booleanAttribute})\n  get enabled(): boolean {\n    return this._enabled;\n  }\n  set enabled(value: boolean) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n\n  @Input()\n  get placeholder(): string {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value: string) {\n    this._cachedPlaceholderHeight = undefined;\n\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n\n    this._cacheTextareaPlaceholderHeight();\n  }\n\n  /** Cached height of a textarea with a single row. */\n  private _cachedLineHeight?: number;\n  /** Cached height of a textarea with only the placeholder. */\n  private _cachedPlaceholderHeight?: number;\n\n  /** Used to reference correct document/window */\n  protected _document? = inject(DOCUMENT, {optional: true});\n\n  private _hasFocus: boolean;\n\n  private _isViewInited = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_CdkTextFieldStyleLoader);\n    this._textareaElement = this._elementRef.nativeElement as HTMLTextAreaElement;\n  }\n\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight(): void {\n    const minHeight =\n      this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight(): void {\n    const maxHeight =\n      this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n\n      this._ngZone.runOutsideAngular(() => {\n        this._listenerCleanups = [\n          this._renderer.listen('window', 'resize', () => this._resizeEvents.next()),\n          this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent),\n          this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent),\n        ];\n        this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n          // Clear the cached heights since the styles can change\n          // when the window is resized (e.g. by media queries).\n          this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n          this.resizeToFitContent(true);\n        });\n      });\n\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n\n  ngOnDestroy() {\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n    this._resizeEvents.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  private _cacheTextareaLineHeight(): void {\n    if (this._cachedLineHeight) {\n      return;\n    }\n\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this._textareaElement.cloneNode(false) as HTMLTextAreaElement;\n    const cloneStyles = textareaClone.style;\n    textareaClone.rows = 1;\n\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    cloneStyles.position = 'absolute';\n    cloneStyles.visibility = 'hidden';\n    cloneStyles.border = 'none';\n    cloneStyles.padding = '0';\n    cloneStyles.height = '';\n    cloneStyles.minHeight = '';\n    cloneStyles.maxHeight = '';\n\n    // App styles might be messing with the height through the positioning properties.\n    cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    cloneStyles.overflow = 'hidden';\n\n    this._textareaElement.parentNode!.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n\n  private _measureScrollHeight(): number {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox\n      ? 'cdk-textarea-autosize-measuring-firefox'\n      : 'cdk-textarea-autosize-measuring';\n\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n\n    return scrollHeight;\n  }\n\n  private _cacheTextareaPlaceholderHeight(): void {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n\n    const value = this._textareaElement.value;\n\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n\n  /** Handles `focus` and `blur` events. */\n  private _handleFocusEvent = (event: FocusEvent) => {\n    this._hasFocus = event.type === 'focus';\n  };\n\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force: boolean = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n\n    const textarea = this._elementRef.nativeElement as HTMLTextAreaElement;\n    const value = textarea.value;\n\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  private _scrollToCaretPosition(textarea: HTMLTextAreaElement) {\n    const {selectionStart, selectionEnd} = textarea;\n\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkAutofill} from './autofill';\nimport {CdkTextareaAutosize} from './autosize';\n\n@NgModule({\n  imports: [CdkAutofill, CdkTextareaAutosize],\n  exports: [CdkAutofill, CdkTextareaAutosize],\n})\nexport class TextFieldModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;AAUA;MAQa,wBAAwB,CAAA;uGAAxB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,qIANzB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ymBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAMD,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAPpC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAE,EACK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA,EAAC,6BAA6B,EAAE,EAAE,EAAC,EAAA,MAAA,EAAA,CAAA,ymBAAA,CAAA,EAAA,CAAA;;;ACwB3C;AACA,MAAM,eAAe,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;AAExC;;;;AAIG;MAEU,eAAe,CAAA;AAClB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAEhE,IAAA,YAAY,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAC9C,IAAA,kBAAkB,GAAG,IAAI,GAAG,EAAiC,CAAC;AAGtE,IAAA,WAAA,GAAA,GAAgB;AAgBhB,IAAA,OAAO,CAAC,YAA2C,EAAA;AACjD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;AAEjD,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAiB,CAAC;QAC7C,MAAM,QAAQ,GAAG,2BAA2B,CAAC;AAC7C,QAAA,MAAM,QAAQ,GAAG,CAAC,KAAqB,KAAI;;;;AAIzC,YAAA,IACE,KAAK,CAAC,aAAa,KAAK,+BAA+B;gBACvD,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACrC;AACA,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,MAAiB,EAAE,YAAY,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;aAC7F;AAAM,iBAAA,IACL,KAAK,CAAC,aAAa,KAAK,6BAA6B;gBACrD,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACpC;AACA,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MACf,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,KAAK,CAAC,MAAiB,EAAE,YAAY,EAAE,KAAK,EAAC,CAAC,CACrE,CAAC;aACH;AACH,SAAC,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACnD,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;AAC3D,YAAA,OAAO,qBAAqB,CAC1B,IAAI,CAAC,SAAS,EACd,OAAO,EACP,gBAAgB,EAChB,QAAQ,EACR,eAAe,CAChB,CAAC;AACJ,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,QAAQ,EAAC,CAAC,CAAC;AAC1D,QAAA,OAAO,OAAO,CAAC;KAChB;AAcD,IAAA,cAAc,CAAC,YAA2C,EAAA;AACxD,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAC9D,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;AACtD,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACzC;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;KACnF;uGAxGU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA,CAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA4GhC;MAIa,WAAW,CAAA;AACd,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;AAC1D,IAAA,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;;AAGhC,IAAA,WAAW,GAAG,IAAI,YAAY,EAAiB,CAAC;AAGnE,IAAA,WAAA,GAAA,GAAgB;IAEhB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,gBAAgB;AAClB,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AACzB,aAAA,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACrD;IAED,WAAW,GAAA;QACT,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACxD;uGAlBU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAHvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AAC1B,iBAAA,CAAA;wDAMoB,WAAW,EAAA,CAAA;sBAA7B,MAAM;;;ACzIT;MAYa,mBAAmB,CAAA;AACtB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;AAC1D,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9B,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAGpC,IAAA,cAAc,CAAU;AACxB,IAAA,cAAc,CAAqB;AAC1B,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;AAC1C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,QAAQ,CAAS;AACjB,IAAA,QAAQ,CAAS;IACjB,QAAQ,GAAY,IAAI,CAAC;AAEjC;;;;AAIG;IACK,gBAAgB,GAAW,CAAC,CAAC,CAAC;AAE9B,IAAA,gBAAgB,CAAsB;;AAG9C,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAGD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;;;AAGxB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YAC3B,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;SACxE;KACF;AAED,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;KAC1C;IACD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;QAE1C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SAC1D;aAAM;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACxC;;AAGO,IAAA,iBAAiB,CAAU;;AAE3B,IAAA,wBAAwB,CAAU;;IAGhC,SAAS,GAAI,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAElD,IAAA,SAAS,CAAU;IAEnB,aAAa,GAAG,KAAK,CAAC;AAI9B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;AACnD,QAAA,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAoC,CAAC;KAC/E;;IAGD,aAAa,GAAA;QACX,MAAM,SAAS,GACb,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,GAAG,IAAI,CAAC;QAE/F,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;SACnD;KACF;;IAGD,aAAa,GAAA;QACX,MAAM,SAAS,GACb,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,GAAG,IAAI,CAAC;QAE/F,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;SACnD;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;;YAE5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC;YACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,IAAI,CAAC,iBAAiB,GAAG;AACvB,oBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AAC1E,oBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAC7E,oBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC;iBAC7E,CAAC;AACF,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;;oBAGpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;AACnE,oBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAChC,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC/B;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;AAED;;;;;;AAMG;IACK,wBAAwB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;SACR;;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAwB,CAAC;AACpF,QAAA,MAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC;AACxC,QAAA,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;;;;AAKvB,QAAA,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC;AAClC,QAAA,WAAW,CAAC,UAAU,GAAG,QAAQ,CAAC;AAClC,QAAA,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,QAAA,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC;AAC1B,QAAA,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;AACxB,QAAA,WAAW,CAAC,SAAS,GAAG,EAAE,CAAC;AAC3B,QAAA,WAAW,CAAC,SAAS,GAAG,EAAE,CAAC;;AAG3B,QAAA,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC;;;;;;AAOrF,QAAA,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEhC,IAAI,CAAC,gBAAgB,CAAC,UAAW,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,YAAY,CAAC;QACpD,aAAa,CAAC,MAAM,EAAE,CAAC;;QAGvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAEO,oBAAoB,GAAA;AAC1B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACtC,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,CAAC;AACxD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACzC,QAAA,MAAM,iBAAiB,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,cAAc,GAAG,SAAS;AAC9B,cAAE,yCAAyC;cACzC,iCAAiC,CAAC;;;;QAKtC,IAAI,iBAAiB,EAAE;YACrB,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,OAAO,CAAC,YAAY,CAAA,EAAA,CAAI,CAAC;SAC1D;;;AAID,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;;;AAGtC,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9C,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAEzC,IAAI,iBAAiB,EAAE;AACrB,YAAA,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,cAAc,CAAC;SAC7C;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;IAEO,+BAA+B,GAAA;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,wBAAwB,IAAI,SAAS,EAAE;YACrE,OAAO;SACR;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;YAClC,OAAO;SACR;AAED,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAE1C,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;AAChE,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;KACrC;;AAGO,IAAA,iBAAiB,GAAG,CAAC,KAAiB,KAAI;QAChD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;AAC1C,KAAC,CAAC;IAEF,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;KACF;AAED;;;;AAIG;IACH,kBAAkB,CAAC,QAAiB,KAAK,EAAA;;AAEvC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,+BAA+B,EAAE,CAAC;;;AAIvC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO;SACR;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,aAAoC,CAAC;AACvE,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;;AAG7B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,gBAAgB,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;YACtF,OAAO;SACR;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACjD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,wBAAwB,IAAI,CAAC,CAAC,CAAC;;QAG1E,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAG,EAAA,MAAM,IAAI,CAAC;AAEtC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;gBAChD,qBAAqB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;aACpE;iBAAM;gBACL,UAAU,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;aACzD;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;KACvC;AAED;;AAEG;IACH,KAAK,GAAA;;;AAGH,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;SAC1D;KACF;IAED,iBAAiB,GAAA;;KAEhB;AAED;;;;AAIG;AACK,IAAA,sBAAsB,CAAC,QAA6B,EAAA;AAC1D,QAAA,MAAM,EAAC,cAAc,EAAE,YAAY,EAAC,GAAG,QAAQ,CAAC;;;;;;;QAQhD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AAChD,YAAA,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SAC1D;KACF;uGA1UU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,+MA+CmB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,GAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FA/CtD,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAX/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,uBAAuB;;;AAGhC,wBAAA,MAAM,EAAE,GAAG;AACX,wBAAA,SAAS,EAAE,qBAAqB;AACjC,qBAAA;AACF,iBAAA,CAAA;wDA6BK,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,oBAAoB,CAAA;gBAWvB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,oBAAoB,CAAA;gBAWvB,OAAO,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAa9D,WAAW,EAAA,CAAA;sBADd,KAAK;;;MCnFK,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,WAAW,EAAE,mBAAmB,CAChC,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,mBAAmB,CAAA,EAAA,CAAA,CAAA;wGAE/B,eAAe,EAAA,CAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC;AAC3C,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC;AAC5C,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}