{"version": 3, "file": "observers.mjs", "sources": ["../../../../../../src/cdk/observers/observe-content.ts", "../../../../../../src/cdk/observers/observers_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NumberInput, coerceElement, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  AfterContentInit,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  Input,\n  NgModule,\n  NgZone,\n  OnDestroy,\n  Output,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {Observable, Observer, Subject, Subscription} from 'rxjs';\nimport {debounceTime, filter, map} from 'rxjs/operators';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record: MutationRecord) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n@Injectable({providedIn: 'root'})\nexport class MutationObserverFactory {\n  create(callback: MutationCallback): MutationObserver | null {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n}\n\n/** An injectable service that allows watching elements for changes to their content. */\n@Injectable({providedIn: 'root'})\nexport class ContentObserver implements OnDestroy {\n  private _mutationObserverFactory = inject(MutationObserverFactory);\n\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  private _observedElements = new Map<\n    Element,\n    {\n      observer: MutationObserver | null;\n      readonly stream: Subject<MutationRecord[]>;\n      count: number;\n    }\n  >();\n\n  private _ngZone = inject(NgZone);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n\n  /**\n   * Observe content changes on an element.\n   * @param element The element to observe for content changes.\n   */\n  observe(element: Element): Observable<MutationRecord[]>;\n\n  /**\n   * Observe content changes on an element.\n   * @param element The element to observe for content changes.\n   */\n  observe(element: ElementRef<Element>): Observable<MutationRecord[]>;\n\n  observe(elementOrRef: Element | ElementRef<Element>): Observable<MutationRecord[]> {\n    const element = coerceElement(elementOrRef);\n\n    return new Observable((observer: Observer<MutationRecord[]>) => {\n      const stream = this._observeElement(element);\n      const subscription = stream\n        .pipe(\n          map(records => records.filter(record => !shouldIgnoreRecord(record))),\n          filter(records => !!records.length),\n        )\n        .subscribe(records => {\n          this._ngZone.run(() => {\n            observer.next(records);\n          });\n        });\n\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  private _observeElement(element: Element): Subject<MutationRecord[]> {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject<MutationRecord[]>();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true,\n          });\n        }\n        this._observedElements.set(element, {observer, stream, count: 1});\n      } else {\n        this._observedElements.get(element)!.count++;\n      }\n      return this._observedElements.get(element)!.stream;\n    });\n  }\n\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  private _unobserveElement(element: Element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element)!.count--;\n      if (!this._observedElements.get(element)!.count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n\n  /** Clean up the underlying MutationObserver for the specified element. */\n  private _cleanupObserver(element: Element) {\n    if (this._observedElements.has(element)) {\n      const {observer, stream} = this._observedElements.get(element)!;\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n}\n\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n@Directive({\n  selector: '[cdkObserveContent]',\n  exportAs: 'cdkObserveContent',\n})\nexport class CdkObserveContent implements AfterContentInit, OnDestroy {\n  private _contentObserver = inject(ContentObserver);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /** Event emitted for each change in the element's content. */\n  @Output('cdkObserveContent') readonly event = new EventEmitter<MutationRecord[]>();\n\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  @Input({alias: 'cdkObserveContentDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  private _disabled = false;\n\n  /** Debounce interval for emitting the changes. */\n  @Input()\n  get debounce(): number {\n    return this._debounce;\n  }\n  set debounce(value: NumberInput) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  private _debounce: number;\n\n  private _currentSubscription: Subscription | null = null;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n\n  private _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n\n    this._currentSubscription = (\n      this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream\n    ).subscribe(this.event);\n  }\n\n  private _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n}\n\n@NgModule({\n  imports: [CdkObserveContent],\n  exports: [CdkObserveContent],\n  providers: [MutationObserverFactory],\n})\nexport class ObserversModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;AA0BA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAsB,EAAA;;AAEhD,IAAA,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,IAAI,MAAM,CAAC,MAAM,YAAY,OAAO,EAAE;AACvE,QAAA,OAAO,IAAI,CAAC;KACb;;AAED,IAAA,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;AAC/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,OAAO,CAAC,EAAE;AAC9C,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;AACD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,YAAA,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,OAAO,CAAC,EAAE;AAChD,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;AAGG;MAEU,uBAAuB,CAAA;AAClC,IAAA,MAAM,CAAC,QAA0B,EAAA;AAC/B,QAAA,OAAO,OAAO,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACxF;uGAHU,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAvB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,cADX,MAAM,EAAA,CAAA,CAAA;;2FAClB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBADnC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAOhC;MAEa,eAAe,CAAA;AAClB,IAAA,wBAAwB,GAAG,MAAM,CAAC,uBAAuB,CAAC,CAAC;;AAG3D,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAOhC,CAAC;AAEI,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAGjC,IAAA,WAAA,GAAA,GAAgB;IAEhB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;KAChF;AAcD,IAAA,OAAO,CAAC,YAA2C,EAAA;AACjD,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;AAE5C,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,QAAoC,KAAI;YAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,MAAM;AACxB,iBAAA,IAAI,CACH,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EACrE,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CACpC;iBACA,SAAS,CAAC,OAAO,IAAG;AACnB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,oBAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzB,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AAEL,YAAA,OAAO,MAAK;gBACV,YAAY,CAAC,WAAW,EAAE,CAAC;AAC3B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAClC,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,eAAe,CAAC,OAAgB,EAAA;AACtC,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YACzC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACxC,gBAAA,MAAM,MAAM,GAAG,IAAI,OAAO,EAAoB,CAAC;AAC/C,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3F,IAAI,QAAQ,EAAE;AACZ,oBAAA,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE;AACxB,wBAAA,aAAa,EAAE,IAAI;AACnB,wBAAA,SAAS,EAAE,IAAI;AACf,wBAAA,OAAO,EAAE,IAAI;AACd,qBAAA,CAAC,CAAC;iBACJ;AACD,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC;aACnE;iBAAM;gBACL,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,KAAK,EAAE,CAAC;aAC9C;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,MAAM,CAAC;AACrD,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,OAAgB,EAAA;QACxC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACvC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,KAAK,EAAE,CAAC;AAC7C,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,KAAK,EAAE;AAC/C,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAChC;SACF;KACF;;AAGO,IAAA,gBAAgB,CAAC,OAAgB,EAAA;QACvC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACvC,YAAA,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;YAChE,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,UAAU,EAAE,CAAC;aACvB;YACD,MAAM,CAAC,QAAQ,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACxC;KACF;uGAxGU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA,CAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AA4GhC;;;AAGG;MAKU,iBAAiB,CAAA;AACpB,IAAA,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;AAC3C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;;AAG5B,IAAA,KAAK,GAAG,IAAI,YAAY,EAAoB,CAAC;AAEnF;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;KAC1D;IACO,SAAS,GAAG,KAAK,CAAC;;AAG1B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,KAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;AACO,IAAA,SAAS,CAAS;IAElB,oBAAoB,GAAwB,IAAI,CAAC;AAGzD,IAAA,WAAA,GAAA,GAAgB;IAEhB,kBAAkB,GAAA;QAChB,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChD,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;KACF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,YAAY,EAAE,CAAC;KACrB;IAEO,UAAU,GAAA;QAChB,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE/D,QAAA,IAAI,CAAC,oBAAoB,GAAG,CAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,EACjE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzB;IAEO,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,oBAAoB,EAAE,WAAW,EAAE,CAAC;KAC1C;uGA1DU,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,qHAW2B,gBAAgB,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAX5D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA,CAAA;wDAMuC,KAAK,EAAA,CAAA;sBAA1C,MAAM;uBAAC,mBAAmB,CAAA;gBAOvB,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,2BAA2B,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYpE,QAAQ,EAAA,CAAA;sBADX,KAAK;;MA4CK,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,OAAA,EAAA,CAlEf,iBAAiB,CAAA,EAAA,OAAA,EAAA,CAAjB,iBAAiB,CAAA,EAAA,CAAA,CAAA;wGAkEjB,eAAe,EAAA,SAAA,EAFf,CAAC,uBAAuB,CAAC,EAAA,CAAA,CAAA;;2FAEzB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,iBAAiB,CAAC;oBAC5B,OAAO,EAAE,CAAC,iBAAiB,CAAC;oBAC5B,SAAS,EAAE,CAAC,uBAAuB,CAAC;AACrC,iBAAA,CAAA;;;ACtPD;;AAEG;;;;"}