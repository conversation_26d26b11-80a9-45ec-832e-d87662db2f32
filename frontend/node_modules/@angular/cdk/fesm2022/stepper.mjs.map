{"version": 3, "file": "stepper.mjs", "sources": ["../../../../../../src/cdk/stepper/step-header.ts", "../../../../../../src/cdk/stepper/step-label.ts", "../../../../../../src/cdk/stepper/stepper.ts", "../../../../../../src/cdk/stepper/stepper-button.ts", "../../../../../../src/cdk/stepper/stepper-module.ts", "../../../../../../src/cdk/stepper/stepper_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, inject} from '@angular/core';\nimport {FocusableOption} from '@angular/cdk/a11y';\n\n@Directive({\n  selector: '[cdkStepHeader]',\n  host: {\n    'role': 'tab',\n  },\n})\nexport class CdkStepHeader implements FocusableOption {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Focuses the step header. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, TemplateRef, inject} from '@angular/core';\n\n@Directive({\n  selector: '[cdkStepLabel]',\n})\nexport class CdkStepLabel {\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, FocusableOption, FocusKeyManager} from '@angular/cdk/a11y';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ENTER, hasModifierKey, SPACE} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n  QueryList,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  AfterContentInit,\n  booleanAttribute,\n  numberAttribute,\n  inject,\n} from '@angular/core';\nimport {\n  ControlContainer,\n  type AbstractControl,\n  type NgForm,\n  type FormGroupDirective,\n} from '@angular/forms';\nimport {_getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {Observable, of as observableOf, Subject} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\n\nimport {CdkStepHeader} from './step-header';\nimport {CdkStepLabel} from './step-label';\n\n/**\n * Position state of the content of each step in stepper that is used for transitioning\n * the content into correct position upon step selection change.\n */\nexport type StepContentPositionState = 'previous' | 'current' | 'next';\n\n/** Possible orientation of a stepper. */\nexport type StepperOrientation = 'horizontal' | 'vertical';\n\n/** Change event emitted on selection changes. */\nexport class StepperSelectionEvent {\n  /** Index of the step now selected. */\n  selectedIndex: number;\n\n  /** Index of the step previously selected. */\n  previouslySelectedIndex: number;\n\n  /** The step instance now selected. */\n  selectedStep: CdkStep;\n\n  /** The step instance previously selected. */\n  previouslySelectedStep: CdkStep;\n}\n\n/** The state of each step. */\nexport type StepState = 'number' | 'edit' | 'done' | 'error' | string;\n\n/** Enum to represent the different states of the steps. */\nexport const STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error',\n};\n\n/** InjectionToken that can be used to specify the global stepper options. */\nexport const STEPPER_GLOBAL_OPTIONS = new InjectionToken<StepperOptions>('STEPPER_GLOBAL_OPTIONS');\n\n/** Configurable options for stepper. */\nexport interface StepperOptions {\n  /**\n   * Whether the stepper should display an error state or not.\n   * Default behavior is assumed to be false.\n   */\n  showError?: boolean;\n\n  /**\n   * Whether the stepper should display the default indicator type\n   * or not.\n   * Default behavior is assumed to be true.\n   */\n  displayDefaultIndicatorType?: boolean;\n}\n\n@Component({\n  selector: 'cdk-step',\n  exportAs: 'cdkStep',\n  template: '<ng-template><ng-content/></ng-template>',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class CdkStep implements OnChanges {\n  private _stepperOptions: StepperOptions;\n  _stepper = inject(CdkStepper);\n  _displayDefaultIndicatorType: boolean;\n\n  /** Template for step label if it exists. */\n  @ContentChild(CdkStepLabel) stepLabel: CdkStepLabel;\n\n  /** Forms that have been projected into the step. */\n  @ContentChildren(\n    // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n    // provides themselves as such, but we don't want to have a concrete reference to both of\n    // the directives. The type is marked as `Partial` in case we run into a class that provides\n    // itself as `ControlContainer` but doesn't have the same interface as the directives.\n    ControlContainer,\n    {\n      descendants: true,\n    },\n  )\n  protected _childForms: QueryList<Partial<NgForm | FormGroupDirective>> | undefined;\n\n  /** Template for step content. */\n  @ViewChild(TemplateRef, {static: true}) content: TemplateRef<any>;\n\n  /** The top level abstract control of the step. */\n  @Input() stepControl: AbstractControl;\n\n  /** Whether user has attempted to move away from the step. */\n  interacted = false;\n\n  /** Emits when the user has attempted to move away from the step. */\n  @Output('interacted')\n  readonly interactedStream: EventEmitter<CdkStep> = new EventEmitter<CdkStep>();\n\n  /** Plain text label of the step. */\n  @Input() label: string;\n\n  /** Error message to display when there's an error. */\n  @Input() errorMessage: string;\n\n  /** Aria label for the tab. */\n  @Input('aria-label') ariaLabel: string;\n\n  /**\n   * Reference to the element that the tab is labelled by.\n   * Will be cleared if `aria-label` is set at the same time.\n   */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** State of the step. */\n  @Input() state: StepState;\n\n  /** Whether the user can return to this step once it has been marked as completed. */\n  @Input({transform: booleanAttribute}) editable: boolean = true;\n\n  /** Whether the completion of step is optional. */\n  @Input({transform: booleanAttribute}) optional: boolean = false;\n\n  /** Whether step is marked as completed. */\n  @Input({transform: booleanAttribute})\n  get completed(): boolean {\n    return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n  }\n  set completed(value: boolean) {\n    this._completedOverride = value;\n  }\n  _completedOverride: boolean | null = null;\n\n  private _getDefaultCompleted() {\n    return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n  }\n\n  /** Whether step has an error. */\n  @Input({transform: booleanAttribute})\n  get hasError(): boolean {\n    return this._customError == null ? this._getDefaultError() : this._customError;\n  }\n  set hasError(value: boolean) {\n    this._customError = value;\n  }\n  private _customError: boolean | null = null;\n\n  private _getDefaultError() {\n    return this.stepControl && this.stepControl.invalid && this.interacted;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const stepperOptions = inject<StepperOptions>(STEPPER_GLOBAL_OPTIONS, {optional: true});\n    this._stepperOptions = stepperOptions ? stepperOptions : {};\n    this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n  }\n\n  /** Selects this step component. */\n  select(): void {\n    this._stepper.selected = this;\n  }\n\n  /** Resets the step to its initial state. Note that this includes resetting form data. */\n  reset(): void {\n    this.interacted = false;\n\n    if (this._completedOverride != null) {\n      this._completedOverride = false;\n    }\n\n    if (this._customError != null) {\n      this._customError = false;\n    }\n\n    if (this.stepControl) {\n      // Reset the forms since the default error state matchers will show errors on submit and we\n      // want the form to be back to its initial state (see #29781). Submitted state is on the\n      // individual directives, rather than the control, so we need to reset them ourselves.\n      this._childForms?.forEach(form => form.resetForm?.());\n      this.stepControl.reset();\n    }\n  }\n\n  ngOnChanges() {\n    // Since basically all inputs of the MatStep get proxied through the view down to the\n    // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n    this._stepper._stateChanged();\n  }\n\n  _markAsInteracted() {\n    if (!this.interacted) {\n      this.interacted = true;\n      this.interactedStream.emit(this);\n    }\n  }\n\n  /** Determines whether the error state can be shown. */\n  _showError(): boolean {\n    // We want to show the error state either if the user opted into/out of it using the\n    // global options, or if they've explicitly set it through the `hasError` input.\n    return this._stepperOptions.showError ?? this._customError != null;\n  }\n}\n\n@Directive({\n  selector: '[cdkStepper]',\n  exportAs: 'cdkStepper',\n})\nexport class CdkStepper implements AfterContentInit, AfterViewInit, OnDestroy {\n  private _dir = inject(Directionality, {optional: true});\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /** Emits when the component is destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /** Used for managing keyboard focus. */\n  private _keyManager: FocusKeyManager<FocusableOption>;\n\n  /** Full list of steps inside the stepper, including inside nested steppers. */\n  @ContentChildren(CdkStep, {descendants: true}) _steps: QueryList<CdkStep>;\n\n  /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n  readonly steps: QueryList<CdkStep> = new QueryList<CdkStep>();\n\n  /** The list of step headers of the steps in the stepper. */\n  @ContentChildren(CdkStepHeader, {descendants: true}) _stepHeader: QueryList<CdkStepHeader>;\n\n  /** List of step headers sorted based on their DOM order. */\n  private _sortedHeaders = new QueryList<CdkStepHeader>();\n\n  /** Whether the validity of previous steps should be checked or not. */\n  @Input({transform: booleanAttribute}) linear: boolean = false;\n\n  /** The index of the selected step. */\n  @Input({transform: numberAttribute})\n  get selectedIndex(): number {\n    return this._selectedIndex;\n  }\n  set selectedIndex(index: number) {\n    if (this._steps) {\n      // Ensure that the index can't be out of bounds.\n      if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n      }\n\n      if (this._selectedIndex !== index) {\n        this.selected?._markAsInteracted();\n\n        if (\n          !this._anyControlsInvalidOrPending(index) &&\n          (index >= this._selectedIndex || this.steps.toArray()[index].editable)\n        ) {\n          this._updateSelectedItemIndex(index);\n        }\n      }\n    } else {\n      this._selectedIndex = index;\n    }\n  }\n  private _selectedIndex = 0;\n\n  /** The step that is selected. */\n  @Input()\n  get selected(): CdkStep | undefined {\n    return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n  }\n  set selected(step: CdkStep | undefined) {\n    this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n  }\n\n  /** Event emitted when the selected step has changed. */\n  @Output() readonly selectionChange = new EventEmitter<StepperSelectionEvent>();\n\n  /** Output to support two-way binding on `[(selectedIndex)]` */\n  @Output() readonly selectedIndexChange: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Used to track unique ID for each stepper component. */\n  private _groupId = inject(_IdGenerator).getId('cdk-stepper-');\n\n  /** Orientation of the stepper. */\n  @Input()\n  get orientation(): StepperOrientation {\n    return this._orientation;\n  }\n  set orientation(value: StepperOrientation) {\n    // This is a protected method so that `MatStepper` can hook into it.\n    this._orientation = value;\n\n    if (this._keyManager) {\n      this._keyManager.withVerticalOrientation(value === 'vertical');\n    }\n  }\n  private _orientation: StepperOrientation = 'horizontal';\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterContentInit() {\n    this._steps.changes\n      .pipe(startWith(this._steps), takeUntil(this._destroyed))\n      .subscribe((steps: QueryList<CdkStep>) => {\n        this.steps.reset(steps.filter(step => step._stepper === this));\n        this.steps.notifyOnChanges();\n      });\n  }\n\n  ngAfterViewInit() {\n    // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n    // Material stepper, they won't appear in the `QueryList` in the same order as they're\n    // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n    // them manually to ensure that they're correct. Alternatively, we can change the Material\n    // template to inline the headers in the `ngFor`, but that'll result in a lot of\n    // code duplication. See #23539.\n    this._stepHeader.changes\n      .pipe(startWith(this._stepHeader), takeUntil(this._destroyed))\n      .subscribe((headers: QueryList<CdkStepHeader>) => {\n        this._sortedHeaders.reset(\n          headers.toArray().sort((a, b) => {\n            const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(\n              b._elementRef.nativeElement,\n            );\n\n            // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n            // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n            // tslint:disable-next-line:no-bitwise\n            return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n          }),\n        );\n        this._sortedHeaders.notifyOnChanges();\n      });\n\n    // Note that while the step headers are content children by default, any components that\n    // extend this one might have them as view children. We initialize the keyboard handling in\n    // AfterViewInit so we're guaranteed for both view and content children to be defined.\n    this._keyManager = new FocusKeyManager<FocusableOption>(this._sortedHeaders)\n      .withWrap()\n      .withHomeAndEnd()\n      .withVerticalOrientation(this._orientation === 'vertical');\n\n    (this._dir ? (this._dir.change as Observable<Direction>) : observableOf<Direction>())\n      .pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed))\n      .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n\n    this._keyManager.updateActiveItem(this._selectedIndex);\n\n    // No need to `takeUntil` here, because we're the ones destroying `steps`.\n    this.steps.changes.subscribe(() => {\n      if (!this.selected) {\n        this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n      }\n    });\n\n    // The logic which asserts that the selected index is within bounds doesn't run before the\n    // steps are initialized, because we don't how many steps there are yet so we may have an\n    // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n    if (!this._isValidIndex(this._selectedIndex)) {\n      this._selectedIndex = 0;\n    }\n\n    // For linear step and selected index is greater than zero,\n    // set all the previous steps to interacted so that we can navigate to previous steps.\n    if (this.linear && this._selectedIndex > 0) {\n      const visitedSteps = this.steps.toArray().slice(0, this._selectedIndex);\n\n      for (const step of visitedSteps) {\n        step._markAsInteracted();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this.steps.destroy();\n    this._sortedHeaders.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Selects and focuses the next step in list. */\n  next(): void {\n    this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n  }\n\n  /** Selects and focuses the previous step in list. */\n  previous(): void {\n    this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n  }\n\n  /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n  reset(): void {\n    this._updateSelectedItemIndex(0);\n    this.steps.forEach(step => step.reset());\n    this._stateChanged();\n  }\n\n  /** Returns a unique id for each step label element. */\n  _getStepLabelId(i: number): string {\n    return `${this._groupId}-label-${i}`;\n  }\n\n  /** Returns unique id for each step content element. */\n  _getStepContentId(i: number): string {\n    return `${this._groupId}-content-${i}`;\n  }\n\n  /** Marks the component to be change detected. */\n  _stateChanged() {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Returns position state of the step with the given index. */\n  _getAnimationDirection(index: number): StepContentPositionState {\n    const position = index - this._selectedIndex;\n    if (position < 0) {\n      return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n    } else if (position > 0) {\n      return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n    }\n    return 'current';\n  }\n\n  /** Returns the type of icon to be displayed. */\n  _getIndicatorType(index: number, state: StepState = STEP_STATE.NUMBER): StepState {\n    const step = this.steps.toArray()[index];\n    const isCurrentStep = this._isCurrentStep(index);\n\n    return step._displayDefaultIndicatorType\n      ? this._getDefaultIndicatorLogic(step, isCurrentStep)\n      : this._getGuidelineLogic(step, isCurrentStep, state);\n  }\n\n  private _getDefaultIndicatorLogic(step: CdkStep, isCurrentStep: boolean): StepState {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (!step.completed || isCurrentStep) {\n      return STEP_STATE.NUMBER;\n    } else {\n      return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n    }\n  }\n\n  private _getGuidelineLogic(\n    step: CdkStep,\n    isCurrentStep: boolean,\n    state: StepState = STEP_STATE.NUMBER,\n  ): StepState {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (step.completed && !isCurrentStep) {\n      return STEP_STATE.DONE;\n    } else if (step.completed && isCurrentStep) {\n      return state;\n    } else if (step.editable && isCurrentStep) {\n      return STEP_STATE.EDIT;\n    } else {\n      return state;\n    }\n  }\n\n  private _isCurrentStep(index: number) {\n    return this._selectedIndex === index;\n  }\n\n  /** Returns the index of the currently-focused step header. */\n  _getFocusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n  }\n\n  private _updateSelectedItemIndex(newIndex: number): void {\n    const stepsArray = this.steps.toArray();\n    this.selectionChange.emit({\n      selectedIndex: newIndex,\n      previouslySelectedIndex: this._selectedIndex,\n      selectedStep: stepsArray[newIndex],\n      previouslySelectedStep: stepsArray[this._selectedIndex],\n    });\n\n    // If focus is inside the stepper, move it to the next header, otherwise it may become\n    // lost when the active step content is hidden. We can't be more granular with the check\n    // (e.g. checking whether focus is inside the active step), because we don't have a\n    // reference to the elements that are rendering out the content.\n    this._containsFocus()\n      ? this._keyManager.setActiveItem(newIndex)\n      : this._keyManager.updateActiveItem(newIndex);\n\n    this._selectedIndex = newIndex;\n    this.selectedIndexChange.emit(this._selectedIndex);\n    this._stateChanged();\n  }\n\n  _onKeydown(event: KeyboardEvent) {\n    const hasModifier = hasModifierKey(event);\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n\n    if (\n      manager.activeItemIndex != null &&\n      !hasModifier &&\n      (keyCode === SPACE || keyCode === ENTER)\n    ) {\n      this.selectedIndex = manager.activeItemIndex;\n      event.preventDefault();\n    } else {\n      manager.setFocusOrigin('keyboard').onKeydown(event);\n    }\n  }\n\n  private _anyControlsInvalidOrPending(index: number): boolean {\n    if (this.linear && index >= 0) {\n      return this.steps\n        .toArray()\n        .slice(0, index)\n        .some(step => {\n          const control = step.stepControl;\n          const isIncomplete = control\n            ? control.invalid || control.pending || !step.interacted\n            : !step.completed;\n          return isIncomplete && !step.optional && !step._completedOverride;\n        });\n    }\n\n    return false;\n  }\n\n  private _layoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Checks whether the stepper contains the focused element. */\n  private _containsFocus(): boolean {\n    const stepperElement = this._elementRef.nativeElement;\n    const focusedElement = _getFocusedElementPierceShadowDom();\n    return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n  }\n\n  /** Checks whether the passed-in index is a valid step index. */\n  private _isValidIndex(index: number): boolean {\n    return index > -1 && (!this.steps || index < this.steps.length);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, Input, inject} from '@angular/core';\n\nimport {CdkStepper} from './stepper';\n\n/** But<PERSON> that moves to the next step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperNext]',\n  host: {\n    '[type]': 'type',\n    '(click)': '_stepper.next()',\n  },\n})\nexport class CdkStepperNext {\n  _stepper = inject(CdkStepper);\n\n  /** Type of the next button. Defaults to \"submit\" if not specified. */\n  @Input() type: string = 'submit';\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/** Button that moves to the previous step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperPrevious]',\n  host: {\n    '[type]': 'type',\n    '(click)': '_stepper.previous()',\n  },\n})\nexport class CdkStepperPrevious {\n  _stepper = inject(CdkStepper);\n\n  /** Type of the previous button. Defaults to \"button\" if not specified. */\n  @Input() type: string = 'button';\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkStepper, CdkStep} from './stepper';\nimport {CdkStepLabel} from './step-label';\nimport {CdkStepperNext, CdkStepperPrevious} from './stepper-button';\nimport {CdkStepHeader} from './step-header';\nimport {BidiModule} from '@angular/cdk/bidi';\n\n@NgModule({\n  imports: [\n    BidiModule,\n    CdkStep,\n    CdkStepper,\n    CdkStepHeader,\n    CdkStepLabel,\n    CdkStepperNext,\n    CdkStepperPrevious,\n  ],\n  exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n})\nexport class CdkStepperModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;MAiBa,aAAa,CAAA;AACxB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;AAG1D,IAAA,WAAA,GAAA,GAAgB;;IAGhB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACxC;uGATU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBANzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;AACF,iBAAA,CAAA;;;MCHY,YAAY,CAAA;AACvB,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC,CAAC;AAGjD,IAAA,WAAA,GAAA,GAAgB;uGAJL,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA,CAAA;;;AC6CD;MACa,qBAAqB,CAAA;;AAEhC,IAAA,aAAa,CAAS;;AAGtB,IAAA,uBAAuB,CAAS;;AAGhC,IAAA,YAAY,CAAU;;AAGtB,IAAA,sBAAsB,CAAU;AACjC,CAAA;AAKD;AACa,MAAA,UAAU,GAAG;AACxB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,KAAK,EAAE,OAAO;EACd;AAEF;MACa,sBAAsB,GAAG,IAAI,cAAc,CAAiB,wBAAwB,EAAE;MAyBtF,OAAO,CAAA;AACV,IAAA,eAAe,CAAiB;AACxC,IAAA,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AAC9B,IAAA,4BAA4B,CAAU;;AAGV,IAAA,SAAS,CAAe;;AAa1C,IAAA,WAAW,CAA8D;;AAG3C,IAAA,OAAO,CAAmB;;AAGzD,IAAA,WAAW,CAAkB;;IAGtC,UAAU,GAAG,KAAK,CAAC;;AAIV,IAAA,gBAAgB,GAA0B,IAAI,YAAY,EAAW,CAAC;;AAGtE,IAAA,KAAK,CAAS;;AAGd,IAAA,YAAY,CAAS;;AAGT,IAAA,SAAS,CAAS;AAEvC;;;AAGG;AACuB,IAAA,cAAc,CAAS;;AAGxC,IAAA,KAAK,CAAY;;IAGY,QAAQ,GAAY,IAAI,CAAC;;IAGzB,QAAQ,GAAY,KAAK,CAAC;;AAGhE,IAAA,IACI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;KAChG;IACD,IAAI,SAAS,CAAC,KAAc,EAAA;AAC1B,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KACjC;IACD,kBAAkB,GAAmB,IAAI,CAAC;IAElC,oBAAoB,GAAA;QAC1B,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;KACvF;;AAGD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC;KAChF;IACD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;KAC3B;IACO,YAAY,GAAmB,IAAI,CAAC;IAEpC,gBAAgB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC;KACxE;AAID,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,cAAc,GAAG,MAAM,CAAiB,sBAAsB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AACxF,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,EAAE,CAAC;QAC5D,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,eAAe,CAAC,2BAA2B,KAAK,KAAK,CAAC;KAChG;;IAGD,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;KAC/B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAExB,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,EAAE;AACnC,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACjC;AAED,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;AAC7B,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;SAC3B;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;;AAIpB,YAAA,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AACtD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC1B;KACF;IAED,WAAW,GAAA;;;AAGT,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;KAC/B;IAED,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClC;KACF;;IAGD,UAAU,GAAA;;;QAGR,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;KACpE;uGA1IU,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAqDC,gBAAgB,CAGhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,yCAGhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAchB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAnErB,YAAY,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA;;;;;gBAQxB,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAQP,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EA1BZ,0CAA0C,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAIzC,OAAO,EAAA,UAAA,EAAA,CAAA;kBAPnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,0CAA0C;oBACpD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAChD,iBAAA,CAAA;wDAO6B,SAAS,EAAA,CAAA;sBAApC,YAAY;uBAAC,YAAY,CAAA;gBAahB,WAAW,EAAA,CAAA;sBAVpB,eAAe;;;;;;oBAKd,gBAAgB;AAChB,oBAAA;AACE,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA,CAAA;gBAKqC,OAAO,EAAA,CAAA;sBAA9C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBAG7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAOG,gBAAgB,EAAA,CAAA;sBADxB,MAAM;uBAAC,YAAY,CAAA;gBAIX,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAGe,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY,CAAA;gBAMO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB,CAAA;gBAGf,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAGgC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAGE,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIhC,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAehC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;MAwEzB,UAAU,CAAA;IACb,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAChD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAC7C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC;;AAGjD,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAG5C,IAAA,WAAW,CAAmC;;AAGP,IAAA,MAAM,CAAqB;;AAGjE,IAAA,KAAK,GAAuB,IAAI,SAAS,EAAW,CAAC;;AAGT,IAAA,WAAW,CAA2B;;AAGnF,IAAA,cAAc,GAAG,IAAI,SAAS,EAAiB,CAAC;;IAGlB,MAAM,GAAY,KAAK,CAAC;;AAG9D,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IACD,IAAI,aAAa,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;;AAEf,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACjF,gBAAA,MAAM,KAAK,CAAC,mEAAmE,CAAC,CAAC;aAClF;AAED,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;AACjC,gBAAA,IAAI,CAAC,QAAQ,EAAE,iBAAiB,EAAE,CAAC;AAEnC,gBAAA,IACE,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;AACzC,qBAAC,KAAK,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EACtE;AACA,oBAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;iBACtC;aACF;SACF;aAAM;AACL,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;SAC7B;KACF;IACO,cAAc,GAAG,CAAC,CAAC;;AAG3B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;KAC1E;IACD,IAAI,QAAQ,CAAC,IAAyB,EAAA;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACnF;;AAGkB,IAAA,eAAe,GAAG,IAAI,YAAY,EAAyB,CAAC;;AAG5D,IAAA,mBAAmB,GAAyB,IAAI,YAAY,EAAU,CAAC;;IAGlF,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;;AAG9D,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IACD,IAAI,WAAW,CAAC,KAAyB,EAAA;;AAEvC,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC;SAChE;KACF;IACO,YAAY,GAAuB,YAAY,CAAC;AAGxD,IAAA,WAAA,GAAA,GAAgB;IAEhB,kBAAkB,GAAA;QAChB,IAAI,CAAC,MAAM,CAAC,OAAO;AAChB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxD,aAAA,SAAS,CAAC,CAAC,KAAyB,KAAI;YACvC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;AAC/B,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;;;;;;;QAOb,IAAI,CAAC,WAAW,CAAC,OAAO;AACrB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC7D,aAAA,SAAS,CAAC,CAAC,OAAiC,KAAI;AAC/C,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CACvB,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AAC9B,gBAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAC1E,CAAC,CAAC,WAAW,CAAC,aAAa,CAC5B,CAAC;;;;AAKF,gBAAA,OAAO,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aACrE,CAAC,CACH,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;AACxC,SAAC,CAAC,CAAC;;;;QAKL,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAkB,IAAI,CAAC,cAAc,CAAC;AACzE,aAAA,QAAQ,EAAE;AACV,aAAA,cAAc,EAAE;AAChB,aAAA,uBAAuB,CAAC,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC,CAAC;AAE7D,QAAA,CAAC,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,MAAgC,GAAGA,EAAY,EAAa;AACjF,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACpE,aAAA,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,CAAC;QAEjF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;QAGvD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;AAChC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5D;AACH,SAAC,CAAC,CAAC;;;;QAKH,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;SACzB;;;QAID,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AAC1C,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAExE,YAAA,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;gBAC/B,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC1B;SACF;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;IAGD,IAAI,GAAA;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC/E;;IAGD,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;KAC3D;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;AAGD,IAAA,eAAe,CAAC,CAAS,EAAA;AACvB,QAAA,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAU,OAAA,EAAA,CAAC,EAAE,CAAC;KACtC;;AAGD,IAAA,iBAAiB,CAAC,CAAS,EAAA;AACzB,QAAA,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAY,SAAA,EAAA,CAAC,EAAE,CAAC;KACxC;;IAGD,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;;AAGD,IAAA,sBAAsB,CAAC,KAAa,EAAA;AAClC,QAAA,MAAM,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;AAC7C,QAAA,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChB,YAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK,KAAK,GAAG,MAAM,GAAG,UAAU,CAAC;SAChE;AAAM,aAAA,IAAI,QAAQ,GAAG,CAAC,EAAE;AACvB,YAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC;SAChE;AACD,QAAA,OAAO,SAAS,CAAC;KAClB;;AAGD,IAAA,iBAAiB,CAAC,KAAa,EAAE,KAAmB,GAAA,UAAU,CAAC,MAAM,EAAA;QACnE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC,4BAA4B;cACpC,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,aAAa,CAAC;cACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;KACzD;IAEO,yBAAyB,CAAC,IAAa,EAAE,aAAsB,EAAA;AACrE,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxD,OAAO,UAAU,CAAC,KAAK,CAAC;SACzB;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,aAAa,EAAE;YAC3C,OAAO,UAAU,CAAC,MAAM,CAAC;SAC1B;aAAM;AACL,YAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;SAC1D;KACF;IAEO,kBAAkB,CACxB,IAAa,EACb,aAAsB,EACtB,KAAmB,GAAA,UAAU,CAAC,MAAM,EAAA;AAEpC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxD,OAAO,UAAU,CAAC,KAAK,CAAC;SACzB;AAAM,aAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,aAAa,EAAE;YAC3C,OAAO,UAAU,CAAC,IAAI,CAAC;SACxB;AAAM,aAAA,IAAI,IAAI,CAAC,SAAS,IAAI,aAAa,EAAE;AAC1C,YAAA,OAAO,KAAK,CAAC;SACd;AAAM,aAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,EAAE;YACzC,OAAO,UAAU,CAAC,IAAI,CAAC;SACxB;aAAM;AACL,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AAEO,IAAA,cAAc,CAAC,KAAa,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC;KACtC;;IAGD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;KAClF;AAEO,IAAA,wBAAwB,CAAC,QAAgB,EAAA;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,YAAA,aAAa,EAAE,QAAQ;YACvB,uBAAuB,EAAE,IAAI,CAAC,cAAc;AAC5C,YAAA,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC;AAClC,YAAA,sBAAsB,EAAE,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;AACxD,SAAA,CAAC,CAAC;;;;;QAMH,IAAI,CAAC,cAAc,EAAE;cACjB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC;cACxC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAEhD,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC7B,QAAA,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1C,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AAEjC,QAAA,IACE,OAAO,CAAC,eAAe,IAAI,IAAI;AAC/B,YAAA,CAAC,WAAW;aACX,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC,EACxC;AACA,YAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC;YAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;aAAM;YACL,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACrD;KACF;AAEO,IAAA,4BAA4B,CAAC,KAAa,EAAA;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,KAAK;AACd,iBAAA,OAAO,EAAE;AACT,iBAAA,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;iBACf,IAAI,CAAC,IAAI,IAAG;AACX,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjC,MAAM,YAAY,GAAG,OAAO;AAC1B,sBAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;AACxD,sBAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;AACpE,aAAC,CAAC,CAAC;SACN;AAED,QAAA,OAAO,KAAK,CAAC;KACd;IAEO,gBAAgB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;KAC/D;;IAGO,cAAc,GAAA;AACpB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AACtD,QAAA,MAAM,cAAc,GAAG,iCAAiC,EAAE,CAAC;QAC3D,OAAO,cAAc,KAAK,cAAc,IAAI,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;KACrF;;AAGO,IAAA,aAAa,CAAC,KAAa,EAAA;AACjC,QAAA,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KACjE;uGA3UU,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,uFAwBF,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAGhB,eAAe,CAfjB,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,OAAO,iEAMP,aAAa,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAlBnB,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACvB,iBAAA,CAAA;wDAagD,MAAM,EAAA,CAAA;sBAApD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAMQ,WAAW,EAAA,CAAA;sBAA/D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;gBAMb,MAAM,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAIhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBA6B/B,QAAQ,EAAA,CAAA;sBADX,KAAK;gBASa,eAAe,EAAA,CAAA;sBAAjC,MAAM;gBAGY,mBAAmB,EAAA,CAAA;sBAArC,MAAM;gBAOH,WAAW,EAAA,CAAA;sBADd,KAAK;;;AC3TR;MAQa,cAAc,CAAA;AACzB,IAAA,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;;IAGrB,IAAI,GAAW,QAAQ,CAAC;AAGjC,IAAA,WAAA,GAAA,GAAgB;uGAPL,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAP1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,QAAQ,EAAE,MAAM;AAChB,wBAAA,SAAS,EAAE,iBAAiB;AAC7B,qBAAA;AACF,iBAAA,CAAA;wDAKU,IAAI,EAAA,CAAA;sBAAZ,KAAK;;AAMR;MAQa,kBAAkB,CAAA;AAC7B,IAAA,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;;IAGrB,IAAI,GAAW,QAAQ,CAAC;AAGjC,IAAA,WAAA,GAAA,GAAgB;uGAPL,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4BAA4B;AACtC,oBAAA,IAAI,EAAE;AACJ,wBAAA,QAAQ,EAAE,MAAM;AAChB,wBAAA,SAAS,EAAE,qBAAqB;AACjC,qBAAA;AACF,iBAAA,CAAA;wDAKU,IAAI,EAAA,CAAA;sBAAZ,KAAK;;;MCfK,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAVzB,UAAU;YACV,OAAO;YACP,UAAU;YACV,aAAa;YACb,YAAY;YACZ,cAAc;YACd,kBAAkB,CAAA,EAAA,OAAA,EAAA,CAEV,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAA,EAAA,CAAA,CAAA;AAEnF,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAVzB,UAAU,CAAA,EAAA,CAAA,CAAA;;2FAUD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAZ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,YAAY;wBACZ,cAAc;wBACd,kBAAkB;AACnB,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAC;AAChG,iBAAA,CAAA;;;AC1BD;;AAEG;;;;"}