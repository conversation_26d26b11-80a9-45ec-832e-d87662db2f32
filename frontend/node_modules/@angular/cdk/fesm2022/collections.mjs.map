{"version": 3, "file": "collections.mjs", "sources": ["../../../../../../src/cdk/collections/data-source.ts", "../../../../../../src/cdk/collections/array-data-source.ts", "../../../../../../src/cdk/collections/view-repeater.ts", "../../../../../../src/cdk/collections/dispose-view-repeater-strategy.ts", "../../../../../../src/cdk/collections/recycle-view-repeater-strategy.ts", "../../../../../../src/cdk/collections/selection-model.ts", "../../../../../../src/cdk/collections/unique-selection-dispatcher.ts", "../../../../../../src/cdk/collections/collections_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ConnectableObservable, Observable} from 'rxjs';\nimport {CollectionViewer} from './collection-viewer';\n\nexport abstract class DataSource<T> {\n  /**\n   * Connects a collection viewer (such as a data-table) to this data source. Note that\n   * the stream provided will be accessed during change detection and should not directly change\n   * values that are bound in template views.\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   * @returns Observable that emits a new value when the data changes.\n   */\n  abstract connect(collectionViewer: CollectionViewer): Observable<readonly T[]>;\n\n  /**\n   * Disconnects a collection viewer (such as a data-table) from this data source. Can be used\n   * to perform any clean-up or tear-down operations when a view is being destroyed.\n   *\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   */\n  abstract disconnect(collectionViewer: CollectionViewer): void;\n}\n\n/** Checks whether an object is a data source. */\nexport function isDataSource(value: any): value is DataSource<any> {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Observable, isObservable, of as observableOf} from 'rxjs';\nimport {DataSource} from './data-source';\n\n/** DataSource wrapper for a native array. */\nexport class ArrayDataSource<T> extends DataSource<T> {\n  constructor(private _data: readonly T[] | Observable<readonly T[]>) {\n    super();\n  }\n\n  connect(): Observable<readonly T[]> {\n    return isObservable(this._data) ? this._data : observableOf(this._data);\n  }\n\n  disconnect() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  InjectionToken,\n  IterableChangeRecord,\n  IterableChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * The context for an embedded view in the repeater's view container.\n *\n * @template T The type for the embedded view's $implicit property.\n */\nexport interface _ViewRepeaterItemContext<T> {\n  $implicit?: T;\n}\n\n/**\n * The arguments needed to construct an embedded view for an item in a view\n * container.\n *\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeaterItemInsertArgs<C> {\n  templateRef: TemplateRef<C>;\n  context?: C;\n  index?: number;\n}\n\n/**\n * A factory that derives the embedded view context for an item in a view\n * container.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport type _ViewRepeaterItemContextFactory<T, R, C extends _ViewRepeaterItemContext<T>> = (\n  record: IterableChangeRecord<R>,\n  adjustedPreviousIndex: number | null,\n  currentIndex: number | null,\n) => _ViewRepeaterItemInsertArgs<C>;\n\n/**\n * Extracts the value of an item from an {@link IterableChangeRecord}.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n */\nexport type _ViewRepeaterItemValueResolver<T, R> = (record: IterableChangeRecord<R>) => T;\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nexport enum _ViewRepeaterOperation {\n  /** The content of an existing view was replaced with another item. */\n  REPLACED,\n  /** A new view was created with `createEmbeddedView`. */\n  INSERTED,\n  /** The position of a view changed, but the content remains the same. */\n  MOVED,\n  /** A view was detached from the view container. */\n  REMOVED,\n}\n\n/**\n * Meta data describing the state of a view after it was updated by a\n * {@link _ViewRepeater}.\n *\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeaterItemChange<R, C> {\n  /** The view's context after it was changed. */\n  context?: C;\n  /** Indicates how the view was changed. */\n  operation: _ViewRepeaterOperation;\n  /** The view's corresponding change record. */\n  record: IterableChangeRecord<R>;\n}\n\n/**\n * Type for a callback to be executed after a view has changed.\n *\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport type _ViewRepeaterItemChanged<R, C> = (change: _ViewRepeaterItemChange<R, C>) => void;\n\n/**\n * Describes a strategy for rendering items in a {@link ViewContainerRef}.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeater<T, R, C extends _ViewRepeaterItemContext<T>> {\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ): void;\n\n  detach(): void;\n}\n\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nexport const _VIEW_REPEATER_STRATEGY = new InjectionToken<\n  _ViewRepeater<unknown, unknown, _ViewRepeaterItemContext<unknown>>\n>('_ViewRepeater');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EmbeddedViewRef,\n  IterableChangeRecord,\n  IterableChanges,\n  ViewContainerRef,\n} from '@angular/core';\nimport {\n  _ViewRepeater,\n  _ViewRepeaterItemChanged,\n  _ViewRepeaterItemContext,\n  _ViewRepeaterItemContextFactory,\n  _ViewRepeaterItemValueResolver,\n  _ViewRepeaterOperation,\n} from './view-repeater';\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport class _DisposeViewRepeaterStrategy<T, R, C extends _ViewRepeaterItemContext<T>>\n  implements _ViewRepeater<T, R, C>\n{\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ) {\n    changes.forEachOperation(\n      (\n        record: IterableChangeRecord<R>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        let view: EmbeddedViewRef<C> | undefined;\n        let operation: _ViewRepeaterOperation;\n        if (record.previousIndex == null) {\n          const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n          view = viewContainerRef.createEmbeddedView(\n            insertContext.templateRef,\n            insertContext.context,\n            insertContext.index,\n          );\n          operation = _ViewRepeaterOperation.INSERTED;\n        } else if (currentIndex == null) {\n          viewContainerRef.remove(adjustedPreviousIndex!);\n          operation = _ViewRepeaterOperation.REMOVED;\n        } else {\n          view = viewContainerRef.get(adjustedPreviousIndex!) as EmbeddedViewRef<C>;\n          viewContainerRef.move(view!, currentIndex);\n          operation = _ViewRepeaterOperation.MOVED;\n        }\n\n        if (itemViewChanged) {\n          itemViewChanged({\n            context: view?.context,\n            operation,\n            record,\n          });\n        }\n      },\n    );\n  }\n\n  detach() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EmbeddedViewRef,\n  IterableChangeRecord,\n  IterableChanges,\n  ViewContainerRef,\n} from '@angular/core';\nimport {\n  _ViewRepeater,\n  _ViewRepeaterItemChanged,\n  _ViewRepeaterItemContext,\n  _ViewRepeaterItemContextFactory,\n  _ViewRepeaterItemInsertArgs,\n  _ViewRepeaterItemValueResolver,\n  _ViewRepeaterOperation,\n} from './view-repeater';\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport class _RecycleViewRepeaterStrategy<T, R, C extends _ViewRepeaterItemContext<T>>\n  implements _ViewRepeater<T, R, C>\n{\n  /**\n   * The size of the cache used to store unused views.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n   */\n  viewCacheSize: number = 20;\n\n  /**\n   * View cache that stores embedded view instances that have been previously stamped out,\n   * but don't are not currently rendered. The view repeater will reuse these views rather than\n   * creating brand new ones.\n   *\n   * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n   */\n  private _viewCache: EmbeddedViewRef<C>[] = [];\n\n  /** Apply changes to the DOM. */\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation(\n      (\n        record: IterableChangeRecord<R>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        let view: EmbeddedViewRef<C> | undefined;\n        let operation: _ViewRepeaterOperation;\n        if (record.previousIndex == null) {\n          // Item added.\n          const viewArgsFactory = () =>\n            itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n          view = this._insertView(\n            viewArgsFactory,\n            currentIndex!,\n            viewContainerRef,\n            itemValueResolver(record),\n          );\n          operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n        } else if (currentIndex == null) {\n          // Item removed.\n          this._detachAndCacheView(adjustedPreviousIndex!, viewContainerRef);\n          operation = _ViewRepeaterOperation.REMOVED;\n        } else {\n          // Item moved.\n          view = this._moveView(\n            adjustedPreviousIndex!,\n            currentIndex!,\n            viewContainerRef,\n            itemValueResolver(record),\n          );\n          operation = _ViewRepeaterOperation.MOVED;\n        }\n\n        if (itemViewChanged) {\n          itemViewChanged({\n            context: view?.context,\n            operation,\n            record,\n          });\n        }\n      },\n    );\n  }\n\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  private _insertView(\n    viewArgsFactory: () => _ViewRepeaterItemInsertArgs<C>,\n    currentIndex: number,\n    viewContainerRef: ViewContainerRef,\n    value: T,\n  ): EmbeddedViewRef<C> | undefined {\n    const cachedView = this._insertViewFromCache(currentIndex!, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(\n      viewArgs.templateRef,\n      viewArgs.context,\n      viewArgs.index,\n    );\n  }\n\n  /** Detaches the view at the given index and inserts into the view cache. */\n  private _detachAndCacheView(index: number, viewContainerRef: ViewContainerRef) {\n    const detachedView = viewContainerRef.detach(index) as EmbeddedViewRef<C>;\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n\n  /** Moves view at the previous index to the current index. */\n  private _moveView(\n    adjustedPreviousIndex: number,\n    currentIndex: number,\n    viewContainerRef: ViewContainerRef,\n    value: T,\n  ): EmbeddedViewRef<C> {\n    const view = viewContainerRef.get(adjustedPreviousIndex!) as EmbeddedViewRef<C>;\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  private _maybeCacheView(view: EmbeddedViewRef<C>, viewContainerRef: ViewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n\n  /** Inserts a recycled view from the cache at the given index. */\n  private _insertViewFromCache(\n    index: number,\n    viewContainerRef: ViewContainerRef,\n  ): EmbeddedViewRef<C> | null {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Subject} from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nexport class SelectionModel<T> {\n  /** Currently-selected values. */\n  private _selection = new Set<T>();\n\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  private _deselectedToEmit: T[] = [];\n\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  private _selectedToEmit: T[] = [];\n\n  /** Cache for the array value of the selected items. */\n  private _selected: T[] | null;\n\n  /** Selected values. */\n  get selected(): T[] {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n\n    return this._selected;\n  }\n\n  /** Event emitted when the value has changed. */\n  readonly changed = new Subject<SelectionChange<T>>();\n\n  constructor(\n    private _multiple = false,\n    initiallySelectedValues?: T[],\n    private _emitChanges = true,\n    public compareWith?: (o1: T, o2: T) => boolean,\n  ) {\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  select(...values: T[]): boolean | void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  deselect(...values: T[]): boolean | void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  setSelection(...values: T[]): boolean | void {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n    values.forEach(value => this._markSelected(value));\n    oldValues\n      .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n      .forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  toggle(value: T): boolean | void {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  clear(flushEvent = true): boolean | void {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value: T): boolean {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty(): boolean {\n    return this._selection.size === 0;\n  }\n\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue(): boolean {\n    return !this.isEmpty();\n  }\n\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate?: (a: T, b: T) => number): void {\n    if (this._multiple && this.selected) {\n      this._selected!.sort(predicate);\n    }\n  }\n\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n\n  /** Emits a change event and clears the records of selected and deselected values. */\n  private _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit,\n      });\n\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n\n  /** Selects a value. */\n  private _markSelected(value: T) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Deselects a value. */\n  private _unmarkSelected(value: T) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Clears out the selected values. */\n  private _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  private _verifyValueAssignment(values: T[]) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n\n  /** Whether there are queued up change to be emitted. */\n  private _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  private _getConcreteValue(inputValue: T, selection?: Set<T>): T {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection ?? this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith!(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n\n/**\n * Event emitted when the value of a MatSelectionModel has changed.\n * @docs-private\n */\nexport interface SelectionChange<T> {\n  /** Model that dispatched the event. */\n  source: SelectionModel<T>;\n  /** Options that were added to the model. */\n  added: T[];\n  /** Options that were removed from the model. */\n  removed: T[];\n}\n\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nexport function getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, OnDestroy} from '@angular/core';\n\n// Users of the Dispatcher never need to see this type, but TypeScript requires it to be exported.\nexport type UniqueSelectionDispatcherListener = (id: string, name: string) => void;\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\n@Injectable({providedIn: 'root'})\nexport class UniqueSelectionDispatcher implements OnDestroy {\n  private _listeners: UniqueSelectionDispatcherListener[] = [];\n\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id: string, name: string) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener: UniqueSelectionDispatcherListener): () => void {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter((registered: UniqueSelectionDispatcherListener) => {\n        return listener !== registered;\n      });\n    };\n  }\n\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf"], "mappings": ";;;;MAWsB,UAAU,CAAA;AAmB/B,CAAA;AAED;AACM,SAAU,YAAY,CAAC,KAAU,EAAA;;;;;AAKrC,IAAA,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,EAAE,KAAK,YAAY,qBAAqB,CAAC,CAAC;AACnG;;AC5BA;AACM,MAAO,eAAmB,SAAQ,UAAa,CAAA;AAC/B,IAAA,KAAA,CAAA;AAApB,IAAA,WAAA,CAAoB,KAA8C,EAAA;AAChE,QAAA,KAAK,EAAE,CAAC;QADU,IAAK,CAAA,KAAA,GAAL,KAAK,CAAyC;KAEjE;IAED,OAAO,GAAA;QACL,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAGA,EAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzE;AAED,IAAA,UAAU,MAAK;AAChB;;ACqCD;IACY,uBASX;AATD,CAAA,UAAY,sBAAsB,EAAA;;AAEhC,IAAA,sBAAA,CAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;;AAER,IAAA,sBAAA,CAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;;AAER,IAAA,sBAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;;AAEL,IAAA,sBAAA,CAAA,sBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACT,CAAC,EATW,sBAAsB,KAAtB,sBAAsB,GASjC,EAAA,CAAA,CAAA,CAAA;AA6CD;;;AAGG;MACU,uBAAuB,GAAG,IAAI,cAAc,CAEvD,eAAe;;ACjGjB;;;;;;;;AAQG;MACU,4BAA4B,CAAA;IAGvC,YAAY,CACV,OAA2B,EAC3B,gBAAkC,EAClC,kBAA4D,EAC5D,iBAAuD,EACvD,eAAgD,EAAA;QAEhD,OAAO,CAAC,gBAAgB,CACtB,CACE,MAA+B,EAC/B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAoC,CAAC;AACzC,YAAA,IAAI,SAAiC,CAAC;AACtC,YAAA,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;gBAChC,MAAM,aAAa,GAAG,kBAAkB,CAAC,MAAM,EAAE,qBAAqB,EAAE,YAAY,CAAC,CAAC;AACtF,gBAAA,IAAI,GAAG,gBAAgB,CAAC,kBAAkB,CACxC,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,KAAK,CACpB,CAAC;AACF,gBAAA,SAAS,GAAG,sBAAsB,CAAC,QAAQ,CAAC;aAC7C;AAAM,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,gBAAgB,CAAC,MAAM,CAAC,qBAAsB,CAAC,CAAC;AAChD,gBAAA,SAAS,GAAG,sBAAsB,CAAC,OAAO,CAAC;aAC5C;iBAAM;AACL,gBAAA,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,qBAAsB,CAAuB,CAAC;AAC1E,gBAAA,gBAAgB,CAAC,IAAI,CAAC,IAAK,EAAE,YAAY,CAAC,CAAC;AAC3C,gBAAA,SAAS,GAAG,sBAAsB,CAAC,KAAK,CAAC;aAC1C;YAED,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC;oBACd,OAAO,EAAE,IAAI,EAAE,OAAO;oBACtB,SAAS;oBACT,MAAM;AACP,iBAAA,CAAC,CAAC;aACJ;AACH,SAAC,CACF,CAAC;KACH;AAED,IAAA,MAAM,MAAK;AACZ;;ACvDD;;;;;;;;;;AAUG;MACU,4BAA4B,CAAA;AAGvC;;;AAGG;IACH,aAAa,GAAW,EAAE,CAAC;AAE3B;;;;;;AAMG;IACK,UAAU,GAAyB,EAAE,CAAC;;IAG9C,YAAY,CACV,OAA2B,EAC3B,gBAAkC,EAClC,kBAA4D,EAC5D,iBAAuD,EACvD,eAAgD,EAAA;;QAGhD,OAAO,CAAC,gBAAgB,CACtB,CACE,MAA+B,EAC/B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAoC,CAAC;AACzC,YAAA,IAAI,SAAiC,CAAC;AACtC,YAAA,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;;AAEhC,gBAAA,MAAM,eAAe,GAAG,MACtB,kBAAkB,CAAC,MAAM,EAAE,qBAAqB,EAAE,YAAY,CAAC,CAAC;AAClE,gBAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CACrB,eAAe,EACf,YAAa,EACb,gBAAgB,EAChB,iBAAiB,CAAC,MAAM,CAAC,CAC1B,CAAC;AACF,gBAAA,SAAS,GAAG,IAAI,GAAG,sBAAsB,CAAC,QAAQ,GAAG,sBAAsB,CAAC,QAAQ,CAAC;aACtF;AAAM,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;;AAE/B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,qBAAsB,EAAE,gBAAgB,CAAC,CAAC;AACnE,gBAAA,SAAS,GAAG,sBAAsB,CAAC,OAAO,CAAC;aAC5C;iBAAM;;AAEL,gBAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CACnB,qBAAsB,EACtB,YAAa,EACb,gBAAgB,EAChB,iBAAiB,CAAC,MAAM,CAAC,CAC1B,CAAC;AACF,gBAAA,SAAS,GAAG,sBAAsB,CAAC,KAAK,CAAC;aAC1C;YAED,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC;oBACd,OAAO,EAAE,IAAI,EAAE,OAAO;oBACtB,SAAS;oBACT,MAAM;AACP,iBAAA,CAAC,CAAC;aACJ;AACH,SAAC,CACF,CAAC;KACH;IAED,MAAM,GAAA;AACJ,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;AAED;;;AAGG;AACK,IAAA,WAAW,CACjB,eAAqD,EACrD,YAAoB,EACpB,gBAAkC,EAClC,KAAQ,EAAA;QAER,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAa,EAAE,gBAAgB,CAAC,CAAC;QAC9E,IAAI,UAAU,EAAE;AACd,YAAA,UAAU,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;AACrC,YAAA,OAAO,SAAS,CAAC;SAClB;AAED,QAAA,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;AACnC,QAAA,OAAO,gBAAgB,CAAC,kBAAkB,CACxC,QAAQ,CAAC,WAAW,EACpB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,KAAK,CACf,CAAC;KACH;;IAGO,mBAAmB,CAAC,KAAa,EAAE,gBAAkC,EAAA;QAC3E,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAuB,CAAC;AAC1E,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;KACtD;;AAGO,IAAA,SAAS,CACf,qBAA6B,EAC7B,YAAoB,EACpB,gBAAkC,EAClC,KAAQ,EAAA;QAER,MAAM,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,qBAAsB,CAAuB,CAAC;AAChF,QAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACK,eAAe,CAAC,IAAwB,EAAE,gBAAkC,EAAA;QAClF,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;AAC/C,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5B;aAAM;YACL,MAAM,KAAK,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;;;;AAM7C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;iBAAM;AACL,gBAAA,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAChC;SACF;KACF;;IAGO,oBAAoB,CAC1B,KAAa,EACb,gBAAkC,EAAA;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QACzC,IAAI,UAAU,EAAE;AACd,YAAA,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,UAAU,IAAI,IAAI,CAAC;KAC3B;AACF;;ACpLD;;AAEG;MACU,cAAc,CAAA;AA0Bf,IAAA,SAAA,CAAA;AAEA,IAAA,YAAA,CAAA;AACD,IAAA,WAAA,CAAA;;AA3BD,IAAA,UAAU,GAAG,IAAI,GAAG,EAAK,CAAC;;IAG1B,iBAAiB,GAAQ,EAAE,CAAC;;IAG5B,eAAe,GAAQ,EAAE,CAAC;;AAG1B,IAAA,SAAS,CAAa;;AAG9B,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AAGQ,IAAA,OAAO,GAAG,IAAI,OAAO,EAAsB,CAAC;IAErD,WACU,CAAA,SAAA,GAAY,KAAK,EACzB,uBAA6B,EACrB,YAAe,GAAA,IAAI,EACpB,WAAuC,EAAA;QAHtC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QAEjB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAO;QACpB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA4B;AAE9C,QAAA,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,MAAM,EAAE;YAC7D,IAAI,SAAS,EAAE;AACb,gBAAA,uBAAuB,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;aACrE;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;aAChD;;AAGD,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;SACjC;KACF;AAED;;;;;AAKG;IACH,MAAM,CAAC,GAAG,MAAW,EAAA;AACnB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACnD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;AAKG;IACH,QAAQ,CAAC,GAAG,MAAW,EAAA;AACrB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;AAKG;IACH,YAAY,CAAC,GAAG,MAAW,EAAA;AACzB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnF,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QACnD,SAAS;AACN,aAAA,MAAM,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;AACnF,aAAA,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,KAAQ,EAAA;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC3E;AAED;;;;;;AAMG;IACH,KAAK,CAAC,UAAU,GAAG,IAAI,EAAA;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;AAEG;AACH,IAAA,UAAU,CAAC,KAAQ,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC3D;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC;KACnC;AAED;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;KACxB;AAED;;AAEG;AACH,IAAA,IAAI,CAAC,SAAkC,EAAA;QACrC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,SAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACjC;KACF;AAED;;AAEG;IACH,mBAAmB,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;IAGO,gBAAgB,GAAA;;AAEtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAChE,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI,CAAC,eAAe;gBAC3B,OAAO,EAAE,IAAI,CAAC,iBAAiB;AAChC,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC3B;KACF;;AAGO,IAAA,aAAa,CAAC,KAAQ,EAAA;AAC5B,QAAA,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,UAAU,EAAE,CAAC;aACnB;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,gBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAC5B;AAED,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClC;SACF;KACF;;AAGO,IAAA,eAAe,CAAC,KAAQ,EAAA;AAC9B,QAAA,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACtC,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAE9B,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpC;SACF;KACF;;IAGO,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;SAC/D;KACF;AAED;;;AAGG;AACK,IAAA,sBAAsB,CAAC,MAAW,EAAA;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC3F,MAAM,uCAAuC,EAAE,CAAC;SACjD;KACF;;IAGO,iBAAiB,GAAA;AACvB,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KACzE;;IAGO,iBAAiB,CAAC,UAAa,EAAE,SAAkB,EAAA;AACzD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,OAAO,UAAU,CAAC;SACnB;aAAM;AACL,YAAA,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;AACzC,YAAA,KAAK,IAAI,aAAa,IAAI,SAAS,EAAE;gBACnC,IAAI,IAAI,CAAC,WAAY,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE;AAChD,oBAAA,OAAO,aAAa,CAAC;iBACtB;aACF;AACD,YAAA,OAAO,UAAU,CAAC;SACnB;KACF;AACF,CAAA;AAeD;;;;AAIG;SACa,uCAAuC,GAAA;AACrD,IAAA,OAAO,KAAK,CAAC,yEAAyE,CAAC,CAAC;AAC1F;;AClQA;;;;;;;;AAQG;MAEU,yBAAyB,CAAA;IAC5B,UAAU,GAAwC,EAAE,CAAC;AAE7D;;;;AAIG;IACH,MAAM,CAAC,EAAU,EAAE,IAAY,EAAA;AAC7B,QAAA,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;AACpC,YAAA,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SACpB;KACF;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,QAA2C,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/B,QAAA,OAAO,MAAK;AACV,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,UAA6C,KAAI;gBACzF,OAAO,QAAQ,KAAK,UAAU,CAAC;AACjC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;KACH;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;uGA7BU,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAzB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,cADb,MAAM,EAAA,CAAA,CAAA;;2FAClB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACtBhC;;AAEG;;;;"}