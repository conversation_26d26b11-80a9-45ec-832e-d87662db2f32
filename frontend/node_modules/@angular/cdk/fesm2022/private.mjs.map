{"version": 3, "file": "private.mjs", "sources": ["../../../../../../src/cdk/private/style-loader.ts", "../../../../../../src/cdk/private/visually-hidden/visually-hidden.ts", "../../../../../../src/cdk/private/private_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ApplicationRef,\n  ComponentRef,\n  createComponent,\n  EnvironmentInjector,\n  inject,\n  Injectable,\n  Injector,\n  Type,\n} from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap<\n  ApplicationRef,\n  {\n    /** Style loaders that have been added. */\n    loaders: Set<Type<unknown>>;\n\n    /** References to the instantiated loaders. */\n    refs: ComponentRef<unknown>[];\n  }\n>();\n\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\n@Injectable({providedIn: 'root'})\nexport class _CdkPrivateStyleLoader {\n  private _appRef: ApplicationRef | undefined;\n  private _injector = inject(Injector);\n  private _environmentInjector = inject(EnvironmentInjector);\n\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader: Type<unknown>): void {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = (this._appRef = this._appRef || this._injector.get(ApplicationRef));\n    let data = appsWithLoaders.get(appRef);\n\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {loaders: new Set(), refs: []};\n      appsWithLoaders.set(appRef, data);\n\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {environmentInjector: this._environmentInjector}));\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\n@Component({\n  styleUrl: 'visually-hidden.css',\n  exportAs: 'cdkVisuallyHidden',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class _VisuallyHiddenLoader {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;AAmBA;AACA,MAAM,eAAe,GAAG,IAAI,OAAO,EAShC,CAAC;AAEJ;;;AAGG;MAEU,sBAAsB,CAAA;AACzB,IAAA,OAAO,CAA6B;AACpC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAE3D;;;AAGG;AACH,IAAA,IAAI,CAAC,MAAqB,EAAA;;QAExB,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;QACnF,IAAI,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;QAGvC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,IAAI,GAAG,EAAC,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC;AACtC,YAAA,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;;AAGlC,YAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,gBAAA,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AAChE,gBAAA,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;SACJ;;QAGD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAC,CAAC,CAAC,CAAC;SAC3F;KACF;uGA/BU,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAtB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,cADV,MAAM,EAAA,CAAA,CAAA;;2FAClB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBADlC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;;ACzBhC;;;AAGG;MAQU,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,yGAHtB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,kQAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAGD,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAPjC,SAAS;+BAEE,mBAAmB,EAAA,aAAA,EACd,iBAAiB,CAAC,IAAI,YAC3B,EAAE,EAAA,eAAA,EACK,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,kQAAA,CAAA,EAAA,CAAA;;;ACnBjD;;AAEG;;;;"}