{"version": 3, "file": "private.mjs", "sources": ["../../../../../../../src/cdk/coercion/private/observable.ts", "../../../../../../../src/cdk/coercion/private/private_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Observable, isObservable, of as observableOf} from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nexport function coerceObservable<T>(data: T | Observable<T>): Observable<T> {\n  if (!isObservable(data)) {\n    return observableOf(data);\n  }\n  return data;\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["observableOf"], "mappings": ";;AASA;;;AAGG;AACG,SAAU,gBAAgB,CAAI,IAAuB,EAAA;AACzD,IAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAOA,EAAY,CAAC,IAAI,CAAC,CAAC;KAC3B;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AClBA;;AAEG;;;;"}