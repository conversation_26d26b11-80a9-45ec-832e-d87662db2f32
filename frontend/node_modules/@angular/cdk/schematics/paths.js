"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MIGRATION_PATH = exports.COLLECTION_PATH = void 0;
const path_1 = require("path");
/** Path to the schematic collection for non-migration schematics. */
exports.COLLECTION_PATH = (0, path_1.join)(__dirname, 'collection.json');
/** Path to the schematic collection that includes the migrations. */
exports.MIGRATION_PATH = (0, path_1.join)(__dirname, 'migration.json');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGF0aHMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3NjaGVtYXRpY3MvcGF0aHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgsK0JBQTBCO0FBRTFCLHFFQUFxRTtBQUN4RCxRQUFBLGVBQWUsR0FBRyxJQUFBLFdBQUksRUFBQyxTQUFTLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztBQUVsRSxxRUFBcUU7QUFDeEQsUUFBQSxjQUFjLEdBQUcsSUFBQSxXQUFJLEVBQUMsU0FBUyxFQUFFLGdCQUFnQixDQUFDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCB7am9pbn0gZnJvbSAncGF0aCc7XG5cbi8qKiBQYXRoIHRvIHRoZSBzY2hlbWF0aWMgY29sbGVjdGlvbiBmb3Igbm9uLW1pZ3JhdGlvbiBzY2hlbWF0aWNzLiAqL1xuZXhwb3J0IGNvbnN0IENPTExFQ1RJT05fUEFUSCA9IGpvaW4oX19kaXJuYW1lLCAnY29sbGVjdGlvbi5qc29uJyk7XG5cbi8qKiBQYXRoIHRvIHRoZSBzY2hlbWF0aWMgY29sbGVjdGlvbiB0aGF0IGluY2x1ZGVzIHRoZSBtaWdyYXRpb25zLiAqL1xuZXhwb3J0IGNvbnN0IE1JR1JBVElPTl9QQVRIID0gam9pbihfX2Rpcm5hbWUsICdtaWdyYXRpb24uanNvbicpO1xuIl19