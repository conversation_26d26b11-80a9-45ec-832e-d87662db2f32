"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectMainFile = getProjectMainFile;
const schematics_1 = require("@angular-devkit/schematics");
const project_targets_1 = require("./project-targets");
/** Looks for the main TypeScript file in the given project and returns its path. */
function getProjectMainFile(project) {
    const buildOptions = (0, project_targets_1.getProjectTargetOptions)(project, 'build');
    // `browser` is for the `@angular-devkit/build-angular:application` builder while
    // `main` is for the `@angular-devkit/build-angular:browser` builder.
    const mainPath = (buildOptions['browser'] || buildOptions['main']);
    if (!mainPath) {
        throw new schematics_1.SchematicsException(`Could not find the project main file inside of the ` +
            `workspace config (${project.sourceRoot})`);
    }
    return mainPath;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvamVjdC1tYWluLWZpbGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL3NjaGVtYXRpY3MvdXRpbHMvcHJvamVjdC1tYWluLWZpbGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7QUFPSCxnREFlQztBQW5CRCwyREFBK0Q7QUFDL0QsdURBQTBEO0FBRTFELG9GQUFvRjtBQUNwRixTQUFnQixrQkFBa0IsQ0FBQyxPQUFxQztJQUN0RSxNQUFNLFlBQVksR0FBRyxJQUFBLHlDQUF1QixFQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztJQUUvRCxpRkFBaUY7SUFDakYscUVBQXFFO0lBQ3JFLE1BQU0sUUFBUSxHQUFHLENBQUMsWUFBWSxDQUFDLFNBQVMsQ0FBQyxJQUFJLFlBQVksQ0FBQyxNQUFNLENBQUMsQ0FBcUIsQ0FBQztJQUV2RixJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDZCxNQUFNLElBQUksZ0NBQW1CLENBQzNCLHFEQUFxRDtZQUNuRCxxQkFBcUIsT0FBTyxDQUFDLFVBQVUsR0FBRyxDQUM3QyxDQUFDO0lBQ0osQ0FBQztJQUVELE9BQU8sUUFBUSxDQUFDO0FBQ2xCLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCB7UGF0aCwgd29ya3NwYWNlc30gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L2NvcmUnO1xuaW1wb3J0IHtTY2hlbWF0aWNzRXhjZXB0aW9ufSBmcm9tICdAYW5ndWxhci1kZXZraXQvc2NoZW1hdGljcyc7XG5pbXBvcnQge2dldFByb2plY3RUYXJnZXRPcHRpb25zfSBmcm9tICcuL3Byb2plY3QtdGFyZ2V0cyc7XG5cbi8qKiBMb29rcyBmb3IgdGhlIG1haW4gVHlwZVNjcmlwdCBmaWxlIGluIHRoZSBnaXZlbiBwcm9qZWN0IGFuZCByZXR1cm5zIGl0cyBwYXRoLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFByb2plY3RNYWluRmlsZShwcm9qZWN0OiB3b3Jrc3BhY2VzLlByb2plY3REZWZpbml0aW9uKTogUGF0aCB7XG4gIGNvbnN0IGJ1aWxkT3B0aW9ucyA9IGdldFByb2plY3RUYXJnZXRPcHRpb25zKHByb2plY3QsICdidWlsZCcpO1xuXG4gIC8vIGBicm93c2VyYCBpcyBmb3IgdGhlIGBAYW5ndWxhci1kZXZraXQvYnVpbGQtYW5ndWxhcjphcHBsaWNhdGlvbmAgYnVpbGRlciB3aGlsZVxuICAvLyBgbWFpbmAgaXMgZm9yIHRoZSBgQGFuZ3VsYXItZGV2a2l0L2J1aWxkLWFuZ3VsYXI6YnJvd3NlcmAgYnVpbGRlci5cbiAgY29uc3QgbWFpblBhdGggPSAoYnVpbGRPcHRpb25zWydicm93c2VyJ10gfHwgYnVpbGRPcHRpb25zWydtYWluJ10pIGFzIFBhdGggfCB1bmRlZmluZWQ7XG5cbiAgaWYgKCFtYWluUGF0aCkge1xuICAgIHRocm93IG5ldyBTY2hlbWF0aWNzRXhjZXB0aW9uKFxuICAgICAgYENvdWxkIG5vdCBmaW5kIHRoZSBwcm9qZWN0IG1haW4gZmlsZSBpbnNpZGUgb2YgdGhlIGAgK1xuICAgICAgICBgd29ya3NwYWNlIGNvbmZpZyAoJHtwcm9qZWN0LnNvdXJjZVJvb3R9KWAsXG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiBtYWluUGF0aDtcbn1cbiJdfQ==