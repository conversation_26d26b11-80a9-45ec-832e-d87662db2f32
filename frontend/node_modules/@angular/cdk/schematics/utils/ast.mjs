"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseSourceFile = parseSourceFile;
exports.addModuleImportToRootModule = addModuleImportToRootModule;
exports.addModuleImportToModule = addModuleImportToModule;
exports.findModuleFromOptions = findModuleFromOptions;
const schematics_1 = require("@angular-devkit/schematics");
const change_1 = require("@schematics/angular/utility/change");
const workspace_1 = require("@schematics/angular/utility/workspace");
const find_module_1 = require("@schematics/angular/utility/find-module");
const ast_utils_1 = require("@schematics/angular/utility/ast-utils");
const ng_ast_utils_1 = require("@schematics/angular/utility/ng-ast-utils");
const ts = require("typescript");
const project_main_file_1 = require("./project-main-file");
/** Reads file given path and returns TypeScript source file. */
function parseSourceFile(host, path) {
    const buffer = host.read(path);
    if (!buffer) {
        throw new schematics_1.SchematicsException(`Could not find file for path: ${path}`);
    }
    return ts.createSourceFile(path, buffer.toString(), ts.ScriptTarget.Latest, true);
}
/** Import and add module to root app module. */
function addModuleImportToRootModule(host, moduleName, src, project) {
    const modulePath = (0, ng_ast_utils_1.getAppModulePath)(host, (0, project_main_file_1.getProjectMainFile)(project));
    addModuleImportToModule(host, modulePath, moduleName, src);
}
/**
 * Import and add module to specific module path.
 * @param host the tree we are updating
 * @param modulePath src location of the module to import
 * @param moduleName name of module to import
 * @param src src location to import
 */
function addModuleImportToModule(host, modulePath, moduleName, src) {
    const moduleSource = parseSourceFile(host, modulePath);
    if (!moduleSource) {
        throw new schematics_1.SchematicsException(`Module not found: ${modulePath}`);
    }
    const changes = (0, ast_utils_1.addImportToModule)(moduleSource, modulePath, moduleName, src);
    const recorder = host.beginUpdate(modulePath);
    changes.forEach(change => {
        if (change instanceof change_1.InsertChange) {
            recorder.insertLeft(change.pos, change.toAdd);
        }
    });
    host.commitUpdate(recorder);
}
/** Wraps the internal find module from options with undefined path handling  */
async function findModuleFromOptions(host, options) {
    const workspace = await (0, workspace_1.getWorkspace)(host);
    if (!options.project) {
        options.project = Array.from(workspace.projects.keys())[0];
    }
    const project = workspace.projects.get(options.project);
    if (options.path === undefined) {
        options.path = `/${project.root}/src/app`;
    }
    return (0, find_module_1.findModuleFromOptions)(host, options);
}
//# sourceMappingURL=data:application/json;base64,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