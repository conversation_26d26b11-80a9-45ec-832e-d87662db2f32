"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystem = void 0;
/**
 * Abstraction of the file system that migrations can use to record and apply
 * changes. This is necessary to support virtual file systems as used in the CLI devkit.
 */
class FileSystem {
}
exports.FileSystem = FileSystem;
//# sourceMappingURL=data:application/json;base64,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