"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAngularDecorators = getAngularDecorators;
exports.getCallDecoratorImport = getCallDecoratorImport;
const ts = require("typescript");
const imports_1 = require("./imports");
/**
 * Gets all decorators which are imported from an Angular package
 * (e.g. "@angular/core") from a list of decorators.
 */
function getAngularDecorators(typeChecker, decorators) {
    return decorators
        .map(node => ({ node, importData: getCallDecoratorImport(typeChecker, node) }))
        .filter(({ importData }) => importData && importData.moduleName.startsWith('@angular/'))
        .map(({ node, importData }) => ({
        node: node,
        name: importData.symbolName,
    }));
}
function getCallDecoratorImport(typeChecker, decorator) {
    if (!ts.isCallExpression(decorator.expression)) {
        return null;
    }
    const valueExpr = decorator.expression.expression;
    let identifier = null;
    if (ts.isIdentifier(valueExpr)) {
        identifier = valueExpr;
    }
    else if (ts.isPropertyAccessExpression(valueExpr) && ts.isIdentifier(valueExpr.name)) {
        identifier = valueExpr.name;
    }
    return identifier ? (0, imports_1.getImportOfIdentifier)(identifier, typeChecker) : null;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGVjb3JhdG9ycy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy91cGRhdGUtdG9vbC91dGlscy9kZWNvcmF0b3JzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7O0dBTUc7O0FBbUJILG9EQVdDO0FBRUQsd0RBZUM7QUE3Q0QsaUNBQWlDO0FBRWpDLHVDQUF3RDtBQVd4RDs7O0dBR0c7QUFDSCxTQUFnQixvQkFBb0IsQ0FDbEMsV0FBMkIsRUFDM0IsVUFBbUM7SUFFbkMsT0FBTyxVQUFVO1NBQ2QsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFDLElBQUksRUFBRSxVQUFVLEVBQUUsc0JBQXNCLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxFQUFDLENBQUMsQ0FBQztTQUM1RSxNQUFNLENBQUMsQ0FBQyxFQUFDLFVBQVUsRUFBQyxFQUFFLEVBQUUsQ0FBQyxVQUFVLElBQUksVUFBVSxDQUFDLFVBQVUsQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLENBQUM7U0FDckYsR0FBRyxDQUFDLENBQUMsRUFBQyxJQUFJLEVBQUUsVUFBVSxFQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDNUIsSUFBSSxFQUFFLElBQStCO1FBQ3JDLElBQUksRUFBRSxVQUFXLENBQUMsVUFBVTtLQUM3QixDQUFDLENBQUMsQ0FBQztBQUNSLENBQUM7QUFFRCxTQUFnQixzQkFBc0IsQ0FDcEMsV0FBMkIsRUFDM0IsU0FBdUI7SUFFdkIsSUFBSSxDQUFDLEVBQUUsQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQztRQUMvQyxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFDRCxNQUFNLFNBQVMsR0FBRyxTQUFTLENBQUMsVUFBVSxDQUFDLFVBQVUsQ0FBQztJQUNsRCxJQUFJLFVBQVUsR0FBeUIsSUFBSSxDQUFDO0lBQzVDLElBQUksRUFBRSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO1FBQy9CLFVBQVUsR0FBRyxTQUFTLENBQUM7SUFDekIsQ0FBQztTQUFNLElBQUksRUFBRSxDQUFDLDBCQUEwQixDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7UUFDdkYsVUFBVSxHQUFHLFNBQVMsQ0FBQyxJQUFJLENBQUM7SUFDOUIsQ0FBQztJQUNELE9BQU8sVUFBVSxDQUFDLENBQUMsQ0FBQyxJQUFBLCtCQUFxQixFQUFDLFVBQVUsRUFBRSxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO0FBQzVFLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCAqIGFzIHRzIGZyb20gJ3R5cGVzY3JpcHQnO1xuXG5pbXBvcnQge2dldEltcG9ydE9mSWRlbnRpZmllciwgSW1wb3J0fSBmcm9tICcuL2ltcG9ydHMnO1xuXG5leHBvcnQgdHlwZSBDYWxsRXhwcmVzc2lvbkRlY29yYXRvciA9IHRzLkRlY29yYXRvciAmIHtcbiAgZXhwcmVzc2lvbjogdHMuQ2FsbEV4cHJlc3Npb247XG59O1xuXG5leHBvcnQgaW50ZXJmYWNlIE5nRGVjb3JhdG9yIHtcbiAgbmFtZTogc3RyaW5nO1xuICBub2RlOiBDYWxsRXhwcmVzc2lvbkRlY29yYXRvcjtcbn1cblxuLyoqXG4gKiBHZXRzIGFsbCBkZWNvcmF0b3JzIHdoaWNoIGFyZSBpbXBvcnRlZCBmcm9tIGFuIEFuZ3VsYXIgcGFja2FnZVxuICogKGUuZy4gXCJAYW5ndWxhci9jb3JlXCIpIGZyb20gYSBsaXN0IG9mIGRlY29yYXRvcnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRBbmd1bGFyRGVjb3JhdG9ycyhcbiAgdHlwZUNoZWNrZXI6IHRzLlR5cGVDaGVja2VyLFxuICBkZWNvcmF0b3JzOiByZWFkb25seSB0cy5EZWNvcmF0b3JbXSxcbik6IHJlYWRvbmx5IE5nRGVjb3JhdG9yW10ge1xuICByZXR1cm4gZGVjb3JhdG9yc1xuICAgIC5tYXAobm9kZSA9PiAoe25vZGUsIGltcG9ydERhdGE6IGdldENhbGxEZWNvcmF0b3JJbXBvcnQodHlwZUNoZWNrZXIsIG5vZGUpfSkpXG4gICAgLmZpbHRlcigoe2ltcG9ydERhdGF9KSA9PiBpbXBvcnREYXRhICYmIGltcG9ydERhdGEubW9kdWxlTmFtZS5zdGFydHNXaXRoKCdAYW5ndWxhci8nKSlcbiAgICAubWFwKCh7bm9kZSwgaW1wb3J0RGF0YX0pID0+ICh7XG4gICAgICBub2RlOiBub2RlIGFzIENhbGxFeHByZXNzaW9uRGVjb3JhdG9yLFxuICAgICAgbmFtZTogaW1wb3J0RGF0YSEuc3ltYm9sTmFtZSxcbiAgICB9KSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRDYWxsRGVjb3JhdG9ySW1wb3J0KFxuICB0eXBlQ2hlY2tlcjogdHMuVHlwZUNoZWNrZXIsXG4gIGRlY29yYXRvcjogdHMuRGVjb3JhdG9yLFxuKTogSW1wb3J0IHwgbnVsbCB7XG4gIGlmICghdHMuaXNDYWxsRXhwcmVzc2lvbihkZWNvcmF0b3IuZXhwcmVzc2lvbikpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICBjb25zdCB2YWx1ZUV4cHIgPSBkZWNvcmF0b3IuZXhwcmVzc2lvbi5leHByZXNzaW9uO1xuICBsZXQgaWRlbnRpZmllcjogdHMuSWRlbnRpZmllciB8IG51bGwgPSBudWxsO1xuICBpZiAodHMuaXNJZGVudGlmaWVyKHZhbHVlRXhwcikpIHtcbiAgICBpZGVudGlmaWVyID0gdmFsdWVFeHByO1xuICB9IGVsc2UgaWYgKHRzLmlzUHJvcGVydHlBY2Nlc3NFeHByZXNzaW9uKHZhbHVlRXhwcikgJiYgdHMuaXNJZGVudGlmaWVyKHZhbHVlRXhwci5uYW1lKSkge1xuICAgIGlkZW50aWZpZXIgPSB2YWx1ZUV4cHIubmFtZTtcbiAgfVxuICByZXR1cm4gaWRlbnRpZmllciA/IGdldEltcG9ydE9mSWRlbnRpZmllcihpZGVudGlmaWVyLCB0eXBlQ2hlY2tlcikgOiBudWxsO1xufVxuIl19