"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.determineBaseTypes = determineBaseTypes;
const ts = require("typescript");
/** Determines the base types of the specified class declaration. */
function determineBaseTypes(node) {
    if (!node.heritageClauses) {
        return null;
    }
    return node.heritageClauses
        .reduce((types, clause) => types.concat(clause.types), [])
        .map(typeExpression => typeExpression.expression)
        .filter(expression => expression && ts.isIdentifier(expression))
        .map(identifier => identifier.text);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmFzZS10eXBlcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvdHlwZXNjcmlwdC9iYXNlLXR5cGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7O0dBTUc7O0FBS0gsZ0RBVUM7QUFiRCxpQ0FBaUM7QUFFakMsb0VBQW9FO0FBQ3BFLFNBQWdCLGtCQUFrQixDQUFDLElBQXlCO0lBQzFELElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7UUFDMUIsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQsT0FBTyxJQUFJLENBQUMsZUFBZTtTQUN4QixNQUFNLENBQUMsQ0FBQyxLQUFLLEVBQUUsTUFBTSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRSxFQUFzQyxDQUFDO1NBQzdGLEdBQUcsQ0FBQyxjQUFjLENBQUMsRUFBRSxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUM7U0FDaEQsTUFBTSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUMsVUFBVSxJQUFJLEVBQUUsQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLENBQUM7U0FDL0QsR0FBRyxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUUsVUFBNEIsQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUMzRCxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuZGV2L2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQgKiBhcyB0cyBmcm9tICd0eXBlc2NyaXB0JztcblxuLyoqIERldGVybWluZXMgdGhlIGJhc2UgdHlwZXMgb2YgdGhlIHNwZWNpZmllZCBjbGFzcyBkZWNsYXJhdGlvbi4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZXRlcm1pbmVCYXNlVHlwZXMobm9kZTogdHMuQ2xhc3NEZWNsYXJhdGlvbik6IHN0cmluZ1tdIHwgbnVsbCB7XG4gIGlmICghbm9kZS5oZXJpdGFnZUNsYXVzZXMpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiBub2RlLmhlcml0YWdlQ2xhdXNlc1xuICAgIC5yZWR1Y2UoKHR5cGVzLCBjbGF1c2UpID0+IHR5cGVzLmNvbmNhdChjbGF1c2UudHlwZXMpLCBbXSBhcyB0cy5FeHByZXNzaW9uV2l0aFR5cGVBcmd1bWVudHNbXSlcbiAgICAubWFwKHR5cGVFeHByZXNzaW9uID0+IHR5cGVFeHByZXNzaW9uLmV4cHJlc3Npb24pXG4gICAgLmZpbHRlcihleHByZXNzaW9uID0+IGV4cHJlc3Npb24gJiYgdHMuaXNJZGVudGlmaWVyKGV4cHJlc3Npb24pKVxuICAgIC5tYXAoaWRlbnRpZmllciA9PiAoaWRlbnRpZmllciBhcyB0cy5JZGVudGlmaWVyKS50ZXh0KTtcbn1cbiJdfQ==