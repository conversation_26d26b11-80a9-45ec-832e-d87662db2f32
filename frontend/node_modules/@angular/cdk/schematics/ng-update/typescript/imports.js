"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isImportSpecifierNode = isImportSpecifierNode;
exports.isExportSpecifierNode = isExportSpecifierNode;
exports.isNamespaceImportNode = isNamespaceImportNode;
exports.getImportDeclaration = getImportDeclaration;
exports.getExportDeclaration = getExportDeclaration;
const ts = require("typescript");
/** Checks whether the given node is part of an import specifier node. */
function isImportSpecifierNode(node) {
    return isPartOfKind(node, ts.SyntaxKind.ImportSpecifier);
}
/** Checks whether the given node is part of an export specifier node. */
function isExportSpecifierNode(node) {
    return isPartOfKind(node, ts.SyntaxKind.ExportSpecifier);
}
/** Checks whether the given node is part of a namespace import. */
function isNamespaceImportNode(node) {
    return isPartOfKind(node, ts.SyntaxKind.NamespaceImport);
}
/** Finds the parent import declaration of a given TypeScript node. */
function getImportDeclaration(node) {
    return findDeclaration(node, ts.SyntaxKind.ImportDeclaration);
}
/** Finds the parent export declaration of a given TypeScript node */
function getExportDeclaration(node) {
    return findDeclaration(node, ts.SyntaxKind.ExportDeclaration);
}
/** Finds the specified declaration for the given node by walking up the TypeScript nodes. */
function findDeclaration(node, kind) {
    while (node.kind !== kind) {
        node = node.parent;
    }
    return node;
}
/** Checks whether the given node is part of another TypeScript Node with the specified kind. */
function isPartOfKind(node, kind) {
    if (node.kind === kind) {
        return true;
    }
    else if (node.kind === ts.SyntaxKind.SourceFile) {
        return false;
    }
    return isPartOfKind(node.parent, kind);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW1wb3J0cy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvdHlwZXNjcmlwdC9pbXBvcnRzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7O0dBTUc7O0FBS0gsc0RBRUM7QUFHRCxzREFFQztBQUdELHNEQUVDO0FBR0Qsb0RBRUM7QUFHRCxvREFFQztBQXpCRCxpQ0FBaUM7QUFFakMseUVBQXlFO0FBQ3pFLFNBQWdCLHFCQUFxQixDQUFDLElBQWE7SUFDakQsT0FBTyxZQUFZLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsZUFBZSxDQUFDLENBQUM7QUFDM0QsQ0FBQztBQUVELHlFQUF5RTtBQUN6RSxTQUFnQixxQkFBcUIsQ0FBQyxJQUFhO0lBQ2pELE9BQU8sWUFBWSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLGVBQWUsQ0FBQyxDQUFDO0FBQzNELENBQUM7QUFFRCxtRUFBbUU7QUFDbkUsU0FBZ0IscUJBQXFCLENBQUMsSUFBYTtJQUNqRCxPQUFPLFlBQVksQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxlQUFlLENBQUMsQ0FBQztBQUMzRCxDQUFDO0FBRUQsc0VBQXNFO0FBQ3RFLFNBQWdCLG9CQUFvQixDQUFDLElBQWE7SUFDaEQsT0FBTyxlQUFlLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQXlCLENBQUM7QUFDeEYsQ0FBQztBQUVELHFFQUFxRTtBQUNyRSxTQUFnQixvQkFBb0IsQ0FBQyxJQUFhO0lBQ2hELE9BQU8sZUFBZSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUF5QixDQUFDO0FBQ3hGLENBQUM7QUFFRCw2RkFBNkY7QUFDN0YsU0FBUyxlQUFlLENBQTBCLElBQWEsRUFBRSxJQUFPO0lBQ3RFLE9BQU8sSUFBSSxDQUFDLElBQUksS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUMxQixJQUFJLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQztJQUNyQixDQUFDO0lBRUQsT0FBTyxJQUFJLENBQUM7QUFDZCxDQUFDO0FBRUQsZ0dBQWdHO0FBQ2hHLFNBQVMsWUFBWSxDQUEwQixJQUFhLEVBQUUsSUFBTztJQUNuRSxJQUFJLElBQUksQ0FBQyxJQUFJLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDdkIsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO1NBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLEVBQUUsQ0FBQyxVQUFVLENBQUMsVUFBVSxFQUFFLENBQUM7UUFDbEQsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0lBRUQsT0FBTyxZQUFZLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQztBQUN6QyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuZGV2L2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQgKiBhcyB0cyBmcm9tICd0eXBlc2NyaXB0JztcblxuLyoqIENoZWNrcyB3aGV0aGVyIHRoZSBnaXZlbiBub2RlIGlzIHBhcnQgb2YgYW4gaW1wb3J0IHNwZWNpZmllciBub2RlLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzSW1wb3J0U3BlY2lmaWVyTm9kZShub2RlOiB0cy5Ob2RlKSB7XG4gIHJldHVybiBpc1BhcnRPZktpbmQobm9kZSwgdHMuU3ludGF4S2luZC5JbXBvcnRTcGVjaWZpZXIpO1xufVxuXG4vKiogQ2hlY2tzIHdoZXRoZXIgdGhlIGdpdmVuIG5vZGUgaXMgcGFydCBvZiBhbiBleHBvcnQgc3BlY2lmaWVyIG5vZGUuICovXG5leHBvcnQgZnVuY3Rpb24gaXNFeHBvcnRTcGVjaWZpZXJOb2RlKG5vZGU6IHRzLk5vZGUpIHtcbiAgcmV0dXJuIGlzUGFydE9mS2luZChub2RlLCB0cy5TeW50YXhLaW5kLkV4cG9ydFNwZWNpZmllcik7XG59XG5cbi8qKiBDaGVja3Mgd2hldGhlciB0aGUgZ2l2ZW4gbm9kZSBpcyBwYXJ0IG9mIGEgbmFtZXNwYWNlIGltcG9ydC4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc05hbWVzcGFjZUltcG9ydE5vZGUobm9kZTogdHMuTm9kZSkge1xuICByZXR1cm4gaXNQYXJ0T2ZLaW5kKG5vZGUsIHRzLlN5bnRheEtpbmQuTmFtZXNwYWNlSW1wb3J0KTtcbn1cblxuLyoqIEZpbmRzIHRoZSBwYXJlbnQgaW1wb3J0IGRlY2xhcmF0aW9uIG9mIGEgZ2l2ZW4gVHlwZVNjcmlwdCBub2RlLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEltcG9ydERlY2xhcmF0aW9uKG5vZGU6IHRzLk5vZGUpIHtcbiAgcmV0dXJuIGZpbmREZWNsYXJhdGlvbihub2RlLCB0cy5TeW50YXhLaW5kLkltcG9ydERlY2xhcmF0aW9uKSBhcyB0cy5JbXBvcnREZWNsYXJhdGlvbjtcbn1cblxuLyoqIEZpbmRzIHRoZSBwYXJlbnQgZXhwb3J0IGRlY2xhcmF0aW9uIG9mIGEgZ2l2ZW4gVHlwZVNjcmlwdCBub2RlICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RXhwb3J0RGVjbGFyYXRpb24obm9kZTogdHMuTm9kZSkge1xuICByZXR1cm4gZmluZERlY2xhcmF0aW9uKG5vZGUsIHRzLlN5bnRheEtpbmQuRXhwb3J0RGVjbGFyYXRpb24pIGFzIHRzLkV4cG9ydERlY2xhcmF0aW9uO1xufVxuXG4vKiogRmluZHMgdGhlIHNwZWNpZmllZCBkZWNsYXJhdGlvbiBmb3IgdGhlIGdpdmVuIG5vZGUgYnkgd2Fsa2luZyB1cCB0aGUgVHlwZVNjcmlwdCBub2Rlcy4gKi9cbmZ1bmN0aW9uIGZpbmREZWNsYXJhdGlvbjxUIGV4dGVuZHMgdHMuU3ludGF4S2luZD4obm9kZTogdHMuTm9kZSwga2luZDogVCkge1xuICB3aGlsZSAobm9kZS5raW5kICE9PSBraW5kKSB7XG4gICAgbm9kZSA9IG5vZGUucGFyZW50O1xuICB9XG5cbiAgcmV0dXJuIG5vZGU7XG59XG5cbi8qKiBDaGVja3Mgd2hldGhlciB0aGUgZ2l2ZW4gbm9kZSBpcyBwYXJ0IG9mIGFub3RoZXIgVHlwZVNjcmlwdCBOb2RlIHdpdGggdGhlIHNwZWNpZmllZCBraW5kLiAqL1xuZnVuY3Rpb24gaXNQYXJ0T2ZLaW5kPFQgZXh0ZW5kcyB0cy5TeW50YXhLaW5kPihub2RlOiB0cy5Ob2RlLCBraW5kOiBUKTogYm9vbGVhbiB7XG4gIGlmIChub2RlLmtpbmQgPT09IGtpbmQpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBlbHNlIGlmIChub2RlLmtpbmQgPT09IHRzLlN5bnRheEtpbmQuU291cmNlRmlsZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuXG4gIHJldHVybiBpc1BhcnRPZktpbmQobm9kZS5wYXJlbnQsIGtpbmQpO1xufVxuIl19