"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.cdkModuleSpecifier = exports.materialModuleSpecifier = void 0;
exports.isMaterialImportDeclaration = isMaterialImportDeclaration;
exports.isMaterialExportDeclaration = isMaterialExportDeclaration;
const imports_1 = require("../typescript/imports");
/** Name of the Angular Material module specifier. */
exports.materialModuleSpecifier = '@angular/material';
/** Name of the Angular CDK module specifier. */
exports.cdkModuleSpecifier = '@angular/cdk';
/** Whether the specified node is part of an Angular Material or CDK import declaration. */
function isMaterialImportDeclaration(node) {
    return isMaterialDeclaration((0, imports_1.getImportDeclaration)(node));
}
/** Whether the specified node is part of an Angular Material or CDK import declaration. */
function isMaterialExportDeclaration(node) {
    return isMaterialDeclaration((0, imports_1.getExportDeclaration)(node));
}
/** Whether the declaration is part of Angular Material. */
function isMaterialDeclaration(declaration) {
    if (!declaration.moduleSpecifier) {
        return false;
    }
    const moduleSpecifier = declaration.moduleSpecifier.getText();
    return (moduleSpecifier.indexOf(exports.materialModuleSpecifier) !== -1 ||
        moduleSpecifier.indexOf(exports.cdkModuleSpecifier) !== -1);
}
//# sourceMappingURL=data:application/json;base64,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