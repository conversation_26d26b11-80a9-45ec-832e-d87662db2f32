"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.findAllSubstringIndices = findAllSubstringIndices;
exports.isStringLiteralLike = isStringLiteralLike;
const ts = require("typescript");
/** Finds all start indices of the given search string in the input string. */
function findAllSubstringIndices(input, search) {
    const result = [];
    let i = -1;
    while ((i = input.indexOf(search, i + 1)) !== -1) {
        result.push(i);
    }
    return result;
}
/**
 * Checks whether the given node is either a string literal or a no-substitution template
 * literal. Note that we cannot use `ts.isStringLiteralLike()` because if developers update
 * an outdated project, their TypeScript version is not automatically being updated
 * and therefore could throw because the function is not available yet.
 * https://github.com/Microsoft/TypeScript/commit/8518343dc8762475a5e92c9f80b5c5725bd81796
 */
function isStringLiteralLike(node) {
    return ts.isStringLiteral(node) || ts.isNoSubstitutionTemplateLiteral(node);
}
//# sourceMappingURL=data:application/json;base64,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