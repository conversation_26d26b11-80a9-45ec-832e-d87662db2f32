"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.cdkMigrations = void 0;
exports.createMigrationSchematicRule = createMigrationSchematicRule;
exports.isDevkitMigration = isDevkitMigration;
const tasks_1 = require("@angular-devkit/schematics/tasks");
const update_tool_1 = require("../update-tool");
const project_tsconfig_paths_1 = require("../utils/project-tsconfig-paths");
const devkit_file_system_1 = require("./devkit-file-system");
const devkit_migration_1 = require("./devkit-migration");
const find_stylesheets_1 = require("./find-stylesheets");
const attribute_selectors_1 = require("./migrations/attribute-selectors");
const class_inheritance_1 = require("./migrations/class-inheritance");
const class_names_1 = require("./migrations/class-names");
const constructor_signature_1 = require("./migrations/constructor-signature");
const css_selectors_1 = require("./migrations/css-selectors");
const css_tokens_1 = require("./migrations/css-tokens");
const element_selectors_1 = require("./migrations/element-selectors");
const input_names_1 = require("./migrations/input-names");
const method_call_arguments_1 = require("./migrations/method-call-arguments");
const misc_template_1 = require("./migrations/misc-template");
const output_names_1 = require("./migrations/output-names");
const property_names_1 = require("./migrations/property-names");
const symbol_removal_1 = require("./migrations/symbol-removal");
/** List of migrations which run for the CDK update. */
exports.cdkMigrations = [
    attribute_selectors_1.AttributeSelectorsMigration,
    class_inheritance_1.ClassInheritanceMigration,
    class_names_1.ClassNamesMigration,
    constructor_signature_1.ConstructorSignatureMigration,
    css_selectors_1.CssSelectorsMigration,
    css_tokens_1.CssTokensMigration,
    element_selectors_1.ElementSelectorsMigration,
    input_names_1.InputNamesMigration,
    method_call_arguments_1.MethodCallArgumentsMigration,
    misc_template_1.MiscTemplateMigration,
    output_names_1.OutputNamesMigration,
    property_names_1.PropertyNamesMigration,
    symbol_removal_1.SymbolRemovalMigration,
];
/**
 * Creates a Angular schematic rule that runs the upgrade for the
 * specified target version.
 */
function createMigrationSchematicRule(targetVersion, extraMigrations, upgradeData, onMigrationCompleteFn) {
    return async (tree, context) => {
        const logger = context.logger;
        const workspace = await (0, project_tsconfig_paths_1.getWorkspaceConfigGracefully)(tree);
        if (workspace === null) {
            logger.error('Could not find workspace configuration file.');
            return;
        }
        // Keep track of all project source files which have been checked/migrated. This is
        // necessary because multiple TypeScript projects can contain the same source file and
        // we don't want to check these again, as this would result in duplicated failure messages.
        const analyzedFiles = new Set();
        const fileSystem = new devkit_file_system_1.DevkitFileSystem(tree);
        const projectNames = workspace.projects.keys();
        const migrations = [...exports.cdkMigrations, ...extraMigrations];
        let hasFailures = false;
        for (const projectName of projectNames) {
            const project = workspace.projects.get(projectName);
            const buildTsconfigPath = (0, project_tsconfig_paths_1.getTargetTsconfigPath)(project, 'build');
            const testTsconfigPath = (0, project_tsconfig_paths_1.getTargetTsconfigPath)(project, 'test');
            if (!buildTsconfigPath && !testTsconfigPath) {
                logger.warn(`Skipping migration for project ${projectName}. Unable to determine 'tsconfig.json' file in workspace config.`);
                continue;
            }
            // In some applications, developers will have global stylesheets which are not
            // specified in any Angular component. Therefore we glob up all CSS and SCSS files
            // in the project and migrate them if needed.
            // TODO: rework this to collect global stylesheets from the workspace config.
            // TODO: https://github.com/angular/components/issues/24032.
            const additionalStylesheetPaths = (0, find_stylesheets_1.findStylesheetFiles)(tree, project.root);
            if (buildTsconfigPath !== null) {
                runMigrations(project, projectName, buildTsconfigPath, additionalStylesheetPaths, false);
            }
            if (testTsconfigPath !== null) {
                runMigrations(project, projectName, testTsconfigPath, additionalStylesheetPaths, true);
            }
        }
        let runPackageManager = false;
        // Run the global post migration static members for all
        // registered devkit migrations.
        migrations.forEach(m => {
            const actionResult = isDevkitMigration(m) && m.globalPostMigration !== undefined
                ? m.globalPostMigration(tree, targetVersion, context)
                : null;
            if (actionResult) {
                runPackageManager = runPackageManager || actionResult.runPackageManager;
            }
        });
        // If a migration requested the package manager to run, we run it as an
        // asynchronous post migration task. We cannot run it synchronously,
        // as file changes from the current migration task are not applied to
        // the file system yet.
        if (runPackageManager) {
            context.addTask(new tasks_1.NodePackageInstallTask({ quiet: false }));
        }
        if (onMigrationCompleteFn) {
            onMigrationCompleteFn(context, targetVersion, hasFailures);
        }
        /** Runs the migrations for the specified workspace project. */
        function runMigrations(project, projectName, tsconfigPath, additionalStylesheetPaths, isTestTarget) {
            const program = update_tool_1.UpdateProject.createProgramFromTsconfig(tsconfigPath, fileSystem);
            const updateContext = {
                isTestTarget,
                projectName,
                project,
                tree,
            };
            const updateProject = new update_tool_1.UpdateProject(updateContext, program, fileSystem, analyzedFiles, context.logger);
            const result = updateProject.migrate(migrations, targetVersion, upgradeData, additionalStylesheetPaths);
            // Commit all recorded edits in the update recorder. We apply the edits after all
            // migrations ran because otherwise offsets in the TypeScript program would be
            // shifted and individual migrations could no longer update the same source file.
            fileSystem.commitEdits();
            hasFailures = hasFailures || result.hasFailures;
        }
    };
}
/** Whether the given migration type refers to a devkit migration */
function isDevkitMigration(value) {
    return devkit_migration_1.DevkitMigration.isPrototypeOf(value);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGV2a2l0LW1pZ3JhdGlvbi1ydWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vc3JjL2Nkay9zY2hlbWF0aWNzL25nLXVwZGF0ZS9kZXZraXQtbWlncmF0aW9uLXJ1bGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBMkRILG9FQW1IQztBQUdELDhDQUlDO0FBakxELDREQUF3RTtBQUV4RSxnREFBNkM7QUFJN0MsNEVBQW9HO0FBRXBHLDZEQUFzRDtBQUN0RCx5REFBdUY7QUFDdkYseURBQXVEO0FBQ3ZELDBFQUE2RTtBQUM3RSxzRUFBeUU7QUFDekUsMERBQTZEO0FBQzdELDhFQUFpRjtBQUNqRiw4REFBaUU7QUFDakUsd0RBQTJEO0FBQzNELHNFQUF5RTtBQUN6RSwwREFBNkQ7QUFDN0QsOEVBQWdGO0FBQ2hGLDhEQUFpRTtBQUNqRSw0REFBK0Q7QUFDL0QsZ0VBQW1FO0FBQ25FLGdFQUFtRTtBQUduRSx1REFBdUQ7QUFDMUMsUUFBQSxhQUFhLEdBQWlDO0lBQ3pELGlEQUEyQjtJQUMzQiw2Q0FBeUI7SUFDekIsaUNBQW1CO0lBQ25CLHFEQUE2QjtJQUM3QixxQ0FBcUI7SUFDckIsK0JBQWtCO0lBQ2xCLDZDQUF5QjtJQUN6QixpQ0FBbUI7SUFDbkIsb0RBQTRCO0lBQzVCLHFDQUFxQjtJQUNyQixtQ0FBb0I7SUFDcEIsdUNBQXNCO0lBQ3RCLHVDQUFzQjtDQUN2QixDQUFDO0FBVUY7OztHQUdHO0FBQ0gsU0FBZ0IsNEJBQTRCLENBQzFDLGFBQTRCLEVBQzVCLGVBQTBDLEVBQzFDLFdBQXdCLEVBQ3hCLHFCQUF1QztJQUV2QyxPQUFPLEtBQUssRUFBRSxJQUFVLEVBQUUsT0FBeUIsRUFBRSxFQUFFO1FBQ3JELE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUM7UUFDOUIsTUFBTSxTQUFTLEdBQUcsTUFBTSxJQUFBLHFEQUE0QixFQUFDLElBQUksQ0FBQyxDQUFDO1FBRTNELElBQUksU0FBUyxLQUFLLElBQUksRUFBRSxDQUFDO1lBQ3ZCLE1BQU0sQ0FBQyxLQUFLLENBQUMsOENBQThDLENBQUMsQ0FBQztZQUM3RCxPQUFPO1FBQ1QsQ0FBQztRQUVELG1GQUFtRjtRQUNuRixzRkFBc0Y7UUFDdEYsMkZBQTJGO1FBQzNGLE1BQU0sYUFBYSxHQUFHLElBQUksR0FBRyxFQUFpQixDQUFDO1FBQy9DLE1BQU0sVUFBVSxHQUFHLElBQUkscUNBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDOUMsTUFBTSxZQUFZLEdBQUcsU0FBUyxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUMvQyxNQUFNLFVBQVUsR0FBRyxDQUFDLEdBQUcscUJBQWEsRUFBRSxHQUFHLGVBQWUsQ0FBOEIsQ0FBQztRQUN2RixJQUFJLFdBQVcsR0FBRyxLQUFLLENBQUM7UUFFeEIsS0FBSyxNQUFNLFdBQVcsSUFBSSxZQUFZLEVBQUUsQ0FBQztZQUN2QyxNQUFNLE9BQU8sR0FBRyxTQUFTLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUUsQ0FBQztZQUNyRCxNQUFNLGlCQUFpQixHQUFHLElBQUEsOENBQXFCLEVBQUMsT0FBTyxFQUFFLE9BQU8sQ0FBQyxDQUFDO1lBQ2xFLE1BQU0sZ0JBQWdCLEdBQUcsSUFBQSw4Q0FBcUIsRUFBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFaEUsSUFBSSxDQUFDLGlCQUFpQixJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztnQkFDNUMsTUFBTSxDQUFDLElBQUksQ0FDVCxrQ0FBa0MsV0FBVyxpRUFBaUUsQ0FDL0csQ0FBQztnQkFDRixTQUFTO1lBQ1gsQ0FBQztZQUVELDhFQUE4RTtZQUM5RSxrRkFBa0Y7WUFDbEYsNkNBQTZDO1lBQzdDLDZFQUE2RTtZQUM3RSw0REFBNEQ7WUFDNUQsTUFBTSx5QkFBeUIsR0FBRyxJQUFBLHNDQUFtQixFQUFDLElBQUksRUFBRSxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFMUUsSUFBSSxpQkFBaUIsS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDL0IsYUFBYSxDQUFDLE9BQU8sRUFBRSxXQUFXLEVBQUUsaUJBQWlCLEVBQUUseUJBQXlCLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDM0YsQ0FBQztZQUNELElBQUksZ0JBQWdCLEtBQUssSUFBSSxFQUFFLENBQUM7Z0JBQzlCLGFBQWEsQ0FBQyxPQUFPLEVBQUUsV0FBVyxFQUFFLGdCQUFnQixFQUFFLHlCQUF5QixFQUFFLElBQUksQ0FBQyxDQUFDO1lBQ3pGLENBQUM7UUFDSCxDQUFDO1FBRUQsSUFBSSxpQkFBaUIsR0FBRyxLQUFLLENBQUM7UUFDOUIsdURBQXVEO1FBQ3ZELGdDQUFnQztRQUNoQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFO1lBQ3JCLE1BQU0sWUFBWSxHQUNoQixpQkFBaUIsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsbUJBQW1CLEtBQUssU0FBUztnQkFDekQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLEVBQUUsYUFBYSxFQUFFLE9BQU8sQ0FBQztnQkFDckQsQ0FBQyxDQUFDLElBQUksQ0FBQztZQUNYLElBQUksWUFBWSxFQUFFLENBQUM7Z0JBQ2pCLGlCQUFpQixHQUFHLGlCQUFpQixJQUFJLFlBQVksQ0FBQyxpQkFBaUIsQ0FBQztZQUMxRSxDQUFDO1FBQ0gsQ0FBQyxDQUFDLENBQUM7UUFFSCx1RUFBdUU7UUFDdkUsb0VBQW9FO1FBQ3BFLHFFQUFxRTtRQUNyRSx1QkFBdUI7UUFDdkIsSUFBSSxpQkFBaUIsRUFBRSxDQUFDO1lBQ3RCLE9BQU8sQ0FBQyxPQUFPLENBQUMsSUFBSSw4QkFBc0IsQ0FBQyxFQUFDLEtBQUssRUFBRSxLQUFLLEVBQUMsQ0FBQyxDQUFDLENBQUM7UUFDOUQsQ0FBQztRQUVELElBQUkscUJBQXFCLEVBQUUsQ0FBQztZQUMxQixxQkFBcUIsQ0FBQyxPQUFPLEVBQUUsYUFBYSxFQUFFLFdBQVcsQ0FBQyxDQUFDO1FBQzdELENBQUM7UUFFRCwrREFBK0Q7UUFDL0QsU0FBUyxhQUFhLENBQ3BCLE9BQXFDLEVBQ3JDLFdBQW1CLEVBQ25CLFlBQTJCLEVBQzNCLHlCQUFtQyxFQUNuQyxZQUFxQjtZQUVyQixNQUFNLE9BQU8sR0FBRywyQkFBYSxDQUFDLHlCQUF5QixDQUFDLFlBQVksRUFBRSxVQUFVLENBQUMsQ0FBQztZQUNsRixNQUFNLGFBQWEsR0FBa0I7Z0JBQ25DLFlBQVk7Z0JBQ1osV0FBVztnQkFDWCxPQUFPO2dCQUNQLElBQUk7YUFDTCxDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsSUFBSSwyQkFBYSxDQUNyQyxhQUFhLEVBQ2IsT0FBTyxFQUNQLFVBQVUsRUFDVixhQUFhLEVBQ2IsT0FBTyxDQUFDLE1BQU0sQ0FDZixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsYUFBYSxDQUFDLE9BQU8sQ0FDbEMsVUFBVSxFQUNWLGFBQWEsRUFDYixXQUFXLEVBQ1gseUJBQXlCLENBQzFCLENBQUM7WUFFRixpRkFBaUY7WUFDakYsOEVBQThFO1lBQzlFLGlGQUFpRjtZQUNqRixVQUFVLENBQUMsV0FBVyxFQUFFLENBQUM7WUFFekIsV0FBVyxHQUFHLFdBQVcsSUFBSSxNQUFNLENBQUMsV0FBVyxDQUFDO1FBQ2xELENBQUM7SUFDSCxDQUFDLENBQUM7QUFDSixDQUFDO0FBRUQsb0VBQW9FO0FBQ3BFLFNBQWdCLGlCQUFpQixDQUMvQixLQUE4QjtJQUU5QixPQUFPLGtDQUFlLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDO0FBQzlDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCB7d29ya3NwYWNlc30gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L2NvcmUnO1xuaW1wb3J0IHtSdWxlLCBTY2hlbWF0aWNDb250ZXh0LCBUcmVlfSBmcm9tICdAYW5ndWxhci1kZXZraXQvc2NoZW1hdGljcyc7XG5pbXBvcnQge05vZGVQYWNrYWdlSW5zdGFsbFRhc2t9IGZyb20gJ0Bhbmd1bGFyLWRldmtpdC9zY2hlbWF0aWNzL3Rhc2tzJztcblxuaW1wb3J0IHtVcGRhdGVQcm9qZWN0fSBmcm9tICcuLi91cGRhdGUtdG9vbCc7XG5pbXBvcnQge1dvcmtzcGFjZVBhdGh9IGZyb20gJy4uL3VwZGF0ZS10b29sL2ZpbGUtc3lzdGVtJztcbmltcG9ydCB7TWlncmF0aW9uQ3Rvcn0gZnJvbSAnLi4vdXBkYXRlLXRvb2wvbWlncmF0aW9uJztcbmltcG9ydCB7VGFyZ2V0VmVyc2lvbn0gZnJvbSAnLi4vdXBkYXRlLXRvb2wvdGFyZ2V0LXZlcnNpb24nO1xuaW1wb3J0IHtnZXRUYXJnZXRUc2NvbmZpZ1BhdGgsIGdldFdvcmtzcGFjZUNvbmZpZ0dyYWNlZnVsbHl9IGZyb20gJy4uL3V0aWxzL3Byb2plY3QtdHNjb25maWctcGF0aHMnO1xuXG5pbXBvcnQge0RldmtpdEZpbGVTeXN0ZW19IGZyb20gJy4vZGV2a2l0LWZpbGUtc3lzdGVtJztcbmltcG9ydCB7RGV2a2l0Q29udGV4dCwgRGV2a2l0TWlncmF0aW9uLCBEZXZraXRNaWdyYXRpb25DdG9yfSBmcm9tICcuL2RldmtpdC1taWdyYXRpb24nO1xuaW1wb3J0IHtmaW5kU3R5bGVzaGVldEZpbGVzfSBmcm9tICcuL2ZpbmQtc3R5bGVzaGVldHMnO1xuaW1wb3J0IHtBdHRyaWJ1dGVTZWxlY3RvcnNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9hdHRyaWJ1dGUtc2VsZWN0b3JzJztcbmltcG9ydCB7Q2xhc3NJbmhlcml0YW5jZU1pZ3JhdGlvbn0gZnJvbSAnLi9taWdyYXRpb25zL2NsYXNzLWluaGVyaXRhbmNlJztcbmltcG9ydCB7Q2xhc3NOYW1lc01pZ3JhdGlvbn0gZnJvbSAnLi9taWdyYXRpb25zL2NsYXNzLW5hbWVzJztcbmltcG9ydCB7Q29uc3RydWN0b3JTaWduYXR1cmVNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9jb25zdHJ1Y3Rvci1zaWduYXR1cmUnO1xuaW1wb3J0IHtDc3NTZWxlY3RvcnNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9jc3Mtc2VsZWN0b3JzJztcbmltcG9ydCB7Q3NzVG9rZW5zTWlncmF0aW9ufSBmcm9tICcuL21pZ3JhdGlvbnMvY3NzLXRva2Vucyc7XG5pbXBvcnQge0VsZW1lbnRTZWxlY3RvcnNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9lbGVtZW50LXNlbGVjdG9ycyc7XG5pbXBvcnQge0lucHV0TmFtZXNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9pbnB1dC1uYW1lcyc7XG5pbXBvcnQge01ldGhvZENhbGxBcmd1bWVudHNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9tZXRob2QtY2FsbC1hcmd1bWVudHMnO1xuaW1wb3J0IHtNaXNjVGVtcGxhdGVNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9taXNjLXRlbXBsYXRlJztcbmltcG9ydCB7T3V0cHV0TmFtZXNNaWdyYXRpb259IGZyb20gJy4vbWlncmF0aW9ucy9vdXRwdXQtbmFtZXMnO1xuaW1wb3J0IHtQcm9wZXJ0eU5hbWVzTWlncmF0aW9ufSBmcm9tICcuL21pZ3JhdGlvbnMvcHJvcGVydHktbmFtZXMnO1xuaW1wb3J0IHtTeW1ib2xSZW1vdmFsTWlncmF0aW9ufSBmcm9tICcuL21pZ3JhdGlvbnMvc3ltYm9sLXJlbW92YWwnO1xuaW1wb3J0IHtVcGdyYWRlRGF0YX0gZnJvbSAnLi91cGdyYWRlLWRhdGEnO1xuXG4vKiogTGlzdCBvZiBtaWdyYXRpb25zIHdoaWNoIHJ1biBmb3IgdGhlIENESyB1cGRhdGUuICovXG5leHBvcnQgY29uc3QgY2RrTWlncmF0aW9uczogTWlncmF0aW9uQ3RvcjxVcGdyYWRlRGF0YT5bXSA9IFtcbiAgQXR0cmlidXRlU2VsZWN0b3JzTWlncmF0aW9uLFxuICBDbGFzc0luaGVyaXRhbmNlTWlncmF0aW9uLFxuICBDbGFzc05hbWVzTWlncmF0aW9uLFxuICBDb25zdHJ1Y3RvclNpZ25hdHVyZU1pZ3JhdGlvbixcbiAgQ3NzU2VsZWN0b3JzTWlncmF0aW9uLFxuICBDc3NUb2tlbnNNaWdyYXRpb24sXG4gIEVsZW1lbnRTZWxlY3RvcnNNaWdyYXRpb24sXG4gIElucHV0TmFtZXNNaWdyYXRpb24sXG4gIE1ldGhvZENhbGxBcmd1bWVudHNNaWdyYXRpb24sXG4gIE1pc2NUZW1wbGF0ZU1pZ3JhdGlvbixcbiAgT3V0cHV0TmFtZXNNaWdyYXRpb24sXG4gIFByb3BlcnR5TmFtZXNNaWdyYXRpb24sXG4gIFN5bWJvbFJlbW92YWxNaWdyYXRpb24sXG5dO1xuXG5leHBvcnQgdHlwZSBOdWxsYWJsZURldmtpdE1pZ3JhdGlvbiA9IE1pZ3JhdGlvbkN0b3I8VXBncmFkZURhdGEgfCBudWxsLCBEZXZraXRDb250ZXh0PjtcblxudHlwZSBQb3N0TWlncmF0aW9uRm4gPSAoXG4gIGNvbnRleHQ6IFNjaGVtYXRpY0NvbnRleHQsXG4gIHRhcmdldFZlcnNpb246IFRhcmdldFZlcnNpb24sXG4gIGhhc0ZhaWx1cmU6IGJvb2xlYW4sXG4pID0+IHZvaWQ7XG5cbi8qKlxuICogQ3JlYXRlcyBhIEFuZ3VsYXIgc2NoZW1hdGljIHJ1bGUgdGhhdCBydW5zIHRoZSB1cGdyYWRlIGZvciB0aGVcbiAqIHNwZWNpZmllZCB0YXJnZXQgdmVyc2lvbi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZU1pZ3JhdGlvblNjaGVtYXRpY1J1bGUoXG4gIHRhcmdldFZlcnNpb246IFRhcmdldFZlcnNpb24sXG4gIGV4dHJhTWlncmF0aW9uczogTnVsbGFibGVEZXZraXRNaWdyYXRpb25bXSxcbiAgdXBncmFkZURhdGE6IFVwZ3JhZGVEYXRhLFxuICBvbk1pZ3JhdGlvbkNvbXBsZXRlRm4/OiBQb3N0TWlncmF0aW9uRm4sXG4pOiBSdWxlIHtcbiAgcmV0dXJuIGFzeW5jICh0cmVlOiBUcmVlLCBjb250ZXh0OiBTY2hlbWF0aWNDb250ZXh0KSA9PiB7XG4gICAgY29uc3QgbG9nZ2VyID0gY29udGV4dC5sb2dnZXI7XG4gICAgY29uc3Qgd29ya3NwYWNlID0gYXdhaXQgZ2V0V29ya3NwYWNlQ29uZmlnR3JhY2VmdWxseSh0cmVlKTtcblxuICAgIGlmICh3b3Jrc3BhY2UgPT09IG51bGwpIHtcbiAgICAgIGxvZ2dlci5lcnJvcignQ291bGQgbm90IGZpbmQgd29ya3NwYWNlIGNvbmZpZ3VyYXRpb24gZmlsZS4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBLZWVwIHRyYWNrIG9mIGFsbCBwcm9qZWN0IHNvdXJjZSBmaWxlcyB3aGljaCBoYXZlIGJlZW4gY2hlY2tlZC9taWdyYXRlZC4gVGhpcyBpc1xuICAgIC8vIG5lY2Vzc2FyeSBiZWNhdXNlIG11bHRpcGxlIFR5cGVTY3JpcHQgcHJvamVjdHMgY2FuIGNvbnRhaW4gdGhlIHNhbWUgc291cmNlIGZpbGUgYW5kXG4gICAgLy8gd2UgZG9uJ3Qgd2FudCB0byBjaGVjayB0aGVzZSBhZ2FpbiwgYXMgdGhpcyB3b3VsZCByZXN1bHQgaW4gZHVwbGljYXRlZCBmYWlsdXJlIG1lc3NhZ2VzLlxuICAgIGNvbnN0IGFuYWx5emVkRmlsZXMgPSBuZXcgU2V0PFdvcmtzcGFjZVBhdGg+KCk7XG4gICAgY29uc3QgZmlsZVN5c3RlbSA9IG5ldyBEZXZraXRGaWxlU3lzdGVtKHRyZWUpO1xuICAgIGNvbnN0IHByb2plY3ROYW1lcyA9IHdvcmtzcGFjZS5wcm9qZWN0cy5rZXlzKCk7XG4gICAgY29uc3QgbWlncmF0aW9ucyA9IFsuLi5jZGtNaWdyYXRpb25zLCAuLi5leHRyYU1pZ3JhdGlvbnNdIGFzIE51bGxhYmxlRGV2a2l0TWlncmF0aW9uW107XG4gICAgbGV0IGhhc0ZhaWx1cmVzID0gZmFsc2U7XG5cbiAgICBmb3IgKGNvbnN0IHByb2plY3ROYW1lIG9mIHByb2plY3ROYW1lcykge1xuICAgICAgY29uc3QgcHJvamVjdCA9IHdvcmtzcGFjZS5wcm9qZWN0cy5nZXQocHJvamVjdE5hbWUpITtcbiAgICAgIGNvbnN0IGJ1aWxkVHNjb25maWdQYXRoID0gZ2V0VGFyZ2V0VHNjb25maWdQYXRoKHByb2plY3QsICdidWlsZCcpO1xuICAgICAgY29uc3QgdGVzdFRzY29uZmlnUGF0aCA9IGdldFRhcmdldFRzY29uZmlnUGF0aChwcm9qZWN0LCAndGVzdCcpO1xuXG4gICAgICBpZiAoIWJ1aWxkVHNjb25maWdQYXRoICYmICF0ZXN0VHNjb25maWdQYXRoKSB7XG4gICAgICAgIGxvZ2dlci53YXJuKFxuICAgICAgICAgIGBTa2lwcGluZyBtaWdyYXRpb24gZm9yIHByb2plY3QgJHtwcm9qZWN0TmFtZX0uIFVuYWJsZSB0byBkZXRlcm1pbmUgJ3RzY29uZmlnLmpzb24nIGZpbGUgaW4gd29ya3NwYWNlIGNvbmZpZy5gLFxuICAgICAgICApO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgLy8gSW4gc29tZSBhcHBsaWNhdGlvbnMsIGRldmVsb3BlcnMgd2lsbCBoYXZlIGdsb2JhbCBzdHlsZXNoZWV0cyB3aGljaCBhcmUgbm90XG4gICAgICAvLyBzcGVjaWZpZWQgaW4gYW55IEFuZ3VsYXIgY29tcG9uZW50LiBUaGVyZWZvcmUgd2UgZ2xvYiB1cCBhbGwgQ1NTIGFuZCBTQ1NTIGZpbGVzXG4gICAgICAvLyBpbiB0aGUgcHJvamVjdCBhbmQgbWlncmF0ZSB0aGVtIGlmIG5lZWRlZC5cbiAgICAgIC8vIFRPRE86IHJld29yayB0aGlzIHRvIGNvbGxlY3QgZ2xvYmFsIHN0eWxlc2hlZXRzIGZyb20gdGhlIHdvcmtzcGFjZSBjb25maWcuXG4gICAgICAvLyBUT0RPOiBodHRwczovL2dpdGh1Yi5jb20vYW5ndWxhci9jb21wb25lbnRzL2lzc3Vlcy8yNDAzMi5cbiAgICAgIGNvbnN0IGFkZGl0aW9uYWxTdHlsZXNoZWV0UGF0aHMgPSBmaW5kU3R5bGVzaGVldEZpbGVzKHRyZWUsIHByb2plY3Qucm9vdCk7XG5cbiAgICAgIGlmIChidWlsZFRzY29uZmlnUGF0aCAhPT0gbnVsbCkge1xuICAgICAgICBydW5NaWdyYXRpb25zKHByb2plY3QsIHByb2plY3ROYW1lLCBidWlsZFRzY29uZmlnUGF0aCwgYWRkaXRpb25hbFN0eWxlc2hlZXRQYXRocywgZmFsc2UpO1xuICAgICAgfVxuICAgICAgaWYgKHRlc3RUc2NvbmZpZ1BhdGggIT09IG51bGwpIHtcbiAgICAgICAgcnVuTWlncmF0aW9ucyhwcm9qZWN0LCBwcm9qZWN0TmFtZSwgdGVzdFRzY29uZmlnUGF0aCwgYWRkaXRpb25hbFN0eWxlc2hlZXRQYXRocywgdHJ1ZSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgbGV0IHJ1blBhY2thZ2VNYW5hZ2VyID0gZmFsc2U7XG4gICAgLy8gUnVuIHRoZSBnbG9iYWwgcG9zdCBtaWdyYXRpb24gc3RhdGljIG1lbWJlcnMgZm9yIGFsbFxuICAgIC8vIHJlZ2lzdGVyZWQgZGV2a2l0IG1pZ3JhdGlvbnMuXG4gICAgbWlncmF0aW9ucy5mb3JFYWNoKG0gPT4ge1xuICAgICAgY29uc3QgYWN0aW9uUmVzdWx0ID1cbiAgICAgICAgaXNEZXZraXRNaWdyYXRpb24obSkgJiYgbS5nbG9iYWxQb3N0TWlncmF0aW9uICE9PSB1bmRlZmluZWRcbiAgICAgICAgICA/IG0uZ2xvYmFsUG9zdE1pZ3JhdGlvbih0cmVlLCB0YXJnZXRWZXJzaW9uLCBjb250ZXh0KVxuICAgICAgICAgIDogbnVsbDtcbiAgICAgIGlmIChhY3Rpb25SZXN1bHQpIHtcbiAgICAgICAgcnVuUGFja2FnZU1hbmFnZXIgPSBydW5QYWNrYWdlTWFuYWdlciB8fCBhY3Rpb25SZXN1bHQucnVuUGFja2FnZU1hbmFnZXI7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICAvLyBJZiBhIG1pZ3JhdGlvbiByZXF1ZXN0ZWQgdGhlIHBhY2thZ2UgbWFuYWdlciB0byBydW4sIHdlIHJ1biBpdCBhcyBhblxuICAgIC8vIGFzeW5jaHJvbm91cyBwb3N0IG1pZ3JhdGlvbiB0YXNrLiBXZSBjYW5ub3QgcnVuIGl0IHN5bmNocm9ub3VzbHksXG4gICAgLy8gYXMgZmlsZSBjaGFuZ2VzIGZyb20gdGhlIGN1cnJlbnQgbWlncmF0aW9uIHRhc2sgYXJlIG5vdCBhcHBsaWVkIHRvXG4gICAgLy8gdGhlIGZpbGUgc3lzdGVtIHlldC5cbiAgICBpZiAocnVuUGFja2FnZU1hbmFnZXIpIHtcbiAgICAgIGNvbnRleHQuYWRkVGFzayhuZXcgTm9kZVBhY2thZ2VJbnN0YWxsVGFzayh7cXVpZXQ6IGZhbHNlfSkpO1xuICAgIH1cblxuICAgIGlmIChvbk1pZ3JhdGlvbkNvbXBsZXRlRm4pIHtcbiAgICAgIG9uTWlncmF0aW9uQ29tcGxldGVGbihjb250ZXh0LCB0YXJnZXRWZXJzaW9uLCBoYXNGYWlsdXJlcyk7XG4gICAgfVxuXG4gICAgLyoqIFJ1bnMgdGhlIG1pZ3JhdGlvbnMgZm9yIHRoZSBzcGVjaWZpZWQgd29ya3NwYWNlIHByb2plY3QuICovXG4gICAgZnVuY3Rpb24gcnVuTWlncmF0aW9ucyhcbiAgICAgIHByb2plY3Q6IHdvcmtzcGFjZXMuUHJvamVjdERlZmluaXRpb24sXG4gICAgICBwcm9qZWN0TmFtZTogc3RyaW5nLFxuICAgICAgdHNjb25maWdQYXRoOiBXb3Jrc3BhY2VQYXRoLFxuICAgICAgYWRkaXRpb25hbFN0eWxlc2hlZXRQYXRoczogc3RyaW5nW10sXG4gICAgICBpc1Rlc3RUYXJnZXQ6IGJvb2xlYW4sXG4gICAgKSB7XG4gICAgICBjb25zdCBwcm9ncmFtID0gVXBkYXRlUHJvamVjdC5jcmVhdGVQcm9ncmFtRnJvbVRzY29uZmlnKHRzY29uZmlnUGF0aCwgZmlsZVN5c3RlbSk7XG4gICAgICBjb25zdCB1cGRhdGVDb250ZXh0OiBEZXZraXRDb250ZXh0ID0ge1xuICAgICAgICBpc1Rlc3RUYXJnZXQsXG4gICAgICAgIHByb2plY3ROYW1lLFxuICAgICAgICBwcm9qZWN0LFxuICAgICAgICB0cmVlLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgdXBkYXRlUHJvamVjdCA9IG5ldyBVcGRhdGVQcm9qZWN0KFxuICAgICAgICB1cGRhdGVDb250ZXh0LFxuICAgICAgICBwcm9ncmFtLFxuICAgICAgICBmaWxlU3lzdGVtLFxuICAgICAgICBhbmFseXplZEZpbGVzLFxuICAgICAgICBjb250ZXh0LmxvZ2dlcixcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHVwZGF0ZVByb2plY3QubWlncmF0ZShcbiAgICAgICAgbWlncmF0aW9ucyxcbiAgICAgICAgdGFyZ2V0VmVyc2lvbixcbiAgICAgICAgdXBncmFkZURhdGEsXG4gICAgICAgIGFkZGl0aW9uYWxTdHlsZXNoZWV0UGF0aHMsXG4gICAgICApO1xuXG4gICAgICAvLyBDb21taXQgYWxsIHJlY29yZGVkIGVkaXRzIGluIHRoZSB1cGRhdGUgcmVjb3JkZXIuIFdlIGFwcGx5IHRoZSBlZGl0cyBhZnRlciBhbGxcbiAgICAgIC8vIG1pZ3JhdGlvbnMgcmFuIGJlY2F1c2Ugb3RoZXJ3aXNlIG9mZnNldHMgaW4gdGhlIFR5cGVTY3JpcHQgcHJvZ3JhbSB3b3VsZCBiZVxuICAgICAgLy8gc2hpZnRlZCBhbmQgaW5kaXZpZHVhbCBtaWdyYXRpb25zIGNvdWxkIG5vIGxvbmdlciB1cGRhdGUgdGhlIHNhbWUgc291cmNlIGZpbGUuXG4gICAgICBmaWxlU3lzdGVtLmNvbW1pdEVkaXRzKCk7XG5cbiAgICAgIGhhc0ZhaWx1cmVzID0gaGFzRmFpbHVyZXMgfHwgcmVzdWx0Lmhhc0ZhaWx1cmVzO1xuICAgIH1cbiAgfTtcbn1cblxuLyoqIFdoZXRoZXIgdGhlIGdpdmVuIG1pZ3JhdGlvbiB0eXBlIHJlZmVycyB0byBhIGRldmtpdCBtaWdyYXRpb24gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0RldmtpdE1pZ3JhdGlvbihcbiAgdmFsdWU6IE1pZ3JhdGlvbkN0b3I8YW55LCBhbnk+LFxuKTogdmFsdWUgaXMgRGV2a2l0TWlncmF0aW9uQ3Rvcjxhbnk+IHtcbiAgcmV0dXJuIERldmtpdE1pZ3JhdGlvbi5pc1Byb3RvdHlwZU9mKHZhbHVlKTtcbn1cbiJdfQ==