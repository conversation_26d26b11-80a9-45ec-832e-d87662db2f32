"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevkitMigration = void 0;
const migration_1 = require("../update-tool/migration");
class DevkitMigration extends migration_1.Migration {
    /** Prints an informative message with context on the current target. */
    printInfo(text) {
        const targetName = this.context.isTestTarget ? 'test' : 'build';
        this.logger.info(`- ${this.context.projectName}@${targetName}: ${text}`);
    }
}
exports.DevkitMigration = DevkitMigration;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGV2a2l0LW1pZ3JhdGlvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvZGV2a2l0LW1pZ3JhdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFJSCx3REFBcUY7QUFjckYsTUFBc0IsZUFBc0IsU0FBUSxxQkFBOEI7SUFDaEYsd0VBQXdFO0lBQzlELFNBQVMsQ0FBQyxJQUFZO1FBQzlCLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQztRQUNoRSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxJQUFJLFVBQVUsS0FBSyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQzNFLENBQUM7Q0FhRjtBQWxCRCwwQ0FrQkMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCB7U2NoZW1hdGljQ29udGV4dCwgVHJlZX0gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L3NjaGVtYXRpY3MnO1xuaW1wb3J0IHt3b3Jrc3BhY2VzfSBmcm9tICdAYW5ndWxhci1kZXZraXQvY29yZSc7XG5pbXBvcnQge0NvbnN0cnVjdG9yLCBNaWdyYXRpb24sIFBvc3RNaWdyYXRpb25BY3Rpb259IGZyb20gJy4uL3VwZGF0ZS10b29sL21pZ3JhdGlvbic7XG5pbXBvcnQge1RhcmdldFZlcnNpb259IGZyb20gJy4uL3VwZGF0ZS10b29sL3RhcmdldC12ZXJzaW9uJztcblxuZXhwb3J0IHR5cGUgRGV2a2l0Q29udGV4dCA9IHtcbiAgLyoqIERldmtpdCB0cmVlIGZvciB0aGUgY3VycmVudCBtaWdyYXRpb25zLiBDYW4gYmUgdXNlZCB0byBpbnNlcnQvcmVtb3ZlIGZpbGVzLiAqL1xuICB0cmVlOiBUcmVlO1xuICAvKiogTmFtZSBvZiB0aGUgcHJvamVjdCB0aGUgbWlncmF0aW9ucyBydW4gYWdhaW5zdC4gKi9cbiAgcHJvamVjdE5hbWU6IHN0cmluZztcbiAgLyoqIFdvcmtzcGFjZSBwcm9qZWN0IHRoZSBtaWdyYXRpb25zIHJ1biBhZ2FpbnN0LiAqL1xuICBwcm9qZWN0OiB3b3Jrc3BhY2VzLlByb2plY3REZWZpbml0aW9uO1xuICAvKiogV2hldGhlciB0aGUgbWlncmF0aW9ucyBydW4gZm9yIGEgdGVzdCB0YXJnZXQuICovXG4gIGlzVGVzdFRhcmdldDogYm9vbGVhbjtcbn07XG5cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBEZXZraXRNaWdyYXRpb248RGF0YT4gZXh0ZW5kcyBNaWdyYXRpb248RGF0YSwgRGV2a2l0Q29udGV4dD4ge1xuICAvKiogUHJpbnRzIGFuIGluZm9ybWF0aXZlIG1lc3NhZ2Ugd2l0aCBjb250ZXh0IG9uIHRoZSBjdXJyZW50IHRhcmdldC4gKi9cbiAgcHJvdGVjdGVkIHByaW50SW5mbyh0ZXh0OiBzdHJpbmcpIHtcbiAgICBjb25zdCB0YXJnZXROYW1lID0gdGhpcy5jb250ZXh0LmlzVGVzdFRhcmdldCA/ICd0ZXN0JyA6ICdidWlsZCc7XG4gICAgdGhpcy5sb2dnZXIuaW5mbyhgLSAke3RoaXMuY29udGV4dC5wcm9qZWN0TmFtZX1AJHt0YXJnZXROYW1lfTogJHt0ZXh0fWApO1xuICB9XG5cbiAgLyoqXG4gICAqIE9wdGlvbmFsIHN0YXRpYyBtZXRob2QgdGhhdCB3aWxsIGJlIGNhbGxlZCBvbmNlIHRoZSBtaWdyYXRpb24gb2YgYWxsIHByb2plY3RcbiAgICogdGFyZ2V0cyBoYXMgYmVlbiBwZXJmb3JtZWQuIFRoaXMgbWV0aG9kIGNhbiBiZSB1c2VkIHRvIG1ha2UgY2hhbmdlcyByZXNwZWN0aW5nIHRoZVxuICAgKiBtaWdyYXRpb24gcmVzdWx0IG9mIGFsbCBpbmRpdmlkdWFsIHRhcmdldHMuIGUuZy4gcmVtb3ZpbmcgSGFtbWVySlMgaWYgaXRcbiAgICogaXMgbm90IG5lZWRlZCBpbiBhbnkgcHJvamVjdCB0YXJnZXQuXG4gICAqL1xuICBzdGF0aWMgZ2xvYmFsUG9zdE1pZ3JhdGlvbj8oXG4gICAgdHJlZTogVHJlZSxcbiAgICB0YXJnZXRWZXJzaW9uOiBUYXJnZXRWZXJzaW9uLFxuICAgIGNvbnRleHQ6IFNjaGVtYXRpY0NvbnRleHQsXG4gICk6IFBvc3RNaWdyYXRpb25BY3Rpb247XG59XG5cbmV4cG9ydCB0eXBlIERldmtpdE1pZ3JhdGlvbkN0b3I8RGF0YT4gPSBDb25zdHJ1Y3RvcjxEZXZraXRNaWdyYXRpb248RGF0YT4+ICYge1xuICBbbSBpbiBrZXlvZiB0eXBlb2YgRGV2a2l0TWlncmF0aW9uXTogKHR5cGVvZiBEZXZraXRNaWdyYXRpb24pW21dO1xufTtcbiJdfQ==