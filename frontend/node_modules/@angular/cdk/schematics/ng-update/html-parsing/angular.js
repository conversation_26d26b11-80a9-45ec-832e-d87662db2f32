"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.findInputsOnElementWithTag = findInputsOnElementWithTag;
exports.findInputsOnElementWithAttr = findInputsOnElementWithAttr;
exports.findOutputsOnElementWithTag = findOutputsOnElementWithTag;
exports.findOutputsOnElementWithAttr = findOutputsOnElementWithAttr;
const elements_1 = require("./elements");
/** Finds the specified Angular @Input in the given elements with tag name. */
function findInputsOnElementWithTag(html, inputName, tagNames) {
    return [
        // Inputs can be also used without brackets (e.g. `<mat-toolbar color="primary">`)
        ...(0, elements_1.findAttributeOnElementWithTag)(html, inputName, tagNames),
        // Add one column to the mapped offset because the first bracket for the @Input
        // is part of the attribute and therefore also part of the offset. We only want to return
        // the offset for the inner name of the bracketed input.
        ...(0, elements_1.findAttributeOnElementWithTag)(html, `[${inputName}]`, tagNames).map(offset => offset + 1),
    ];
}
/** Finds the specified Angular @Input in elements that have one of the specified attributes. */
function findInputsOnElementWithAttr(html, inputName, attrs) {
    return [
        // Inputs can be also used without brackets (e.g. `<button mat-button color="primary">`)
        ...(0, elements_1.findAttributeOnElementWithAttrs)(html, inputName, attrs),
        // Add one column to the mapped offset because the first bracket for the @Input
        // is part of the attribute and therefore also part of the offset. We only want to return
        // the offset for the inner name of the bracketed input.
        ...(0, elements_1.findAttributeOnElementWithAttrs)(html, `[${inputName}]`, attrs).map(offset => offset + 1),
    ];
}
/** Finds the specified Angular @Output in the given elements with tag name. */
function findOutputsOnElementWithTag(html, outputName, tagNames) {
    // Add one column to the mapped offset because the first parenthesis for the @Output
    // is part of the attribute and therefore also part of the offset. We only want to return
    // the offset for the inner name of the output.
    return (0, elements_1.findAttributeOnElementWithTag)(html, `(${outputName})`, tagNames).map(offset => offset + 1);
}
/** Finds the specified Angular @Output in elements that have one of the specified attributes. */
function findOutputsOnElementWithAttr(html, outputName, attrs) {
    // Add one column to the mapped offset because the first bracket for the @Output
    // is part of the attribute and therefore also part of the offset. We only want to return
    // the offset for the inner name of the output.
    return (0, elements_1.findAttributeOnElementWithAttrs)(html, `(${outputName})`, attrs).map(offset => offset + 1);
}
//# sourceMappingURL=data:application/json;base64,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