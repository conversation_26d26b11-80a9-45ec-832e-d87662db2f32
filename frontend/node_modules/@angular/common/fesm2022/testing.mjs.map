{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/common/src/navigation/platform_navigation.ts", "../../../../../../packages/common/testing/src/navigation/fake_navigation.ts", "../../../../../../packages/common/testing/src/mock_platform_location.ts", "../../../../../../packages/common/testing/src/navigation/provide_fake_platform_navigation.ts", "../../../../../../packages/common/testing/src/location_mock.ts", "../../../../../../packages/common/testing/src/mock_location_strategy.ts", "../../../../../../packages/common/testing/src/provide_location_mocks.ts", "../../../../../../packages/common/testing/src/testing.ts", "../../../../../../packages/common/testing/public_api.ts", "../../../../../../packages/common/testing/index.ts", "../../../../../../packages/common/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\n\nimport {\n  NavigateEvent,\n  Navigation,\n  NavigationCurrentEntryChangeEvent,\n  NavigationHistoryEntry,\n  NavigationNavigateOptions,\n  NavigationOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationTransition,\n  NavigationUpdateCurrentEntryOptions,\n} from './navigation_types';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\n@Injectable({providedIn: 'platform', useFactory: () => (window as any).navigation})\nexport abstract class PlatformNavigation implements Navigation {\n  abstract entries(): NavigationHistoryEntry[];\n  abstract currentEntry: NavigationHistoryEntry | null;\n  abstract updateCurrentEntry(options: NavigationUpdateCurrentEntryOptions): void;\n  abstract transition: NavigationTransition | null;\n  abstract canGoBack: boolean;\n  abstract canGoForward: boolean;\n  abstract navigate(url: string, options?: NavigationNavigateOptions | undefined): NavigationResult;\n  abstract reload(options?: NavigationReloadOptions | undefined): NavigationResult;\n  abstract traverseTo(key: string, options?: NavigationOptions | undefined): NavigationResult;\n  abstract back(options?: NavigationOptions | undefined): NavigationResult;\n  abstract forward(options?: NavigationOptions | undefined): NavigationResult;\n  abstract onnavigate: ((this: Navigation, ev: NavigateEvent) => any) | null;\n  abstract onnavigatesuccess: ((this: Navigation, ev: Event) => any) | null;\n  abstract onnavigateerror: ((this: Navigation, ev: ErrorEvent) => any) | null;\n  abstract oncurrententrychange:\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null;\n  abstract addEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract removeEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract dispatchEvent(event: Event): boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  NavigateEvent,\n  Navigation,\n  NavigationCurrentEntryChangeEvent,\n  NavigationDestination,\n  NavigationHistoryEntry,\n  NavigationInterceptOptions,\n  NavigationNavigateOptions,\n  NavigationOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationTransition,\n  NavigationTypeString,\n  NavigationUpdateCurrentEntryOptions,\n} from './navigation_types';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nexport class FakeNavigation implements Navigation {\n  /**\n   * The fake implementation of an entries array. Only same-document entries\n   * allowed.\n   */\n  private readonly entriesArr: FakeNavigationHistoryEntry[] = [];\n\n  /**\n   * The current active entry index into `entriesArr`.\n   */\n  private currentEntryIndex = 0;\n\n  /**\n   * The current navigate event.\n   */\n  private navigateEvent: InternalFakeNavigateEvent | undefined = undefined;\n\n  /**\n   * A Map of pending traversals, so that traversals to the same entry can be\n   * re-used.\n   */\n  private readonly traversalQueue = new Map<string, InternalNavigationResult>();\n\n  /**\n   * A Promise that resolves when the previous traversals have finished. Used to\n   * simulate the cross-process communication necessary for traversals.\n   */\n  private nextTraversal = Promise.resolve();\n\n  /**\n   * A prospective current active entry index, which includes unresolved\n   * traversals. Used by `go` to determine where navigations are intended to go.\n   */\n  private prospectiveEntryIndex = 0;\n\n  /**\n   * A test-only option to make traversals synchronous, rather than emulate\n   * cross-process communication.\n   */\n  private synchronousTraversals = false;\n\n  /** Whether to allow a call to setInitialEntryForTesting. */\n  private canSetInitialEntry = true;\n\n  /** `EventTarget` to dispatch events. */\n  private eventTarget: EventTarget;\n\n  /** The next unique id for created entries. Replace recreates this id. */\n  private nextId = 0;\n\n  /** The next unique key for created entries. Replace inherits this id. */\n  private nextKey = 0;\n\n  /** Whether this fake is disposed. */\n  private disposed = false;\n\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry(): FakeNavigationHistoryEntry {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n\n  get canGoBack(): boolean {\n    return this.currentEntryIndex > 0;\n  }\n\n  get canGoForward(): boolean {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n\n  constructor(\n    private readonly window: Window,\n    startURL: `http${string}`,\n  ) {\n    this.eventTarget = this.window.document.createElement('div');\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n\n  /**\n   * Sets the initial entry.\n   */\n  private setInitialEntryForTesting(\n    url: `http${string}`,\n    options: {historyState: unknown; state?: unknown} = {historyState: null},\n  ) {\n    if (!this.canSetInitialEntry) {\n      throw new Error(\n        'setInitialEntryForTesting can only be called before any ' + 'navigation has occurred',\n      );\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(new URL(url).toString(), {\n      index: 0,\n      key: currentInitialEntry?.key ?? String(this.nextKey++),\n      id: currentInitialEntry?.id ?? String(this.nextId++),\n      sameDocument: true,\n      historyState: options?.historyState,\n      state: options.state,\n    });\n  }\n\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting(): boolean {\n    return this.canSetInitialEntry;\n  }\n\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals: boolean) {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n\n  /** Equivalent to `navigation.entries()`. */\n  entries(): FakeNavigationHistoryEntry[] {\n    return this.entriesArr.slice();\n  }\n\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url: string, options?: NavigationNavigateOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = new URL(url, this.currentEntry.url!);\n\n    let navigationType: NavigationTypeString;\n    if (!options?.history || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options?.state,\n      sameDocument: hashChange,\n      historyState: null,\n    });\n    const result = new InternalNavigationResult();\n\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options?.info,\n    });\n\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `history.pushState()`. */\n  pushState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n\n  private pushOrReplaceState(\n    navigationType: NavigationTypeString,\n    data: unknown,\n    _title: string,\n    url?: string,\n  ): void {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = url ? new URL(url, this.currentEntry.url!) : fromUrl;\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true,\n      historyState: data,\n    });\n    const result = new InternalNavigationResult();\n\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange,\n      skipPopState: true,\n    });\n  }\n\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key: string, options?: NavigationOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry),\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key)!;\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished,\n      };\n    }\n\n    const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n    const destination = new FakeNavigationDestination({\n      url: entry.url!,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument,\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult();\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options?.info,\n      });\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `navigation.back()`. */\n  back(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /** Equivalent to `navigation.forward()`. */\n  forward(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction: number): void {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url!);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n      const destination = new FakeNavigationDestination({\n        url: entry.url!,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument,\n      });\n      const result = new InternalNavigationResult();\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange,\n      });\n    });\n  }\n\n  /** Runs a traversal synchronously or asynchronously */\n  private runTraversal(traversal: () => void) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise<void>((resolve) => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event: Event): boolean {\n    return this.eventTarget.dispatchEvent(event);\n  }\n\n  /** Cleans up resources. */\n  dispose() {\n    // Recreate eventTarget to release current listeners.\n    // `document.createElement` because NodeJS `EventTarget` is incompatible with Domino's `Event`.\n    this.eventTarget = this.window.document.createElement('div');\n    this.disposed = true;\n  }\n\n  /** Returns whether this fake is disposed. */\n  isDisposed() {\n    return this.disposed;\n  }\n\n  /** Implementation for all navigations and traversals. */\n  private userAgentNavigate(\n    destination: FakeNavigationDestination,\n    result: InternalNavigationResult,\n    options: InternalNavigateOptions,\n  ) {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n      this.navigateEvent = undefined;\n    }\n\n    const navigateEvent = createFakeNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      signal: result.signal,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      skipPopState: options.skipPopState,\n      result,\n      userAgentCommit: () => {\n        this.userAgentCommit();\n      },\n    });\n\n    this.navigateEvent = navigateEvent;\n    this.eventTarget.dispatchEvent(navigateEvent);\n    navigateEvent.dispatchedNavigateEvent();\n    if (navigateEvent.commitOption === 'immediate') {\n      navigateEvent.commit(/* internal= */ true);\n    }\n  }\n\n  /** Implementation to commit a navigation. */\n  private userAgentCommit() {\n    if (!this.navigateEvent) {\n      return;\n    }\n    const from = this.currentEntry;\n    if (!this.navigateEvent.sameDocument) {\n      const error = new Error('Cannot navigate to a non-same-document URL.');\n      this.navigateEvent.cancel(error);\n      throw error;\n    }\n    if (\n      this.navigateEvent.navigationType === 'push' ||\n      this.navigateEvent.navigationType === 'replace'\n    ) {\n      this.userAgentPushOrReplace(this.navigateEvent.destination, {\n        navigationType: this.navigateEvent.navigationType,\n      });\n    } else if (this.navigateEvent.navigationType === 'traverse') {\n      this.userAgentTraverse(this.navigateEvent.destination);\n    }\n    this.navigateEvent.userAgentNavigated(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from,\n      navigationType: this.navigateEvent.navigationType,\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    if (!this.navigateEvent.skipPopState) {\n      const popStateEvent = createPopStateEvent({\n        state: this.navigateEvent.destination.getHistoryState(),\n      });\n      this.window.dispatchEvent(popStateEvent);\n    }\n  }\n\n  /** Implementation for a push or replace navigation. */\n  private userAgentPushOrReplace(\n    destination: FakeNavigationDestination,\n    {navigationType}: {navigationType: NavigationTypeString},\n  ) {\n    if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex;\n    }\n    const index = this.currentEntryIndex;\n    const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n    const entry = new FakeNavigationHistoryEntry(destination.url, {\n      id: String(this.nextId++),\n      key,\n      index,\n      sameDocument: true,\n      state: destination.getState(),\n      historyState: destination.getHistoryState(),\n    });\n    if (navigationType === 'push') {\n      this.entriesArr.splice(index, Infinity, entry);\n    } else {\n      this.entriesArr[index] = entry;\n    }\n  }\n\n  /** Implementation for a traverse navigation. */\n  private userAgentTraverse(destination: FakeNavigationDestination) {\n    this.currentEntryIndex = destination.index;\n  }\n\n  /** Utility method for finding entries with the given `key`. */\n  private findEntry(key: string) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n\n  set onnavigate(_handler: ((this: Navigation, ev: NavigateEvent) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigate(): ((this: Navigation, ev: NavigateEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set oncurrententrychange(\n    _handler: ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  get oncurrententrychange():\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigatesuccess(_handler: ((this: Navigation, ev: Event) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigatesuccess(): ((this: Navigation, ev: Event) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigateerror(_handler: ((this: Navigation, ev: ErrorEvent) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigateerror(): ((this: Navigation, ev: ErrorEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  get transition(): NavigationTransition | null {\n    throw new Error('unimplemented');\n  }\n\n  updateCurrentEntry(_options: NavigationUpdateCurrentEntryOptions): void {\n    throw new Error('unimplemented');\n  }\n\n  reload(_options?: NavigationReloadOptions): NavigationResult {\n    throw new Error('unimplemented');\n  }\n}\n\n/**\n * Fake equivalent of the `NavigationResult` interface with\n * `FakeNavigationHistoryEntry`.\n */\ninterface FakeNavigationResult extends NavigationResult {\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n}\n\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nexport class FakeNavigationHistoryEntry implements NavigationHistoryEntry {\n  readonly sameDocument;\n\n  readonly id: string;\n  readonly key: string;\n  readonly index: number;\n  private readonly state: unknown;\n  private readonly historyState: unknown;\n\n  ondispose: ((this: NavigationHistoryEntry, ev: Event) => any) | null = null;\n\n  constructor(\n    readonly url: string | null,\n    {\n      id,\n      key,\n      index,\n      sameDocument,\n      state,\n      historyState,\n    }: {\n      id: string;\n      key: string;\n      index: number;\n      sameDocument: boolean;\n      historyState: unknown;\n      state?: unknown;\n    },\n  ) {\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n\n  getState(): unknown {\n    // Budget copy.\n    return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n  }\n\n  getHistoryState(): unknown {\n    // Budget copy.\n    return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n  }\n\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  dispatchEvent(event: Event): boolean {\n    throw new Error('unimplemented');\n  }\n}\n\n/** `NavigationInterceptOptions` with experimental commit option. */\nexport interface ExperimentalNavigationInterceptOptions extends NavigationInterceptOptions {\n  commit?: 'immediate' | 'after-transition';\n}\n\n/** `NavigateEvent` with experimental commit function. */\nexport interface ExperimentalNavigateEvent extends NavigateEvent {\n  intercept(options?: ExperimentalNavigationInterceptOptions): void;\n\n  commit(): void;\n}\n\n/**\n * Fake equivalent of `NavigateEvent`.\n */\nexport interface FakeNavigateEvent extends ExperimentalNavigateEvent {\n  readonly destination: FakeNavigationDestination;\n}\n\ninterface InternalFakeNavigateEvent extends FakeNavigateEvent {\n  readonly sameDocument: boolean;\n  readonly skipPopState?: boolean;\n  readonly commitOption: 'after-transition' | 'immediate';\n  readonly result: InternalNavigationResult;\n\n  commit(internal?: boolean): void;\n  cancel(reason: Error): void;\n  dispatchedNavigateEvent(): void;\n  userAgentNavigated(entry: FakeNavigationHistoryEntry): void;\n}\n\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  signal,\n  destination,\n  info,\n  sameDocument,\n  skipPopState,\n  result,\n  userAgentCommit,\n}: {\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  navigationType: NavigationTypeString;\n  signal: AbortSignal;\n  destination: FakeNavigationDestination;\n  info: unknown;\n  sameDocument: boolean;\n  skipPopState?: boolean;\n  result: InternalNavigationResult;\n  userAgentCommit: () => void;\n}) {\n  const event = new Event('navigate', {bubbles: false, cancelable}) as {\n    -readonly [P in keyof InternalFakeNavigateEvent]: InternalFakeNavigateEvent[P];\n  };\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.navigationType = navigationType;\n  event.signal = signal;\n  event.destination = destination;\n  event.info = info;\n  event.downloadRequest = null;\n  event.formData = null;\n\n  event.sameDocument = sameDocument;\n  event.skipPopState = skipPopState;\n  event.commitOption = 'immediate';\n\n  let handlerFinished: Promise<void> | undefined = undefined;\n  let interceptCalled = false;\n  let dispatchedNavigateEvent = false;\n  let commitCalled = false;\n\n  event.intercept = function (\n    this: InternalFakeNavigateEvent,\n    options?: ExperimentalNavigationInterceptOptions,\n  ): void {\n    interceptCalled = true;\n    event.sameDocument = true;\n    const handler = options?.handler;\n    if (handler) {\n      handlerFinished = handler();\n    }\n    if (options?.commit) {\n      event.commitOption = options.commit;\n    }\n    if (options?.focusReset !== undefined || options?.scroll !== undefined) {\n      throw new Error('unimplemented');\n    }\n  };\n\n  event.scroll = function (this: InternalFakeNavigateEvent): void {\n    throw new Error('unimplemented');\n  };\n\n  event.commit = function (this: InternalFakeNavigateEvent, internal = false) {\n    if (!internal && !interceptCalled) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': intercept() must be ` +\n          `called before commit().`,\n        'InvalidStateError',\n      );\n    }\n    if (!dispatchedNavigateEvent) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': commit() may not be ` +\n          `called during event dispatch.`,\n        'InvalidStateError',\n      );\n    }\n    if (commitCalled) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': commit() already ` + `called.`,\n        'InvalidStateError',\n      );\n    }\n    commitCalled = true;\n\n    userAgentCommit();\n  };\n\n  // Internal only.\n  event.cancel = function (this: InternalFakeNavigateEvent, reason: Error) {\n    result.committedReject(reason);\n    result.finishedReject(reason);\n  };\n\n  // Internal only.\n  event.dispatchedNavigateEvent = function (this: InternalFakeNavigateEvent) {\n    dispatchedNavigateEvent = true;\n    if (event.commitOption === 'after-transition') {\n      // If handler finishes before commit, call commit.\n      handlerFinished?.then(\n        () => {\n          if (!commitCalled) {\n            event.commit(/* internal */ true);\n          }\n        },\n        () => {},\n      );\n    }\n    Promise.all([result.committed, handlerFinished]).then(\n      ([entry]) => {\n        result.finishedResolve(entry);\n      },\n      (reason) => {\n        result.finishedReject(reason);\n      },\n    );\n  };\n\n  // Internal only.\n  event.userAgentNavigated = function (\n    this: InternalFakeNavigateEvent,\n    entry: FakeNavigationHistoryEntry,\n  ) {\n    result.committedResolve(entry);\n  };\n\n  return event as InternalFakeNavigateEvent;\n}\n\n/** Fake equivalent of `NavigationCurrentEntryChangeEvent`. */\nexport interface FakeNavigationCurrentEntryChangeEvent extends NavigationCurrentEntryChangeEvent {\n  readonly from: FakeNavigationHistoryEntry;\n}\n\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType,\n}: {\n  from: FakeNavigationHistoryEntry;\n  navigationType: NavigationTypeString;\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false,\n  }) as {\n    -readonly [P in keyof NavigationCurrentEntryChangeEvent]: NavigationCurrentEntryChangeEvent[P];\n  };\n  event.from = from;\n  event.navigationType = navigationType;\n  return event as FakeNavigationCurrentEntryChangeEvent;\n}\n\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({state}: {state: unknown}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false,\n  }) as {-readonly [P in keyof PopStateEvent]: PopStateEvent[P]};\n  event.state = state;\n  return event as PopStateEvent;\n}\n\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nexport class FakeNavigationDestination implements NavigationDestination {\n  readonly url: string;\n  readonly sameDocument: boolean;\n  readonly key: string | null;\n  readonly id: string | null;\n  readonly index: number;\n\n  private readonly state?: unknown;\n  private readonly historyState: unknown;\n\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1,\n  }: {\n    url: string;\n    sameDocument: boolean;\n    historyState: unknown;\n    state?: unknown;\n    key?: string | null;\n    id?: string | null;\n    index?: number;\n  }) {\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  getHistoryState(): unknown {\n    return this.historyState;\n  }\n}\n\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from: URL, to: URL): boolean {\n  return (\n    to.hash !== from.hash &&\n    to.hostname === from.hostname &&\n    to.pathname === from.pathname &&\n    to.search === from.search\n  );\n}\n\n/** Internal utility class for representing the result of a navigation.  */\nclass InternalNavigationResult {\n  committedResolve!: (entry: FakeNavigationHistoryEntry) => void;\n  committedReject!: (reason: Error) => void;\n  finishedResolve!: (entry: FakeNavigationHistoryEntry) => void;\n  finishedReject!: (reason: Error) => void;\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n  get signal(): AbortSignal {\n    return this.abortController.signal;\n  }\n  private readonly abortController = new AbortController();\n\n  constructor() {\n    this.committed = new Promise<FakeNavigationHistoryEntry>((resolve, reject) => {\n      this.committedResolve = resolve;\n      this.committedReject = reject;\n    });\n\n    this.finished = new Promise<FakeNavigationHistoryEntry>(async (resolve, reject) => {\n      this.finishedResolve = resolve;\n      this.finishedReject = (reason: Error) => {\n        reject(reason);\n        this.abortController.abort(reason);\n      };\n    });\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\n\n/** Internal options for performing a navigate. */\ninterface InternalNavigateOptions {\n  navigationType: NavigationTypeString;\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  info?: unknown;\n  skipPopState?: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOCUMENT,\n  LocationChangeEvent,\n  LocationChangeListener,\n  PlatformLocation,\n  ɵPlatformNavigation as PlatformNavigation,\n} from '@angular/common';\nimport {Inject, inject, Injectable, InjectionToken, Optional} from '@angular/core';\nimport {Subject} from 'rxjs';\n\nimport {FakeNavigation} from './navigation/fake_navigation';\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\nfunction parseUrl(urlStr: string, baseHref: string) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase: string | undefined;\n\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl: {\n    protocol: string;\n    hostname: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n  };\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8],\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: (!serverBase && parsedUrl.hostname) || '',\n    protocol: (!serverBase && parsedUrl.protocol) || '',\n    port: (!serverBase && parsedUrl.port) || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || '',\n  };\n}\n\n/**\n * Mock platform location config\n *\n * @publicApi\n */\nexport interface MockPlatformLocationConfig {\n  startUrl?: string;\n  appBaseHref?: string;\n}\n\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nexport const MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken<MockPlatformLocationConfig>(\n  'MOCK_PLATFORM_LOCATION_CONFIG',\n);\n\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockPlatformLocation implements PlatformLocation {\n  private baseHref: string = '';\n  private hashUpdate = new Subject<LocationChangeEvent>();\n  private popStateSubject = new Subject<LocationChangeEvent>();\n  private urlChangeIndex: number = 0;\n  private urlChanges: {\n    hostname: string;\n    protocol: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n    state: unknown;\n  }[] = [{hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null}];\n\n  constructor(\n    @Inject(MOCK_PLATFORM_LOCATION_CONFIG) @Optional() config?: MockPlatformLocationConfig,\n  ) {\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n\n      const parsedChanges = this.parseChanges(\n        null,\n        config.startUrl || 'http://_empty_/',\n        this.baseHref,\n      );\n      this.urlChanges[0] = {...parsedChanges};\n    }\n  }\n\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n\n  getBaseHrefFromDOM(): string {\n    return this.baseHref;\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  get href(): string {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n\n  get url(): string {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n\n  private parseChanges(state: unknown, url: string, baseHref: string = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {...parseUrl(url, baseHref), state};\n  }\n\n  replaceState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    };\n  }\n\n  pushState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n\n  forward(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  back(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  private emitEvents(oldHash: string, oldUrl: string) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url,\n    } as LocationChangeEvent);\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url,\n      } as LocationChangeEvent);\n    }\n  }\n}\n\n/**\n * Mock implementation of URL state.\n */\n@Injectable()\nexport class FakeNavigationPlatformLocation implements PlatformLocation {\n  private _platformNavigation = inject(PlatformNavigation) as FakeNavigation;\n  private window = inject(DOCUMENT).defaultView!;\n\n  constructor() {\n    if (!(this._platformNavigation instanceof FakeNavigation)) {\n      throw new Error(\n        'FakePlatformNavigation cannot be used without FakeNavigation. Use ' +\n          '`provideFakeNavigation` to have all these services provided together.',\n      );\n    }\n  }\n\n  private config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n  getBaseHrefFromDOM(): string {\n    return this.config?.appBaseHref ?? '';\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    this.window.addEventListener('popstate', fn);\n    return () => this.window.removeEventListener('popstate', fn);\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    this.window.addEventListener('hashchange', fn as any);\n    return () => this.window.removeEventListener('hashchange', fn as any);\n  }\n\n  get href(): string {\n    return this._platformNavigation.currentEntry.url!;\n  }\n  get protocol(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).protocol;\n  }\n  get hostname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hostname;\n  }\n  get port(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).port;\n  }\n  get pathname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).pathname;\n  }\n  get search(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).search;\n  }\n  get hash(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hash;\n  }\n\n  pushState(state: any, title: string, url: string): void {\n    this._platformNavigation.pushState(state, title, url);\n  }\n\n  replaceState(state: any, title: string, url: string): void {\n    this._platformNavigation.replaceState(state, title, url);\n  }\n\n  forward(): void {\n    this._platformNavigation.forward();\n  }\n\n  back(): void {\n    this._platformNavigation.back();\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    this._platformNavigation.go(relativePosition);\n  }\n\n  getState(): unknown {\n    return this._platformNavigation.currentEntry.getHistoryState();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, PlatformLocation} from '@angular/common';\nimport {inject, Provider} from '@angular/core';\n\n// @ng_package: ignore-cross-repo-import\nimport {PlatformNavigation} from '../../../src/navigation/platform_navigation';\nimport {\n  FakeNavigationPlatformLocation,\n  MOCK_PLATFORM_LOCATION_CONFIG,\n} from '../mock_platform_location';\n\nimport {FakeNavigation} from './fake_navigation';\n\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nexport function provideFakePlatformNavigation(): Provider[] {\n  return [\n    {\n      provide: PlatformNavigation,\n      useFactory: () => {\n        const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n        return new FakeNavigation(\n          inject(DOCUMENT).defaultView!,\n          (config?.startUrl as `http${string}`) ?? 'http://_empty_/',\n        );\n      },\n    },\n    {provide: PlatformLocation, useClass: FakeNavigationPlatformLocation},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Location,\n  LocationStrategy,\n  PopStateEvent,\n  ɵnormalizeQueryParams as normalizeQueryParams,\n} from '@angular/common';\nimport {Injectable} from '@angular/core';\nimport {Subject, SubscriptionLike} from 'rxjs';\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '', null)];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject = new Subject<PopStateEvent>();\n  /** @internal */\n  _basePath: string = '';\n  /** @internal */\n  _locationStrategy: LocationStrategy = null!;\n  /** @internal */\n  _urlChangeListeners: ((url: string, state: unknown) => void)[] = [];\n  /** @internal */\n  _urlChangeSubscription: SubscriptionLike | null = null;\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n\n  setInitialPath(url: string) {\n    this._history[this._historyIndex].path = url;\n  }\n\n  setBaseHref(url: string) {\n    this._basePath = url;\n  }\n\n  path(): string {\n    return this._history[this._historyIndex].path;\n  }\n\n  getState(): unknown {\n    return this._history[this._historyIndex].state;\n  }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/')\n      ? this.path().substring(0, this.path().length - 1)\n      : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n\n  simulateUrlPop(pathname: string) {\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'popstate'});\n  }\n\n  simulateHashChange(pathname: string) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'popstate'});\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n\n  go(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    this.pushHistory(path, query, state);\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  replaceState(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n\n    history.state = state;\n\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  onUrlChange(fn: (url: string, state: unknown) => void): VoidFunction {\n    this._urlChangeListeners.push(fn);\n\n    this._urlChangeSubscription ??= this.subscribe((v) => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n\n  /** @internal */\n  _notifyUrlChangeListeners(url: string = '', state: unknown) {\n    this._urlChangeListeners.forEach((fn) => fn(url, state));\n  }\n\n  subscribe(\n    onNext: (value: any) => void,\n    onThrow?: ((error: any) => void) | null,\n    onReturn?: (() => void) | null,\n  ): SubscriptionLike {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow ?? undefined,\n      complete: onReturn ?? undefined,\n    });\n  }\n\n  normalize(url: string): string {\n    return null!;\n  }\n\n  private pushHistory(path: string, query: string, state: any) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n}\n\nclass LocationState {\n  constructor(\n    public path: string,\n    public query: string,\n    public state: any,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {Injectable} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject = new Subject<_MockPopStateEvent>();\n  private stateChanges: any[] = [];\n  constructor() {\n    super();\n  }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.next(new _MockPopStateEvent(this.path()));\n  }\n\n  override path(includeHash: boolean = false): string {\n    return this.internalPath;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  override pushState(ctx: any, title: string, path: string, query: string): void {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  override replaceState(ctx: any, title: string, path: string, query: string): void {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  override onPopState(fn: (value: any) => void): void {\n    this._subject.subscribe({next: fn});\n  }\n\n  override getBaseHref(): string {\n    return this.internalBaseHref;\n  }\n\n  override back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  override forward(): void {\n    throw 'not implemented';\n  }\n\n  override getState(): unknown {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {Provider} from '@angular/core';\n\nimport {SpyLocation} from './location_mock';\nimport {MockLocationStrategy} from './mock_location_strategy';\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nexport function provideLocationMocks(): Provider[] {\n  return [\n    {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\nexport * from './private_export';\nexport {SpyLocation} from './location_mock';\nexport {MockLocationStrategy} from './mock_location_strategy';\nexport {\n  MOCK_PLATFORM_LOCATION_CONFIG,\n  MockPlatformLocation,\n  MockPlatformLocationConfig,\n} from './mock_platform_location';\nexport {provideLocationMocks} from './provide_location_mocks';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["PlatformNavigation", "normalizeQueryParams"], "mappings": ";;;;;;;;;;;AAuBA;;;AAGG;MAEmB,kBAAkB,CAAA;kHAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;sHAAlB,kBAAkB,EAAA,UAAA,EADf,UAAU,EAAc,UAAA,EAAA,MAAO,MAAc,CAAC,UAAU,EAAA,CAAA,CAAA;;sGAC3D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBADvC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAO,MAAc,CAAC,UAAU,EAAC,CAAA;;;ACHlF;;;;AAIG;MACU,cAAc,CAAA;AAsEN,IAAA,MAAA,CAAA;AArEnB;;;AAGG;IACc,UAAU,GAAiC,EAAE,CAAA;AAE9D;;AAEG;IACK,iBAAiB,GAAG,CAAC,CAAA;AAE7B;;AAEG;IACK,aAAa,GAA0C,SAAS,CAAA;AAExE;;;AAGG;AACc,IAAA,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAA;AAE7E;;;AAGG;AACK,IAAA,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,CAAA;AAEzC;;;AAGG;IACK,qBAAqB,GAAG,CAAC,CAAA;AAEjC;;;AAGG;IACK,qBAAqB,GAAG,KAAK,CAAA;;IAG7B,kBAAkB,GAAG,IAAI,CAAA;;AAGzB,IAAA,WAAW,CAAA;;IAGX,MAAM,GAAG,CAAC,CAAA;;IAGV,OAAO,GAAG,CAAC,CAAA;;IAGX,QAAQ,GAAG,KAAK,CAAA;;AAGxB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;KAChD;AAEA,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAA;KACnC;AAEA,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;KAC5D;IAEA,WACmB,CAAA,MAAc,EAC/B,QAAyB,EAAA;QADR,IAAM,CAAA,MAAA,GAAN,MAAM,CAAA;AAGvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;;AAE5D,QAAA,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;KAC1C;AAEA;;AAEG;IACK,yBAAyB,CAC/B,GAAoB,EACpB,OAAA,GAAoD,EAAC,YAAY,EAAE,IAAI,EAAC,EAAA;AAExE,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CACb,0DAA0D,GAAG,yBAAyB,CACvF,CAAA;SACH;QACA,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,0BAA0B,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;AAC3E,YAAA,KAAK,EAAE,CAAC;YACR,GAAG,EAAE,mBAAmB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,EAAE,EAAE,mBAAmB,EAAE,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACpD,YAAA,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,KAAK,EAAE,OAAO,CAAC,KAAK;AACrB,SAAA,CAAC,CAAA;KACJ;;IAGA,4BAA4B,GAAA;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAA;KAChC;AAEA;;;AAGG;AACH,IAAA,kCAAkC,CAAC,qBAA8B,EAAA;AAC/D,QAAA,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAA;KACpD;;IAGA,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;KAChC;;IAGA,QAAQ,CAAC,GAAW,EAAE,OAAmC,EAAA;QACvD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAA;AAC/C,QAAA,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAA;AAElD,QAAA,IAAI,cAAoC,CAAA;QACxC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;;YAEnD,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,EAAE;gBAC3C,cAAc,GAAG,SAAS,CAAA;aAC5B;iBAAO;gBACL,cAAc,GAAG,MAAM,CAAA;aACzB;SACF;aAAO;AACL,YAAA,cAAc,GAAG,OAAO,CAAC,OAAO,CAAA;SAClC;QAEA,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAE/C,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;YACrB,KAAK,EAAE,OAAO,EAAE,KAAK;AACrB,YAAA,YAAY,EAAE,UAAU;AACxB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAA;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAA;AAE7C,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC1C,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;YACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,SAAA,CAAC,CAAA;QAEF,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAA;KACH;;AAGA,IAAA,SAAS,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QAClD,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;KACnD;;AAGA,IAAA,YAAY,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;KACtD;AAEQ,IAAA,kBAAkB,CACxB,cAAoC,EACpC,IAAa,EACb,MAAc,EACd,GAAY,EAAA;QAEZ,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,GAAG,OAAO,CAAA;QAElE,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAE/C,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;AACrB,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAA;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAA;AAE7C,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC1C,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;AACV,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAA;KACJ;;IAGA,UAAU,CAAC,GAAW,EAAE,OAA2B,EAAA;QACjD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;QACjC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAA;YACzE,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAA;SACH;AACA,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;YAC/B,OAAO;gBACL,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;gBAC7C,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;aAC7C,CAAA;SACH;QACA,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACtC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAE,CAAA;YAC1D,OAAO;gBACL,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,QAAQ,EAAE,cAAc,CAAC,QAAQ;aAClC,CAAA;SACH;QAEA,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,CAAA;AACrF,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;YAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,YAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;YACrC,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,SAAA,CAAC,CAAA;AACF,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,KAAK,CAAA;AACxC,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAA;QAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACrC,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC1C,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;gBACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,aAAA,CAAC,CAAA;AACJ,SAAC,CAAC,CAAA;QACF,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAA;KACH;;AAGA,IAAA,IAAI,CAAC,OAA2B,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAChC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAA;YAC5E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAA;SACH;AACA,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;KAC5C;;AAGA,IAAA,OAAO,CAAC,OAA2B,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAA;YAC/E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAA;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAA;SACH;AACA,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;KAC5C;AAEA;;;;;;AAMG;AACH,IAAA,EAAE,CAAC,SAAiB,EAAA;AAClB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAA;AAC1D,QAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;YAC5D,OAAO;SACT;AACA,QAAA,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAA;AACxC,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;;AAErB,YAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;gBAC5D,OAAO;aACT;YACA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAA;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;YAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,CAAA;AACrF,YAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;gBAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,gBAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,gBAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;gBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,aAAA,CAAC,CAAA;AACF,YAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAA;AAC7C,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC1C,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;AACX,aAAA,CAAC,CAAA;AACJ,SAAC,CAAC,CAAA;KACJ;;AAGQ,IAAA,YAAY,CAAC,SAAqB,EAAA;AACxC,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,SAAS,EAAE,CAAA;YACX,OAAO;SACT;;;;QAKA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAK;AAChD,YAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;gBACnC,UAAU,CAAC,MAAK;AACd,oBAAA,OAAO,EAAE,CAAA;AACT,oBAAA,SAAS,EAAE,CAAA;AACb,iBAAC,CAAC,CAAA;AACJ,aAAC,CAAC,CAAA;AACJ,SAAC,CAAC,CAAA;KACJ;;AAGA,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;QAE3C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;KAC5D;;AAGA,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;QAExC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;KAC/D;;AAGA,IAAA,aAAa,CAAC,KAAY,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;KAC9C;;IAGA,OAAO,GAAA;;;AAGL,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;AAC5D,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;KACtB;;IAGA,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAA;KACtB;;AAGQ,IAAA,iBAAiB,CACvB,WAAsC,EACtC,MAAgC,EAChC,OAAgC,EAAA;;;AAIhC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC,CAAA;AACnF,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAA;SAChC;QAEA,MAAM,aAAa,GAAG,uBAAuB,CAAC;YAC5C,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,WAAW;YACX,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,MAAM;YACN,eAAe,EAAE,MAAK;gBACpB,IAAI,CAAC,eAAe,EAAE,CAAA;aACvB;AACF,SAAA,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;AAClC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;QAC7C,aAAa,CAAC,uBAAuB,EAAE,CAAA;AACvC,QAAA,IAAI,aAAa,CAAC,YAAY,KAAK,WAAW,EAAE;AAC9C,YAAA,aAAa,CAAC,MAAM,iBAAiB,IAAI,CAAC,CAAA;SAC5C;KACF;;IAGQ,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,OAAO;SACT;AACA,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;AACpC,YAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;AACtE,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AAChC,YAAA,MAAM,KAAK,CAAA;SACb;AACA,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,MAAM;AAC5C,YAAA,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,EAC/C;YACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAC1D,gBAAA,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;AAClD,aAAA,CAAC,CAAA;SACJ;aAAO,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,UAAU,EAAE;YAC3D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;SACxD;QACA,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACxD,MAAM,uBAAuB,GAAG,2CAA2C,CAAC;YAC1E,IAAI;AACJ,YAAA,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;AAClD,SAAA,CAAC,CAAA;AACF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAA;AACvD,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YACpC,MAAM,aAAa,GAAG,mBAAmB,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,eAAe,EAAE;AACxD,aAAA,CAAC,CAAA;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;SAC1C;KACF;;AAGQ,IAAA,sBAAsB,CAC5B,WAAsC,EACtC,EAAC,cAAc,EAAyC,EAAA;AAExD,QAAA,IAAI,cAAc,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAA;AACxB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAA;SACrD;AACA,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAA;QACpC,MAAM,GAAG,GAAG,cAAc,KAAK,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAA;QACtF,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,WAAW,CAAC,GAAG,EAAE;AAC5D,YAAA,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,GAAG;YACH,KAAK;AACL,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;AAC7B,YAAA,YAAY,EAAE,WAAW,CAAC,eAAe,EAAE;AAC5C,SAAA,CAAC,CAAA;AACF,QAAA,IAAI,cAAc,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;SAChD;aAAO;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;SAChC;KACF;;AAGQ,IAAA,iBAAiB,CAAC,WAAsC,EAAA;AAC9D,QAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAA;KAC5C;;AAGQ,IAAA,SAAS,CAAC,GAAW,EAAA;AAC3B,QAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;AAAE,gBAAA,OAAO,KAAK,CAAA;SACrC;AACA,QAAA,OAAO,SAAS,CAAA;KAClB;IAEA,IAAI,UAAU,CAAC,QAA+D,EAAA;AAC5E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;IAEA,IAAI,oBAAoB,CACtB,QAAmF,EAAA;AAEnF,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,IAAI,oBAAoB,GAAA;AAGtB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;IAEA,IAAI,iBAAiB,CAAC,QAAuD,EAAA;AAC3E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;IAEA,IAAI,eAAe,CAAC,QAA4D,EAAA;AAC9E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,kBAAkB,CAAC,QAA6C,EAAA;AAC9D,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,MAAM,CAAC,QAAkC,EAAA;AACvC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AACD,CAAA;AAWD;;AAEG;MACU,0BAA0B,CAAA;AAY1B,IAAA,GAAA,CAAA;AAXF,IAAA,YAAY,CAAA;AAEZ,IAAA,EAAE,CAAA;AACF,IAAA,GAAG,CAAA;AACH,IAAA,KAAK,CAAA;AACG,IAAA,KAAK,CAAA;AACL,IAAA,YAAY,CAAA;IAE7B,SAAS,GAA8D,IAAI,CAAA;AAE3E,IAAA,WAAA,CACW,GAAkB,EAC3B,EACE,EAAE,EACF,GAAG,EACH,KAAK,EACL,YAAY,EACZ,KAAK,EACL,YAAY,GAQb,EAAA;QAfQ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAA;AAiBZ,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;AACZ,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;AAChC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;KAClC;IAEA,QAAQ,GAAA;;QAEN,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAA;KACzE;IAEA,eAAe,GAAA;;QAEb,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAA;KAC9F;AAEA,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;AAE3C,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;AAExC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AAEA,IAAA,aAAa,CAAC,KAAY,EAAA;AACxB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;KAClC;AACD,CAAA;AAiCD;;;AAGG;AACH,SAAS,uBAAuB,CAAC,EAC/B,UAAU,EACV,YAAY,EACZ,aAAa,EACb,UAAU,EACV,cAAc,EACd,MAAM,EACN,WAAW,EACX,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,eAAe,GAchB,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAC,CAE/D,CAAA;AACD,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAA;AACjC,IAAA,KAAK,CAAC,aAAa,GAAG,aAAa,CAAA;AACnC,IAAA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAA;AAC7B,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc,CAAA;AACrC,IAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAA;AACrB,IAAA,KAAK,CAAC,WAAW,GAAG,WAAW,CAAA;AAC/B,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;AACjB,IAAA,KAAK,CAAC,eAAe,GAAG,IAAI,CAAA;AAC5B,IAAA,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAA;AAErB,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAA;AACjC,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAA;AACjC,IAAA,KAAK,CAAC,YAAY,GAAG,WAAW,CAAA;IAEhC,IAAI,eAAe,GAA8B,SAAS,CAAA;IAC1D,IAAI,eAAe,GAAG,KAAK,CAAA;IAC3B,IAAI,uBAAuB,GAAG,KAAK,CAAA;IACnC,IAAI,YAAY,GAAG,KAAK,CAAA;AAExB,IAAA,KAAK,CAAC,SAAS,GAAG,UAEhB,OAAgD,EAAA;QAEhD,eAAe,GAAG,IAAI,CAAA;AACtB,QAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAA;AACzB,QAAA,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,CAAA;QAChC,IAAI,OAAO,EAAE;YACX,eAAe,GAAG,OAAO,EAAE,CAAA;SAC7B;AACA,QAAA,IAAI,OAAO,EAAE,MAAM,EAAE;AACnB,YAAA,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAA;SACrC;AACA,QAAA,IAAI,OAAO,EAAE,UAAU,KAAK,SAAS,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,EAAE;AACtE,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;SAClC;AACF,KAAC,CAAA;IAED,KAAK,CAAC,MAAM,GAAG,YAAA;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;AAClC,KAAC,CAAA;AAED,IAAA,KAAK,CAAC,MAAM,GAAG,UAA2C,QAAQ,GAAG,KAAK,EAAA;AACxE,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,EAAE;YACjC,MAAM,IAAI,YAAY,CACpB,CAAqE,mEAAA,CAAA;gBACnE,CAAyB,uBAAA,CAAA,EAC3B,mBAAmB,CACpB,CAAA;SACH;QACA,IAAI,CAAC,uBAAuB,EAAE;YAC5B,MAAM,IAAI,YAAY,CACpB,CAAqE,mEAAA,CAAA;gBACnE,CAA+B,6BAAA,CAAA,EACjC,mBAAmB,CACpB,CAAA;SACH;QACA,IAAI,YAAY,EAAE;YAChB,MAAM,IAAI,YAAY,CACpB,CAAA,gEAAA,CAAkE,GAAG,CAAS,OAAA,CAAA,EAC9E,mBAAmB,CACpB,CAAA;SACH;QACA,YAAY,GAAG,IAAI,CAAA;AAEnB,QAAA,eAAe,EAAE,CAAA;AACnB,KAAC,CAAA;;AAGD,IAAA,KAAK,CAAC,MAAM,GAAG,UAA2C,MAAa,EAAA;AACrE,QAAA,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;AAC9B,QAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;AAC/B,KAAC,CAAA;;IAGD,KAAK,CAAC,uBAAuB,GAAG,YAAA;QAC9B,uBAAuB,GAAG,IAAI,CAAA;AAC9B,QAAA,IAAI,KAAK,CAAC,YAAY,KAAK,kBAAkB,EAAE;;AAE7C,YAAA,eAAe,EAAE,IAAI,CACnB,MAAK;gBACH,IAAI,CAAC,YAAY,EAAE;AACjB,oBAAA,KAAK,CAAC,MAAM,gBAAgB,IAAI,CAAC,CAAA;iBACnC;AACF,aAAC,EACD,MAAO,GAAC,CACT,CAAA;SACH;AACA,QAAA,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CACnD,CAAC,CAAC,KAAK,CAAC,KAAI;AACV,YAAA,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;AAC/B,SAAC,EACD,CAAC,MAAM,KAAI;AACT,YAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;AAC/B,SAAC,CACF,CAAA;AACH,KAAC,CAAA;;AAGD,IAAA,KAAK,CAAC,kBAAkB,GAAG,UAEzB,KAAiC,EAAA;AAEjC,QAAA,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;AAChC,KAAC,CAAA;AAED,IAAA,OAAO,KAAkC,CAAA;AAC3C,CAAA;AAOA;;;AAGG;AACH,SAAS,2CAA2C,CAAC,EACnD,IAAI,EACJ,cAAc,GAIf,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,EAAE;AAC5C,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAEA,CAAA;AACD,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;AACjB,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc,CAAA;AACrC,IAAA,OAAO,KAA8C,CAAA;AACvD,CAAA;AAEA;;;AAGG;AACH,SAAS,mBAAmB,CAAC,EAAC,KAAK,EAAmB,EAAA;AACpD,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE;AAClC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAA6D,CAAA;AAC9D,IAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAA;AACnB,IAAA,OAAO,KAAsB,CAAA;AAC/B,CAAA;AAEA;;AAEG;MACU,yBAAyB,CAAA;AAC3B,IAAA,GAAG,CAAA;AACH,IAAA,YAAY,CAAA;AACZ,IAAA,GAAG,CAAA;AACH,IAAA,EAAE,CAAA;AACF,IAAA,KAAK,CAAA;AAEG,IAAA,KAAK,CAAA;AACL,IAAA,YAAY,CAAA;IAE7B,WAAY,CAAA,EACV,GAAG,EACH,YAAY,EACZ,YAAY,EACZ,KAAK,EACL,GAAG,GAAG,IAAI,EACV,EAAE,GAAG,IAAI,EACT,KAAK,GAAG,CAAC,CAAC,GASX,EAAA;AACC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AACd,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;AAChC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;AAChC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AACd,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;AACZ,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;KACpB;IAEA,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;KACnB;IAEA,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAA;KAC1B;AACD,CAAA;AAED;AACA,SAAS,YAAY,CAAC,IAAS,EAAE,EAAO,EAAA;AACtC,IAAA,QACE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AACrB,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAC1B;AACH,CAAA;AAEA;AACA,MAAM,wBAAwB,CAAA;AAC5B,IAAA,gBAAgB,CAAA;AAChB,IAAA,eAAe,CAAA;AACf,IAAA,eAAe,CAAA;AACf,IAAA,cAAc,CAAA;AACL,IAAA,SAAS,CAAA;AACT,IAAA,QAAQ,CAAA;AACjB,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAA;KACpC;AACiB,IAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA;AAExD,IAAA,WAAA,GAAA;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,KAAI;AAC3E,YAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAA;AAC/B,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAA;AAC/B,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAA6B,OAAO,OAAO,EAAE,MAAM,KAAI;AAChF,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAA;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,MAAa,KAAI;gBACtC,MAAM,CAAC,MAAM,CAAC,CAAA;AACd,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;AACpC,aAAC,CAAA;AACH,SAAC,CAAC,CAAA;;QAEF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC,CAAA;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC,CAAA;KAC/B;AACD;;ACj7BD;;;;;;;;;;;;;;;;;;AAkBG;AACH,MAAM,QAAQ,GAAG,+DAA+D,CAAA;AAEhF,SAAS,QAAQ,CAAC,MAAc,EAAE,QAAgB,EAAA;IAChD,MAAM,cAAc,GAAG,wBAAwB,CAAA;AAC/C,IAAA,IAAI,UAA8B,CAAA;;;IAIlC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAChC,UAAU,GAAG,mBAAmB,CAAA;KAClC;AACA,IAAA,IAAI,SAOH,CAAA;AACD,IAAA,IAAI;QACF,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;KACzC;IAAE,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,GAAG,MAAM,CAAC,CAAA;QACvD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,MAAM,CAAe,YAAA,EAAA,QAAQ,CAAE,CAAA,CAAC,CAAA;SAClE;QACA,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACtC,QAAA,SAAS,GAAG;AACV,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AACtB,YAAA,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;AACxB,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AACjB,YAAA,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SAChB,CAAA;KACH;AACA,IAAA,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpE,QAAA,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KACpE;IACA,OAAO;QACL,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,IAAI,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,KAAK,EAAE;AAC3C,QAAA,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,GAAG;AACnC,QAAA,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;AAC9B,QAAA,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;KAC3B,CAAA;AACH,CAAA;AAYA;;;;AAIG;MACU,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B,EAChC;AAED;;;;AAIG;MAEU,oBAAoB,CAAA;IACvB,QAAQ,GAAW,EAAE,CAAA;AACrB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAuB,CAAA;AAC/C,IAAA,eAAe,GAAG,IAAI,OAAO,EAAuB,CAAA;IACpD,cAAc,GAAW,CAAC,CAAA;AAC1B,IAAA,UAAU,GAQZ,CAAC,EAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAA;AAEhG,IAAA,WAAA,CACqD,MAAmC,EAAA;QAEtF,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAA;AAExC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CACrC,IAAI,EACJ,MAAM,CAAC,QAAQ,IAAI,iBAAiB,EACpC,IAAI,CAAC,QAAQ,CACd,CAAA;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAC,GAAG,aAAa,EAAC,CAAA;SACzC;KACF;AAEA,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAA;KACtD;AACA,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAA;KACtD;AACA,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAA;KAClD;AACA,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAA;KACtD;AACA,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAA;KACpD;AACA,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAA;KAClD;AACA,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAA;KACnD;IAEA,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAA;KACtB;AAEA,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;AACvD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE,CAAA;KACzC;AAEA,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;AAClD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE,CAAA;KACzC;AAEA,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA,CAAE,CAAA;QACjF,GAAG,IAAI,CAAG,EAAA,IAAI,CAAC,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,MAAM,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,CAAE,CAAA;AAChF,QAAA,OAAO,GAAG,CAAA;KACZ;AAEA,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,CAAG,EAAA,IAAI,CAAC,MAAM,CAAG,EAAA,IAAI,CAAC,IAAI,EAAE,CAAA;KACrD;AAEQ,IAAA,YAAY,CAAC,KAAc,EAAE,GAAW,EAAE,WAAmB,EAAE,EAAA;;AAErE,QAAA,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;QACzC,OAAO,EAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,KAAK,EAAC,CAAA;KAC5C;AAEA,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACpD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAErF,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG;AACrC,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;SACnB,CAAA;KACH;AAEA,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACjD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACrF,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAA;SACjD;AACA,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;AACnB,SAAA,CAAC,CAAA;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;KAClD;IAEA,OAAO,GAAA;AACL,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;QACzB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAChD,IAAI,CAAC,cAAc,EAAE,CAAA;SACvB;AACA,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;KAClC;IAEA,IAAI,GAAA;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;AACzB,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAA;SACvB;AACA,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;KAClC;IAEA,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;AACzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAA;AAC5D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAChE,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAA;SACrC;AACA,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;KAClC;IAEA,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;KACnB;AAEA;;;;;;;;;AASG;IACK,UAAU,CAAC,OAAe,EAAE,MAAc,EAAA;AAChD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM;YACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,SAAA,CAAC,CAAA;AACzB,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,gBAAA,IAAI,EAAE,YAAY;AAClB,gBAAA,KAAK,EAAE,IAAI;gBACX,MAAM;gBACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,aAAA,CAAC,CAAA;SAC3B;KACF;AAtKW,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,kBAgBrB,6BAA6B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;sHAhB5B,oBAAoB,EAAA,CAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;0BAiBN,MAAM;2BAAC,6BAA6B,CAAA;;0BAAG,QAAQ;;AAyJpD;;AAEG;MAEU,8BAA8B,CAAA;AACjC,IAAA,mBAAmB,GAAG,MAAM,CAACA,mBAAkB,CAAmB,CAAA;AAClE,IAAA,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAY,CAAA;AAE9C,IAAA,WAAA,GAAA;QACE,IAAI,EAAE,IAAI,CAAC,mBAAmB,YAAY,cAAc,CAAC,EAAE;YACzD,MAAM,IAAI,KAAK,CACb,oEAAoE;AAClE,gBAAA,uEAAuE,CAC1E,CAAA;SACH;KACF;IAEQ,MAAM,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAA;IACxE,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,EAAE,CAAA;KACvC;AAEA,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;AAC5C,QAAA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;KAC9D;AAEA,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAS,CAAC,CAAA;AACrD,QAAA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAS,CAAC,CAAA;KACvE;AAEA,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAA;KACnD;AACA,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAA;KACrE;AACA,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAA;KACrE;AACA,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI,CAAA;KACjE;AACA,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAA;KACrE;AACA,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,MAAM,CAAA;KACnE;AACA,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI,CAAA;KACjE;AAEA,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QAC9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;KACvD;AAEA,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QACjD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;KAC1D;IAEA,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAA;KACpC;IAEA,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAA;KACjC;IAEA,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAA;KAC/C;IAEA,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,eAAe,EAAE,CAAA;KAChE;kHAxEW,8BAA8B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;sHAA9B,8BAA8B,EAAA,CAAA,CAAA;;sGAA9B,8BAA8B,EAAA,UAAA,EAAA,CAAA;kBAD1C,UAAU;;;ACzQX;;AAEG;SACa,6BAA6B,GAAA;IAC3C,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAA;AACtE,gBAAA,OAAO,IAAI,cAAc,CACvB,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAY,EAC5B,MAAM,EAAE,QAA4B,IAAI,iBAAiB,CAC3D,CAAA;aACF;AACF,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,8BAA8B,EAAC;KACtE,CAAA;AACH;;ACpBA;;;;AAIG;MAEU,WAAW,CAAA;IACtB,UAAU,GAAa,EAAE,CAAA;AACjB,IAAA,QAAQ,GAAoB,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;IAC7D,aAAa,GAAW,CAAC,CAAA;;AAEjC,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAiB,CAAA;;IAEvC,SAAS,GAAW,EAAE,CAAA;;IAEtB,iBAAiB,GAAqB,IAAK,CAAA;;IAE3C,mBAAmB,GAA8C,EAAE,CAAA;;IAEnE,sBAAsB,GAA4B,IAAI,CAAA;;IAGtD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAA;AAC1C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAA;KAC/B;AAEA,IAAA,cAAc,CAAC,GAAW,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;KAC9C;AAEA,IAAA,WAAW,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;KACtB;IAEA,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAA;KAC/C;IAEA,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAA;KAChD;AAEA,IAAA,oBAAoB,CAAC,IAAY,EAAE,KAAA,GAAgB,EAAE,EAAA;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAA;AACvC,cAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAA;AACjD,cAAE,IAAI,CAAC,IAAI,EAAE,CAAA;QAEf,OAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAA;KACtE;AAEA,IAAA,cAAc,CAAC,QAAgB,EAAA;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC,CAAA;KACxE;AAEA,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QAEhC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAA;;;AAGzC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC,CAAA;AACtE,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAC,CAAC,CAAA;KAC1E;AAEA,IAAA,kBAAkB,CAAC,GAAW,EAAA;AAC5B,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;SACjB;AACA,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;KAC7B;AAEA,IAAA,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AACpD,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAEpC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;AAEpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAA;AAC3D,QAAA,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,KAAK,EAAE;YAC9D,OAAO;SACT;QAEA,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAA;AACxD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACzB,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGC,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAA;KAC3E;AAEA,IAAA,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AAC9D,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AAEjD,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;AAErB,QAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE;YAClD,OAAO;SACT;AAEA,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;AACnB,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QAErB,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAA;QACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAA;AACvC,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGA,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAA;KAC3E;IAEA,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,IAAI,CAAC,aAAa,EAAE,CAAA;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAA;SACJ;KACF;IAEA,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAA;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAA;SACJ;KACF;IAEA,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAA;AAC3D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC9D,YAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAA;SACJ;KACF;AAEA,IAAA,WAAW,CAAC,EAAyC,EAAA;AACnD,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAEjC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACnD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;AAChD,SAAC,CAAC,CAAA;AAEF,QAAA,OAAO,MAAK;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YACpD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YAE3C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAA;AAC1C,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAA;aACpC;AACF,SAAC,CAAA;KACH;;AAGA,IAAA,yBAAyB,CAAC,GAAA,GAAc,EAAE,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAA;KAC1D;AAEA,IAAA,SAAS,CACP,MAA4B,EAC5B,OAAuC,EACvC,QAA8B,EAAA;AAE9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC7B,YAAA,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO,IAAI,SAAS;YAC3B,QAAQ,EAAE,QAAQ,IAAI,SAAS;AAChC,SAAA,CAAC,CAAA;KACJ;AAEA,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,IAAK,CAAA;KACd;AAEQ,IAAA,WAAW,CAAC,IAAY,EAAE,KAAa,EAAE,KAAU,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAA;SAC9C;AACA,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;KAC/C;kHAxLW,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;sHAAX,WAAW,EAAA,CAAA,CAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBADvB,UAAU;;AA4LX,MAAM,aAAa,CAAA;AAER,IAAA,IAAA,CAAA;AACA,IAAA,KAAA,CAAA;AACA,IAAA,KAAA,CAAA;AAHT,IAAA,WAAA,CACS,IAAY,EACZ,KAAa,EACb,KAAU,EAAA;QAFV,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAA;QACJ,IAAK,CAAA,KAAA,GAAL,KAAK,CAAA;QACL,IAAK,CAAA,KAAA,GAAL,KAAK,CAAA;KACX;AACJ;;AC5MD;;;;;AAKG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;IACxD,gBAAgB,GAAW,GAAG,CAAA;IAC9B,YAAY,GAAW,GAAG,CAAA;IAC1B,aAAa,GAAW,EAAE,CAAA;IAC1B,UAAU,GAAa,EAAE,CAAA;;AAEzB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAsB,CAAA;IACpC,YAAY,GAAU,EAAE,CAAA;AAChC,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAA;KACT;AAEA,IAAA,gBAAgB,CAAC,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAA;AACvB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;KACzD;IAES,IAAI,CAAC,cAAuB,KAAK,EAAA;QACxC,OAAO,IAAI,CAAC,YAAY,CAAA;KAC1B;AAES,IAAA,kBAAkB,CAAC,QAAgB,EAAA;AAC1C,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SACtD;AACA,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAA;KACzC;AAES,IAAA,SAAS,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAErE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAE3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAE1B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAA;AACxD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAA;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;KACnC;AAES,IAAA,YAAY,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAExE,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAA;AAE5D,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAE1B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAA;AACxD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAA;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAA;KACjD;AAES,IAAA,UAAU,CAAC,EAAwB,EAAA;QAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC,CAAA;KACrC;IAES,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAA;KAC9B;IAES,IAAI,GAAA;QACX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAA;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAA;AACvB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAA;AAC7F,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;SAChC;KACF;IAES,OAAO,GAAA;AACd,QAAA,MAAM,iBAAiB,CAAA;KACzB;IAES,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;KAC/D;kHA7EW,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;sHAApB,oBAAoB,EAAA,CAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;AAiFX,MAAM,kBAAkB,CAAA;AAGH,IAAA,MAAA,CAAA;IAFnB,GAAG,GAAY,IAAI,CAAA;IACnB,IAAI,GAAW,UAAU,CAAA;AACzB,IAAA,WAAA,CAAmB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM,CAAA;KAAW;AACrC;;ACzFD;;;;;AAKG;SACa,oBAAoB,GAAA;IAClC,OAAO;AACL,QAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAC;AAC1C,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;KAC5D,CAAA;AACH;;ACjBA;;;;AAIG;;ACJH;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}