{"version": 3, "file": "upgrade.mjs", "sources": ["../../../../../../packages/common/upgrade/src/utils.ts", "../../../../../../packages/common/upgrade/src/location_shim.ts", "../../../../../../packages/common/upgrade/src/params.ts", "../../../../../../packages/common/upgrade/src/location_upgrade_module.ts", "../../../../../../packages/common/upgrade/public_api.ts", "../../../../../../packages/common/upgrade/index.ts", "../../../../../../packages/common/upgrade/upgrade.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function stripPrefix(val: string, prefix: string): string {\n  return val.startsWith(prefix) ? val.substring(prefix.length) : val;\n}\n\nexport function deepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true;\n  } else if (!a || !b) {\n    return false;\n  } else {\n    try {\n      if (a.prototype !== b.prototype || (Array.isArray(a) && Array.isArray(b))) {\n        return false;\n      }\n      return JSON.stringify(a) === JSON.stringify(b);\n    } catch (e) {\n      return false;\n    }\n  }\n}\n\nexport function isAnchor(el: (Node & ParentNode) | Element | null): el is HTMLAnchorElement {\n  return (<HTMLAnchorElement>el).href !== undefined;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Location, LocationStrategy, PlatformLocation} from '@angular/common';\nimport {ɵisPromise as isPromise} from '@angular/core';\nimport {UpgradeModule} from '@angular/upgrade/static';\nimport {ReplaySubject} from 'rxjs';\n\nimport {UrlCodec} from './params';\nimport {deepEqual, isAnchor} from './utils';\n\nconst PATH_MATCH = /^([^?#]*)(\\?([^#]*))?(#(.*))?$/;\nconst DOUBLE_SLASH_REGEX = /^\\s*[\\\\/]{2,}/;\nconst IGNORE_URI_REGEXP = /^\\s*(javascript|mailto):/i;\nconst DEFAULT_PORTS: {[key: string]: number} = {\n  'http:': 80,\n  'https:': 443,\n  'ftp:': 21,\n};\n\n/**\n * Location service that provides a drop-in replacement for the $location service\n * provided in AngularJS.\n *\n * @see [Using the Angular Unified Location Service](guide/upgrade#using-the-unified-angular-location-service)\n *\n * @publicApi\n */\nexport class $locationShim {\n  private initializing = true;\n  private updateBrowser = false;\n  private $$absUrl: string = '';\n  private $$url: string = '';\n  private $$protocol: string;\n  private $$host: string = '';\n  private $$port: number | null;\n  private $$replace: boolean = false;\n  private $$path: string = '';\n  private $$search: any = '';\n  private $$hash: string = '';\n  private $$state: unknown;\n  private $$changeListeners: [\n    (\n      url: string,\n      state: unknown,\n      oldUrl: string,\n      oldState: unknown,\n      err?: (e: Error) => void,\n    ) => void,\n    (e: Error) => void,\n  ][] = [];\n\n  private cachedState: unknown = null;\n\n  private urlChanges = new ReplaySubject<{newUrl: string; newState: unknown}>(1);\n\n  constructor(\n    $injector: any,\n    private location: Location,\n    private platformLocation: PlatformLocation,\n    private urlCodec: UrlCodec,\n    private locationStrategy: LocationStrategy,\n  ) {\n    const initialUrl = this.browserUrl();\n\n    let parsedUrl = this.urlCodec.parse(initialUrl);\n\n    if (typeof parsedUrl === 'string') {\n      throw 'Invalid URL';\n    }\n\n    this.$$protocol = parsedUrl.protocol;\n    this.$$host = parsedUrl.hostname;\n    this.$$port = parseInt(parsedUrl.port) || DEFAULT_PORTS[parsedUrl.protocol] || null;\n\n    this.$$parseLinkUrl(initialUrl, initialUrl);\n    this.cacheState();\n    this.$$state = this.browserState();\n\n    this.location.onUrlChange((newUrl, newState) => {\n      this.urlChanges.next({newUrl, newState});\n    });\n\n    if (isPromise($injector)) {\n      $injector.then(($i) => this.initialize($i));\n    } else {\n      this.initialize($injector);\n    }\n  }\n\n  private initialize($injector: any) {\n    const $rootScope = $injector.get('$rootScope');\n    const $rootElement = $injector.get('$rootElement');\n\n    $rootElement.on('click', (event: any) => {\n      if (\n        event.ctrlKey ||\n        event.metaKey ||\n        event.shiftKey ||\n        event.which === 2 ||\n        event.button === 2\n      ) {\n        return;\n      }\n\n      let elm: (Node & ParentNode) | null = event.target;\n\n      // traverse the DOM up to find first A tag\n      while (elm && elm.nodeName.toLowerCase() !== 'a') {\n        // ignore rewriting if no A tag (reached root element, or no parent - removed from document)\n        if (elm === $rootElement[0] || !(elm = elm.parentNode)) {\n          return;\n        }\n      }\n\n      if (!isAnchor(elm)) {\n        return;\n      }\n\n      const absHref = elm.href;\n      const relHref = elm.getAttribute('href');\n\n      // Ignore when url is started with javascript: or mailto:\n      if (IGNORE_URI_REGEXP.test(absHref)) {\n        return;\n      }\n\n      if (absHref && !elm.getAttribute('target') && !event.isDefaultPrevented()) {\n        if (this.$$parseLinkUrl(absHref, relHref)) {\n          // We do a preventDefault for all urls that are part of the AngularJS application,\n          // in html5mode and also without, so that we are able to abort navigation without\n          // getting double entries in the location history.\n          event.preventDefault();\n          // update location manually\n          if (this.absUrl() !== this.browserUrl()) {\n            $rootScope.$apply();\n          }\n        }\n      }\n    });\n\n    this.urlChanges.subscribe(({newUrl, newState}) => {\n      const oldUrl = this.absUrl();\n      const oldState = this.$$state;\n      this.$$parse(newUrl);\n      newUrl = this.absUrl();\n      this.$$state = newState;\n      const defaultPrevented = $rootScope.$broadcast(\n        '$locationChangeStart',\n        newUrl,\n        oldUrl,\n        newState,\n        oldState,\n      ).defaultPrevented;\n\n      // if the location was changed by a `$locationChangeStart` handler then stop\n      // processing this location change\n      if (this.absUrl() !== newUrl) return;\n\n      // If default was prevented, set back to old state. This is the state that was locally\n      // cached in the $location service.\n      if (defaultPrevented) {\n        this.$$parse(oldUrl);\n        this.state(oldState);\n        this.setBrowserUrlWithFallback(oldUrl, false, oldState);\n        this.$$notifyChangeListeners(this.url(), this.$$state, oldUrl, oldState);\n      } else {\n        this.initializing = false;\n        $rootScope.$broadcast('$locationChangeSuccess', newUrl, oldUrl, newState, oldState);\n        this.resetBrowserUpdate();\n      }\n      if (!$rootScope.$$phase) {\n        $rootScope.$digest();\n      }\n    });\n\n    // update browser\n    $rootScope.$watch(() => {\n      if (this.initializing || this.updateBrowser) {\n        this.updateBrowser = false;\n\n        const oldUrl = this.browserUrl();\n        const newUrl = this.absUrl();\n        const oldState = this.browserState();\n        let currentReplace = this.$$replace;\n\n        const urlOrStateChanged =\n          !this.urlCodec.areEqual(oldUrl, newUrl) || oldState !== this.$$state;\n\n        // Fire location changes one time to on initialization. This must be done on the\n        // next tick (thus inside $evalAsync()) in order for listeners to be registered\n        // before the event fires. Mimicing behavior from $locationWatch:\n        // https://github.com/angular/angular.js/blob/master/src/ng/location.js#L983\n        if (this.initializing || urlOrStateChanged) {\n          this.initializing = false;\n\n          $rootScope.$evalAsync(() => {\n            // Get the new URL again since it could have changed due to async update\n            const newUrl = this.absUrl();\n            const defaultPrevented = $rootScope.$broadcast(\n              '$locationChangeStart',\n              newUrl,\n              oldUrl,\n              this.$$state,\n              oldState,\n            ).defaultPrevented;\n\n            // if the location was changed by a `$locationChangeStart` handler then stop\n            // processing this location change\n            if (this.absUrl() !== newUrl) return;\n\n            if (defaultPrevented) {\n              this.$$parse(oldUrl);\n              this.$$state = oldState;\n            } else {\n              // This block doesn't run when initializing because it's going to perform the update\n              // to the URL which shouldn't be needed when initializing.\n              if (urlOrStateChanged) {\n                this.setBrowserUrlWithFallback(\n                  newUrl,\n                  currentReplace,\n                  oldState === this.$$state ? null : this.$$state,\n                );\n                this.$$replace = false;\n              }\n              $rootScope.$broadcast(\n                '$locationChangeSuccess',\n                newUrl,\n                oldUrl,\n                this.$$state,\n                oldState,\n              );\n              if (urlOrStateChanged) {\n                this.$$notifyChangeListeners(this.url(), this.$$state, oldUrl, oldState);\n              }\n            }\n          });\n        }\n      }\n      this.$$replace = false;\n    });\n  }\n\n  private resetBrowserUpdate() {\n    this.$$replace = false;\n    this.$$state = this.browserState();\n    this.updateBrowser = false;\n    this.lastBrowserUrl = this.browserUrl();\n  }\n\n  private lastHistoryState: unknown;\n  private lastBrowserUrl: string = '';\n  private browserUrl(): string;\n  private browserUrl(url: string, replace?: boolean, state?: unknown): this;\n  private browserUrl(url?: string, replace?: boolean, state?: unknown) {\n    // In modern browsers `history.state` is `null` by default; treating it separately\n    // from `undefined` would cause `$browser.url('/foo')` to change `history.state`\n    // to undefined via `pushState`. Instead, let's change `undefined` to `null` here.\n    if (typeof state === 'undefined') {\n      state = null;\n    }\n\n    // setter\n    if (url) {\n      let sameState = this.lastHistoryState === state;\n\n      // Normalize the inputted URL\n      url = this.urlCodec.parse(url).href;\n\n      // Don't change anything if previous and current URLs and states match.\n      if (this.lastBrowserUrl === url && sameState) {\n        return this;\n      }\n      this.lastBrowserUrl = url;\n      this.lastHistoryState = state;\n\n      // Remove server base from URL as the Angular APIs for updating URL require\n      // it to be the path+.\n      url = this.stripBaseUrl(this.getServerBase(), url) || url;\n\n      // Set the URL\n      if (replace) {\n        this.locationStrategy.replaceState(state, '', url, '');\n      } else {\n        this.locationStrategy.pushState(state, '', url, '');\n      }\n\n      this.cacheState();\n\n      return this;\n      // getter\n    } else {\n      return this.platformLocation.href;\n    }\n  }\n\n  // This variable should be used *only* inside the cacheState function.\n  private lastCachedState: unknown = null;\n  private cacheState() {\n    // This should be the only place in $browser where `history.state` is read.\n    this.cachedState = this.platformLocation.getState();\n    if (typeof this.cachedState === 'undefined') {\n      this.cachedState = null;\n    }\n\n    // Prevent callbacks fo fire twice if both hashchange & popstate were fired.\n    if (deepEqual(this.cachedState, this.lastCachedState)) {\n      this.cachedState = this.lastCachedState;\n    }\n\n    this.lastCachedState = this.cachedState;\n    this.lastHistoryState = this.cachedState;\n  }\n\n  /**\n   * This function emulates the $browser.state() function from AngularJS. It will cause\n   * history.state to be cached unless changed with deep equality check.\n   */\n  private browserState(): unknown {\n    return this.cachedState;\n  }\n\n  private stripBaseUrl(base: string, url: string) {\n    if (url.startsWith(base)) {\n      return url.slice(base.length);\n    }\n    return undefined;\n  }\n\n  private getServerBase() {\n    const {protocol, hostname, port} = this.platformLocation;\n    const baseHref = this.locationStrategy.getBaseHref();\n    let url = `${protocol}//${hostname}${port ? ':' + port : ''}${baseHref || '/'}`;\n    return url.endsWith('/') ? url : url + '/';\n  }\n\n  private parseAppUrl(url: string) {\n    if (DOUBLE_SLASH_REGEX.test(url)) {\n      throw new Error(`Bad Path - URL cannot start with double slashes: ${url}`);\n    }\n\n    let prefixed = url.charAt(0) !== '/';\n    if (prefixed) {\n      url = '/' + url;\n    }\n    let match = this.urlCodec.parse(url, this.getServerBase());\n    if (typeof match === 'string') {\n      throw new Error(`Bad URL - Cannot parse URL: ${url}`);\n    }\n    let path =\n      prefixed && match.pathname.charAt(0) === '/' ? match.pathname.substring(1) : match.pathname;\n    this.$$path = this.urlCodec.decodePath(path);\n    this.$$search = this.urlCodec.decodeSearch(match.search);\n    this.$$hash = this.urlCodec.decodeHash(match.hash);\n\n    // make sure path starts with '/';\n    if (this.$$path && this.$$path.charAt(0) !== '/') {\n      this.$$path = '/' + this.$$path;\n    }\n  }\n\n  /**\n   * Registers listeners for URL changes. This API is used to catch updates performed by the\n   * AngularJS framework. These changes are a subset of the `$locationChangeStart` and\n   * `$locationChangeSuccess` events which fire when AngularJS updates its internally-referenced\n   * version of the browser URL.\n   *\n   * It's possible for `$locationChange` events to happen, but for the browser URL\n   * (window.location) to remain unchanged. This `onChange` callback will fire only when AngularJS\n   * actually updates the browser URL (window.location).\n   *\n   * @param fn The callback function that is triggered for the listener when the URL changes.\n   * @param err The callback function that is triggered when an error occurs.\n   */\n  onChange(\n    fn: (url: string, state: unknown, oldUrl: string, oldState: unknown) => void,\n    err: (e: Error) => void = (e: Error) => {},\n  ) {\n    this.$$changeListeners.push([fn, err]);\n  }\n\n  /** @internal */\n  $$notifyChangeListeners(\n    url: string = '',\n    state: unknown,\n    oldUrl: string = '',\n    oldState: unknown,\n  ) {\n    this.$$changeListeners.forEach(([fn, err]) => {\n      try {\n        fn(url, state, oldUrl, oldState);\n      } catch (e) {\n        err(e as Error);\n      }\n    });\n  }\n\n  /**\n   * Parses the provided URL, and sets the current URL to the parsed result.\n   *\n   * @param url The URL string.\n   */\n  $$parse(url: string) {\n    let pathUrl: string | undefined;\n    if (url.startsWith('/')) {\n      pathUrl = url;\n    } else {\n      // Remove protocol & hostname if URL starts with it\n      pathUrl = this.stripBaseUrl(this.getServerBase(), url);\n    }\n    if (typeof pathUrl === 'undefined') {\n      throw new Error(`Invalid url \"${url}\", missing path prefix \"${this.getServerBase()}\".`);\n    }\n\n    this.parseAppUrl(pathUrl);\n\n    this.$$path ||= '/';\n    this.composeUrls();\n  }\n\n  /**\n   * Parses the provided URL and its relative URL.\n   *\n   * @param url The full URL string.\n   * @param relHref A URL string relative to the full URL string.\n   */\n  $$parseLinkUrl(url: string, relHref?: string | null): boolean {\n    // When relHref is passed, it should be a hash and is handled separately\n    if (relHref && relHref[0] === '#') {\n      this.hash(relHref.slice(1));\n      return true;\n    }\n    let rewrittenUrl;\n    let appUrl = this.stripBaseUrl(this.getServerBase(), url);\n    if (typeof appUrl !== 'undefined') {\n      rewrittenUrl = this.getServerBase() + appUrl;\n    } else if (this.getServerBase() === url + '/') {\n      rewrittenUrl = this.getServerBase();\n    }\n    // Set the URL\n    if (rewrittenUrl) {\n      this.$$parse(rewrittenUrl);\n    }\n    return !!rewrittenUrl;\n  }\n\n  private setBrowserUrlWithFallback(url: string, replace: boolean, state: unknown) {\n    const oldUrl = this.url();\n    const oldState = this.$$state;\n    try {\n      this.browserUrl(url, replace, state);\n\n      // Make sure $location.state() returns referentially identical (not just deeply equal)\n      // state object; this makes possible quick checking if the state changed in the digest\n      // loop. Checking deep equality would be too expensive.\n      this.$$state = this.browserState();\n    } catch (e) {\n      // Restore old values if pushState fails\n      this.url(oldUrl);\n      this.$$state = oldState;\n\n      throw e;\n    }\n  }\n\n  private composeUrls() {\n    this.$$url = this.urlCodec.normalize(this.$$path, this.$$search, this.$$hash);\n    this.$$absUrl = this.getServerBase() + this.$$url.slice(1); // remove '/' from front of URL\n    this.updateBrowser = true;\n  }\n\n  /**\n   * Retrieves the full URL representation with all segments encoded according to\n   * rules specified in\n   * [RFC 3986](https://tools.ietf.org/html/rfc3986).\n   *\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let absUrl = $location.absUrl();\n   * // => \"http://example.com/#/some/path?foo=bar&baz=xoxo\"\n   * ```\n   */\n  absUrl(): string {\n    return this.$$absUrl;\n  }\n\n  /**\n   * Retrieves the current URL, or sets a new URL. When setting a URL,\n   * changes the path, search, and hash, and returns a reference to its own instance.\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let url = $location.url();\n   * // => \"/some/path?foo=bar&baz=xoxo\"\n   * ```\n   */\n  url(): string;\n  url(url: string): this;\n  url(url?: string): string | this {\n    if (typeof url === 'string') {\n      if (!url.length) {\n        url = '/';\n      }\n\n      const match = PATH_MATCH.exec(url);\n      if (!match) return this;\n      if (match[1] || url === '') this.path(this.urlCodec.decodePath(match[1]));\n      if (match[2] || match[1] || url === '') this.search(match[3] || '');\n      this.hash(match[5] || '');\n\n      // Chainable method\n      return this;\n    }\n\n    return this.$$url;\n  }\n\n  /**\n   * Retrieves the protocol of the current URL.\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let protocol = $location.protocol();\n   * // => \"http\"\n   * ```\n   */\n  protocol(): string {\n    return this.$$protocol;\n  }\n\n  /**\n   * Retrieves the protocol of the current URL.\n   *\n   * In contrast to the non-AngularJS version `location.host` which returns `hostname:port`, this\n   * returns the `hostname` portion only.\n   *\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let host = $location.host();\n   * // => \"example.com\"\n   *\n   * // given URL http://user:<EMAIL>:8080/#/some/path?foo=bar&baz=xoxo\n   * host = $location.host();\n   * // => \"example.com\"\n   * host = location.host;\n   * // => \"example.com:8080\"\n   * ```\n   */\n  host(): string {\n    return this.$$host;\n  }\n\n  /**\n   * Retrieves the port of the current URL.\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let port = $location.port();\n   * // => 80\n   * ```\n   */\n  port(): number | null {\n    return this.$$port;\n  }\n\n  /**\n   * Retrieves the path of the current URL, or changes the path and returns a reference to its own\n   * instance.\n   *\n   * Paths should always begin with forward slash (/). This method adds the forward slash\n   * if it is missing.\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let path = $location.path();\n   * // => \"/some/path\"\n   * ```\n   */\n  path(): string;\n  path(path: string | number | null): this;\n  path(path?: string | number | null): string | this {\n    if (typeof path === 'undefined') {\n      return this.$$path;\n    }\n\n    // null path converts to empty string. Prepend with \"/\" if needed.\n    path = path !== null ? path.toString() : '';\n    path = path.charAt(0) === '/' ? path : '/' + path;\n\n    this.$$path = path;\n\n    this.composeUrls();\n    return this;\n  }\n\n  /**\n   * Retrieves a map of the search parameters of the current URL, or changes a search\n   * part and returns a reference to its own instance.\n   *\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo\n   * let searchObject = $location.search();\n   * // => {foo: 'bar', baz: 'xoxo'}\n   *\n   * // set foo to 'yipee'\n   * $location.search('foo', 'yipee');\n   * // $location.search() => {foo: 'yipee', baz: 'xoxo'}\n   * ```\n   *\n   * @param {string|Object.<string>|Object.<Array.<string>>} search New search params - string or\n   * hash object.\n   *\n   * When called with a single argument the method acts as a setter, setting the `search` component\n   * of `$location` to the specified value.\n   *\n   * If the argument is a hash object containing an array of values, these values will be encoded\n   * as duplicate search parameters in the URL.\n   *\n   * @param {(string|Number|Array<string>|boolean)=} paramValue If `search` is a string or number,\n   *     then `paramValue`\n   * will override only a single search property.\n   *\n   * If `paramValue` is an array, it will override the property of the `search` component of\n   * `$location` specified via the first argument.\n   *\n   * If `paramValue` is `null`, the property specified via the first argument will be deleted.\n   *\n   * If `paramValue` is `true`, the property specified via the first argument will be added with no\n   * value nor trailing equal sign.\n   *\n   * @return {Object} The parsed `search` object of the current URL, or the changed `search` object.\n   */\n  search(): {[key: string]: unknown};\n  search(search: string | number | {[key: string]: unknown}): this;\n  search(\n    search: string | number | {[key: string]: unknown},\n    paramValue: null | undefined | string | number | boolean | string[],\n  ): this;\n  search(\n    search?: string | number | {[key: string]: unknown},\n    paramValue?: null | undefined | string | number | boolean | string[],\n  ): {[key: string]: unknown} | this {\n    switch (arguments.length) {\n      case 0:\n        return this.$$search;\n      case 1:\n        if (typeof search === 'string' || typeof search === 'number') {\n          this.$$search = this.urlCodec.decodeSearch(search.toString());\n        } else if (typeof search === 'object' && search !== null) {\n          // Copy the object so it's never mutated\n          search = {...search};\n          // remove object undefined or null properties\n          for (const key in search) {\n            if (search[key] == null) delete search[key];\n          }\n\n          this.$$search = search;\n        } else {\n          throw new Error(\n            'LocationProvider.search(): First argument must be a string or an object.',\n          );\n        }\n        break;\n      default:\n        if (typeof search === 'string') {\n          const currentSearch = this.search();\n          if (typeof paramValue === 'undefined' || paramValue === null) {\n            delete currentSearch[search];\n            return this.search(currentSearch);\n          } else {\n            currentSearch[search] = paramValue;\n            return this.search(currentSearch);\n          }\n        }\n    }\n    this.composeUrls();\n    return this;\n  }\n\n  /**\n   * Retrieves the current hash fragment, or changes the hash fragment and returns a reference to\n   * its own instance.\n   *\n   * ```js\n   * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo#hashValue\n   * let hash = $location.hash();\n   * // => \"hashValue\"\n   * ```\n   */\n  hash(): string;\n  hash(hash: string | number | null): this;\n  hash(hash?: string | number | null): string | this {\n    if (typeof hash === 'undefined') {\n      return this.$$hash;\n    }\n\n    this.$$hash = hash !== null ? hash.toString() : '';\n\n    this.composeUrls();\n    return this;\n  }\n\n  /**\n   * Changes to `$location` during the current `$digest` will replace the current\n   * history record, instead of adding a new one.\n   */\n  replace(): this {\n    this.$$replace = true;\n    return this;\n  }\n\n  /**\n   * Retrieves the history state object when called without any parameter.\n   *\n   * Change the history state object when called with one parameter and return `$location`.\n   * The state object is later passed to `pushState` or `replaceState`.\n   *\n   * This method is supported only in HTML5 mode and only in browsers supporting\n   * the HTML5 History API methods such as `pushState` and `replaceState`. If you need to support\n   * older browsers (like Android < 4.0), don't use this method.\n   *\n   */\n  state(): unknown;\n  state(state: unknown): this;\n  state(state?: unknown): unknown | this {\n    if (typeof state === 'undefined') {\n      return this.$$state;\n    }\n\n    this.$$state = state;\n    return this;\n  }\n}\n\n/**\n * The factory function used to create an instance of the `$locationShim` in Angular,\n * and provides an API-compatible `$locationProvider` for AngularJS.\n *\n * @publicApi\n */\nexport class $locationShimProvider {\n  constructor(\n    private ngUpgrade: UpgradeModule,\n    private location: Location,\n    private platformLocation: PlatformLocation,\n    private urlCodec: UrlCodec,\n    private locationStrategy: LocationStrategy,\n  ) {}\n\n  /**\n   * Factory method that returns an instance of the $locationShim\n   */\n  $get() {\n    return new $locationShim(\n      this.ngUpgrade.$injector,\n      this.location,\n      this.platformLocation,\n      this.urlCodec,\n      this.locationStrategy,\n    );\n  }\n\n  /**\n   * Stub method used to keep API compatible with AngularJS. This setting is configured through\n   * the LocationUpgradeModule's `config` method in your Angular app.\n   */\n  hashPrefix(prefix?: string) {\n    throw new Error('Configure LocationUpgrade through LocationUpgradeModule.config method.');\n  }\n\n  /**\n   * Stub method used to keep API compatible with AngularJS. This setting is configured through\n   * the LocationUpgradeModule's `config` method in your Angular app.\n   */\n  html5Mode(mode?: any) {\n    throw new Error('Configure LocationUpgrade through LocationUpgradeModule.config method.');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A codec for encoding and decoding URL parts.\n *\n * @publicApi\n **/\nexport abstract class UrlCodec {\n  /**\n   * Encodes the path from the provided string\n   *\n   * @param path The path string\n   */\n  abstract encodePath(path: string): string;\n\n  /**\n   * Decodes the path from the provided string\n   *\n   * @param path The path string\n   */\n  abstract decodePath(path: string): string;\n\n  /**\n   * Encodes the search string from the provided string or object\n   *\n   * @param path The path string or object\n   */\n  abstract encodeSearch(search: string | {[k: string]: unknown}): string;\n\n  /**\n   * Decodes the search objects from the provided string\n   *\n   * @param path The path string\n   */\n  abstract decodeSearch(search: string): {[k: string]: unknown};\n\n  /**\n   * Encodes the hash from the provided string\n   *\n   * @param path The hash string\n   */\n  abstract encodeHash(hash: string): string;\n\n  /**\n   * Decodes the hash from the provided string\n   *\n   * @param path The hash string\n   */\n  abstract decodeHash(hash: string): string;\n\n  /**\n   * Normalizes the URL from the provided string\n   *\n   * @param path The URL string\n   */\n  abstract normalize(href: string): string;\n\n  /**\n   * Normalizes the URL from the provided string, search, hash, and base URL parameters\n   *\n   * @param path The URL path\n   * @param search The search object\n   * @param hash The has string\n   * @param baseUrl The base URL for the URL\n   */\n  abstract normalize(\n    path: string,\n    search: {[k: string]: unknown},\n    hash: string,\n    baseUrl?: string,\n  ): string;\n\n  /**\n   * Checks whether the two strings are equal\n   * @param valA First string for comparison\n   * @param valB Second string for comparison\n   */\n  abstract areEqual(valA: string, valB: string): boolean;\n\n  /**\n   * Parses the URL string based on the base URL\n   *\n   * @param url The full URL string\n   * @param base The base for the URL\n   */\n  abstract parse(\n    url: string,\n    base?: string,\n  ): {\n    href: string;\n    protocol: string;\n    host: string;\n    search: string;\n    hash: string;\n    hostname: string;\n    port: string;\n    pathname: string;\n  };\n}\n\n/**\n * A `UrlCodec` that uses logic from AngularJS to serialize and parse URLs\n * and URL parameters.\n *\n * @publicApi\n */\nexport class AngularJSUrlCodec implements UrlCodec {\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L15\n  encodePath(path: string): string {\n    const segments = path.split('/');\n    let i = segments.length;\n\n    while (i--) {\n      // decode forward slashes to prevent them from being double encoded\n      segments[i] = encodeUriSegment(segments[i].replace(/%2F/g, '/'));\n    }\n\n    path = segments.join('/');\n    return _stripIndexHtml(((path && path[0] !== '/' && '/') || '') + path);\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L42\n  encodeSearch(search: string | {[k: string]: unknown}): string {\n    if (typeof search === 'string') {\n      search = parseKeyValue(search);\n    }\n\n    search = toKeyValue(search);\n    return search ? '?' + search : '';\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L44\n  encodeHash(hash: string) {\n    hash = encodeUriSegment(hash);\n    return hash ? '#' + hash : '';\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L27\n  decodePath(path: string, html5Mode = true): string {\n    const segments = path.split('/');\n    let i = segments.length;\n\n    while (i--) {\n      segments[i] = decodeURIComponent(segments[i]);\n      if (html5Mode) {\n        // encode forward slashes to prevent them from being mistaken for path separators\n        segments[i] = segments[i].replace(/\\//g, '%2F');\n      }\n    }\n\n    return segments.join('/');\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L72\n  decodeSearch(search: string) {\n    return parseKeyValue(search);\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L73\n  decodeHash(hash: string) {\n    hash = decodeURIComponent(hash);\n    return hash[0] === '#' ? hash.substring(1) : hash;\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L149\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/location.js#L42\n  normalize(href: string): string;\n  normalize(path: string, search: {[k: string]: unknown}, hash: string, baseUrl?: string): string;\n  normalize(\n    pathOrHref: string,\n    search?: {[k: string]: unknown},\n    hash?: string,\n    baseUrl?: string,\n  ): string {\n    if (arguments.length === 1) {\n      const parsed = this.parse(pathOrHref, baseUrl);\n\n      if (typeof parsed === 'string') {\n        return parsed;\n      }\n\n      const serverUrl = `${parsed.protocol}://${parsed.hostname}${\n        parsed.port ? ':' + parsed.port : ''\n      }`;\n\n      return this.normalize(\n        this.decodePath(parsed.pathname),\n        this.decodeSearch(parsed.search),\n        this.decodeHash(parsed.hash),\n        serverUrl,\n      );\n    } else {\n      const encPath = this.encodePath(pathOrHref);\n      const encSearch = (search && this.encodeSearch(search)) || '';\n      const encHash = (hash && this.encodeHash(hash)) || '';\n\n      let joinedPath = (baseUrl || '') + encPath;\n\n      if (!joinedPath.length || joinedPath[0] !== '/') {\n        joinedPath = '/' + joinedPath;\n      }\n      return joinedPath + encSearch + encHash;\n    }\n  }\n\n  areEqual(valA: string, valB: string) {\n    return this.normalize(valA) === this.normalize(valB);\n  }\n\n  // https://github.com/angular/angular.js/blob/864c7f0/src/ng/urlUtils.js#L60\n  parse(url: string, base?: string) {\n    try {\n      // Safari 12 throws an error when the URL constructor is called with an undefined base.\n      const parsed = !base ? new URL(url) : new URL(url, base);\n      return {\n        href: parsed.href,\n        protocol: parsed.protocol ? parsed.protocol.replace(/:$/, '') : '',\n        host: parsed.host,\n        search: parsed.search ? parsed.search.replace(/^\\?/, '') : '',\n        hash: parsed.hash ? parsed.hash.replace(/^#/, '') : '',\n        hostname: parsed.hostname,\n        port: parsed.port,\n        pathname: parsed.pathname.charAt(0) === '/' ? parsed.pathname : '/' + parsed.pathname,\n      };\n    } catch (e) {\n      throw new Error(`Invalid URL (${url}) with base (${base})`);\n    }\n  }\n}\n\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\n/**\n * Tries to decode the URI component without throwing an exception.\n *\n * @param str value potential URI component to check.\n * @returns the decoded URI if it can be decoded or else `undefined`.\n */\nfunction tryDecodeURIComponent(value: string): string | undefined {\n  try {\n    return decodeURIComponent(value);\n  } catch (e) {\n    // Ignore any invalid uri component.\n    return undefined;\n  }\n}\n\n/**\n * Parses an escaped url query string into key-value pairs. Logic taken from\n * https://github.com/angular/angular.js/blob/864c7f0/src/Angular.js#L1382\n */\nfunction parseKeyValue(keyValue: string): {[k: string]: unknown} {\n  const obj: {[k: string]: unknown} = {};\n  (keyValue || '').split('&').forEach((keyValue) => {\n    let splitPoint, key, val;\n    if (keyValue) {\n      key = keyValue = keyValue.replace(/\\+/g, '%20');\n      splitPoint = keyValue.indexOf('=');\n      if (splitPoint !== -1) {\n        key = keyValue.substring(0, splitPoint);\n        val = keyValue.substring(splitPoint + 1);\n      }\n      key = tryDecodeURIComponent(key);\n      if (typeof key !== 'undefined') {\n        val = typeof val !== 'undefined' ? tryDecodeURIComponent(val) : true;\n        if (!obj.hasOwnProperty(key)) {\n          obj[key] = val;\n        } else if (Array.isArray(obj[key])) {\n          (obj[key] as unknown[]).push(val);\n        } else {\n          obj[key] = [obj[key], val];\n        }\n      }\n    }\n  });\n  return obj;\n}\n\n/**\n * Serializes into key-value pairs. Logic taken from\n * https://github.com/angular/angular.js/blob/864c7f0/src/Angular.js#L1409\n */\nfunction toKeyValue(obj: {[k: string]: unknown}) {\n  const parts: unknown[] = [];\n  for (const key in obj) {\n    let value = obj[key];\n    if (Array.isArray(value)) {\n      value.forEach((arrayValue) => {\n        parts.push(\n          encodeUriQuery(key, true) +\n            (arrayValue === true ? '' : '=' + encodeUriQuery(arrayValue, true)),\n        );\n      });\n    } else {\n      parts.push(\n        encodeUriQuery(key, true) +\n          (value === true ? '' : '=' + encodeUriQuery(value as any, true)),\n      );\n    }\n  }\n  return parts.length ? parts.join('&') : '';\n}\n\n/**\n * We need our custom method because encodeURIComponent is too aggressive and doesn't follow\n * https://tools.ietf.org/html/rfc3986 with regards to the character set (pchar) allowed in path\n * segments:\n *    segment       = *pchar\n *    pchar         = unreserved / pct-encoded / sub-delims / \":\" / \"@\"\n *    pct-encoded   = \"%\" HEXDIG HEXDIG\n *    unreserved    = ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\"\n *    sub-delims    = \"!\" / \"$\" / \"&\" / \"'\" / \"(\" / \")\"\n *                     / \"*\" / \"+\" / \",\" / \";\" / \"=\"\n *\n * Logic from https://github.com/angular/angular.js/blob/864c7f0/src/Angular.js#L1437\n */\nfunction encodeUriSegment(val: string) {\n  return encodeUriQuery(val, true).replace(/%26/g, '&').replace(/%3D/gi, '=').replace(/%2B/gi, '+');\n}\n\n/**\n * This method is intended for encoding *key* or *value* parts of query component. We need a custom\n * method because encodeURIComponent is too aggressive and encodes stuff that doesn't have to be\n * encoded per https://tools.ietf.org/html/rfc3986:\n *    query         = *( pchar / \"/\" / \"?\" )\n *    pchar         = unreserved / pct-encoded / sub-delims / \":\" / \"@\"\n *    unreserved    = ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\"\n *    pct-encoded   = \"%\" HEXDIG HEXDIG\n *    sub-delims    = \"!\" / \"$\" / \"&\" / \"'\" / \"(\" / \")\"\n *                     / \"*\" / \"+\" / \",\" / \";\" / \"=\"\n *\n * Logic from https://github.com/angular/angular.js/blob/864c7f0/src/Angular.js#L1456\n */\nfunction encodeUriQuery(val: string, pctEncodeSpaces: boolean = false) {\n  return encodeURIComponent(val)\n    .replace(/%40/g, '@')\n    .replace(/%3A/gi, ':')\n    .replace(/%24/g, '$')\n    .replace(/%2C/gi, ',')\n    .replace(/%3B/gi, ';')\n    .replace(/%20/g, pctEncodeSpaces ? '%20' : '+');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  APP_BASE_HREF,\n  CommonModule,\n  HashLocationStrategy,\n  Location,\n  LocationStrategy,\n  PathLocationStrategy,\n  PlatformLocation,\n} from '@angular/common';\nimport {Inject, InjectionToken, ModuleWithProviders, NgModule, Optional} from '@angular/core';\nimport {UpgradeModule} from '@angular/upgrade/static';\n\nimport {$locationShim, $locationShimProvider} from './location_shim';\nimport {AngularJSUrlCodec, UrlCodec} from './params';\n\n/**\n * Configuration options for LocationUpgrade.\n *\n * @publicApi\n */\nexport interface LocationUpgradeConfig {\n  /**\n   * Configures whether the location upgrade module should use the `HashLocationStrategy`\n   * or the `PathLocationStrategy`\n   */\n  useHash?: boolean;\n  /**\n   * Configures the hash prefix used in the URL when using the `HashLocationStrategy`\n   */\n  hashPrefix?: string;\n  /**\n   * Configures the URL codec for encoding and decoding URLs. Default is the `AngularJSCodec`\n   */\n  urlCodec?: typeof UrlCodec;\n  /**\n   * Configures the base href when used in server-side rendered applications\n   */\n  serverBaseHref?: string;\n  /**\n   * Configures the base href when used in client-side rendered applications\n   */\n  appBaseHref?: string;\n}\n\n/**\n * A provider token used to configure the location upgrade module.\n *\n * @publicApi\n */\nexport const LOCATION_UPGRADE_CONFIGURATION = new InjectionToken<LocationUpgradeConfig>(\n  ngDevMode ? 'LOCATION_UPGRADE_CONFIGURATION' : '',\n);\n\nconst APP_BASE_HREF_RESOLVED = new InjectionToken<string>(\n  ngDevMode ? 'APP_BASE_HREF_RESOLVED' : '',\n);\n\n/**\n * `NgModule` used for providing and configuring Angular's Unified Location Service for upgrading.\n *\n * @see [Using the Unified Angular Location Service](https://angular.io/guide/upgrade#using-the-unified-angular-location-service)\n *\n * @publicApi\n */\n@NgModule({imports: [CommonModule]})\nexport class LocationUpgradeModule {\n  static config(config?: LocationUpgradeConfig): ModuleWithProviders<LocationUpgradeModule> {\n    return {\n      ngModule: LocationUpgradeModule,\n      providers: [\n        Location,\n        {\n          provide: $locationShim,\n          useFactory: provide$location,\n          deps: [UpgradeModule, Location, PlatformLocation, UrlCodec, LocationStrategy],\n        },\n        {provide: LOCATION_UPGRADE_CONFIGURATION, useValue: config ? config : {}},\n        {provide: UrlCodec, useFactory: provideUrlCodec, deps: [LOCATION_UPGRADE_CONFIGURATION]},\n        {\n          provide: APP_BASE_HREF_RESOLVED,\n          useFactory: provideAppBaseHref,\n          deps: [LOCATION_UPGRADE_CONFIGURATION, [new Inject(APP_BASE_HREF), new Optional()]],\n        },\n        {\n          provide: LocationStrategy,\n          useFactory: provideLocationStrategy,\n          deps: [PlatformLocation, APP_BASE_HREF_RESOLVED, LOCATION_UPGRADE_CONFIGURATION],\n        },\n      ],\n    };\n  }\n}\n\nexport function provideAppBaseHref(config: LocationUpgradeConfig, appBaseHref?: string) {\n  if (config && config.appBaseHref != null) {\n    return config.appBaseHref;\n  } else if (appBaseHref != null) {\n    return appBaseHref;\n  }\n  return '';\n}\n\nexport function provideUrlCodec(config: LocationUpgradeConfig) {\n  const codec = (config && config.urlCodec) || AngularJSUrlCodec;\n  return new (codec as any)();\n}\n\nexport function provideLocationStrategy(\n  platformLocation: PlatformLocation,\n  baseHref: string,\n  options: LocationUpgradeConfig = {},\n) {\n  return options.useHash\n    ? new HashLocationStrategy(platformLocation, baseHref)\n    : new PathLocationStrategy(platformLocation, baseHref);\n}\n\nexport function provide$location(\n  ngUpgrade: UpgradeModule,\n  location: Location,\n  platformLocation: PlatformLocation,\n  urlCodec: UrlCodec,\n  locationStrategy: LocationStrategy,\n) {\n  const $locationProvider = new $locationShimProvider(\n    ngUpgrade,\n    location,\n    platformLocation,\n    urlCodec,\n    locationStrategy,\n  );\n\n  return $locationProvider.$get();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/index';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["isPromise"], "mappings": ";;;;;;;;;;;;AAQgB,SAAA,WAAW,CAAC,GAAW,EAAE,MAAc,EAAA;IACrD,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;AACpE,CAAA;AAEgB,SAAA,SAAS,CAAC,CAAM,EAAE,CAAM,EAAA;AACtC,IAAA,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,QAAA,OAAO,IAAI,CAAA;KACb;AAAO,SAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;AACnB,QAAA,OAAO,KAAK,CAAA;KACd;SAAO;AACL,QAAA,IAAI;YACF,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;AACzE,gBAAA,OAAO,KAAK,CAAA;aACd;AACA,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChD;QAAE,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,KAAK,CAAA;SACd;KACF;AACF,CAAA;AAEM,SAAU,QAAQ,CAAC,EAAwC,EAAA;AAC/D,IAAA,OAA2B,EAAG,CAAC,IAAI,KAAK,SAAS,CAAA;AACnD;;ACfA,MAAM,UAAU,GAAG,gCAAgC,CAAA;AACnD,MAAM,kBAAkB,GAAG,eAAe,CAAA;AAC1C,MAAM,iBAAiB,GAAG,2BAA2B,CAAA;AACrD,MAAM,aAAa,GAA4B;AAC7C,IAAA,OAAO,EAAE,EAAE;AACX,IAAA,QAAQ,EAAE,GAAG;AACb,IAAA,MAAM,EAAE,EAAE;CACX,CAAA;AAED;;;;;;;AAOG;MACU,aAAa,CAAA;AA8Bd,IAAA,QAAA,CAAA;AACA,IAAA,gBAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,gBAAA,CAAA;IAhCF,YAAY,GAAG,IAAI,CAAA;IACnB,aAAa,GAAG,KAAK,CAAA;IACrB,QAAQ,GAAW,EAAE,CAAA;IACrB,KAAK,GAAW,EAAE,CAAA;AAClB,IAAA,UAAU,CAAA;IACV,MAAM,GAAW,EAAE,CAAA;AACnB,IAAA,MAAM,CAAA;IACN,SAAS,GAAY,KAAK,CAAA;IAC1B,MAAM,GAAW,EAAE,CAAA;IACnB,QAAQ,GAAQ,EAAE,CAAA;IAClB,MAAM,GAAW,EAAE,CAAA;AACnB,IAAA,OAAO,CAAA;IACP,iBAAiB,GASnB,EAAE,CAAA;IAEA,WAAW,GAAY,IAAI,CAAA;AAE3B,IAAA,UAAU,GAAG,IAAI,aAAa,CAAsC,CAAC,CAAC,CAAA;IAE9E,WACE,CAAA,SAAc,EACN,QAAkB,EAClB,gBAAkC,EAClC,QAAkB,EAClB,gBAAkC,EAAA;QAHlC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAA;QACR,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAA;QAChB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAA;QACR,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAA;AAExB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QAEpC,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;AAE/C,QAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;AACjC,YAAA,MAAM,aAAa,CAAA;SACrB;AAEA,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAA;AACpC,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAA;AAChC,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAA;AAEnF,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QAC3C,IAAI,CAAC,UAAU,EAAE,CAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QAElC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,QAAQ,KAAI;YAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAC,CAAC,CAAA;AAC1C,SAAC,CAAC,CAAA;AAEF,QAAA,IAAIA,UAAS,CAAC,SAAS,CAAC,EAAE;AACxB,YAAA,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;SAC7C;aAAO;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;SAC5B;KACF;AAEQ,IAAA,UAAU,CAAC,SAAc,EAAA;QAC/B,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAC9C,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAElD,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,KAAI;YACtC,IACE,KAAK,CAAC,OAAO;AACb,gBAAA,KAAK,CAAC,OAAO;AACb,gBAAA,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,KAAK,KAAK,CAAC;AACjB,gBAAA,KAAK,CAAC,MAAM,KAAK,CAAC,EAClB;gBACA,OAAO;aACT;AAEA,YAAA,IAAI,GAAG,GAA+B,KAAK,CAAC,MAAM,CAAA;;YAGlD,OAAO,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;;AAEhD,gBAAA,IAAI,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE;oBACtD,OAAO;iBACT;aACF;AAEA,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAClB,OAAO;aACT;AAEA,YAAA,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAA;YACxB,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;;AAGxC,YAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACnC,OAAO;aACT;AAEA,YAAA,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE;gBACzE,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;;;;oBAIzC,KAAK,CAAC,cAAc,EAAE,CAAA;;oBAEtB,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,EAAE;wBACvC,UAAU,CAAC,MAAM,EAAE,CAAA;qBACrB;iBACF;aACF;AACF,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAC,KAAI;AAC/C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;AAC5B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAA;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;AACpB,YAAA,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;AACtB,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAA;AACvB,YAAA,MAAM,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAC5C,sBAAsB,EACtB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,CACT,CAAC,gBAAgB,CAAA;;;AAIlB,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,MAAM;gBAAE,OAAO;;;YAIrC,IAAI,gBAAgB,EAAE;AACpB,gBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;AACpB,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACpB,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;AACvD,gBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;aAC1E;iBAAO;AACL,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;AACzB,gBAAA,UAAU,CAAC,UAAU,CAAC,wBAAwB,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;gBACnF,IAAI,CAAC,kBAAkB,EAAE,CAAA;aAC3B;AACA,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;gBACvB,UAAU,CAAC,OAAO,EAAE,CAAA;aACtB;AACF,SAAC,CAAC,CAAA;;AAGF,QAAA,UAAU,CAAC,MAAM,CAAC,MAAK;YACrB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3C,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;AAE1B,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;AAChC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;AAC5B,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;AACpC,gBAAA,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAA;AAEnC,gBAAA,MAAM,iBAAiB,GACrB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAA;;;;;AAMtE,gBAAA,IAAI,IAAI,CAAC,YAAY,IAAI,iBAAiB,EAAE;AAC1C,oBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;AAEzB,oBAAA,UAAU,CAAC,UAAU,CAAC,MAAK;;AAEzB,wBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;wBAC5B,MAAM,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAC5C,sBAAsB,EACtB,MAAM,EACN,MAAM,EACN,IAAI,CAAC,OAAO,EACZ,QAAQ,CACT,CAAC,gBAAgB,CAAA;;;AAIlB,wBAAA,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,MAAM;4BAAE,OAAO;wBAErC,IAAI,gBAAgB,EAAE;AACpB,4BAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;AACpB,4BAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAA;yBACzB;6BAAO;;;4BAGL,IAAI,iBAAiB,EAAE;gCACrB,IAAI,CAAC,yBAAyB,CAC5B,MAAM,EACN,cAAc,EACd,QAAQ,KAAK,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAChD,CAAA;AACD,gCAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;6BACxB;AACA,4BAAA,UAAU,CAAC,UAAU,CACnB,wBAAwB,EACxB,MAAM,EACN,MAAM,EACN,IAAI,CAAC,OAAO,EACZ,QAAQ,CACT,CAAA;4BACD,IAAI,iBAAiB,EAAE;AACrB,gCAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;6BAC1E;yBACF;AACF,qBAAC,CAAC,CAAA;iBACJ;aACF;AACA,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;AACxB,SAAC,CAAC,CAAA;KACJ;IAEQ,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;KACzC;AAEQ,IAAA,gBAAgB,CAAA;IAChB,cAAc,GAAW,EAAE,CAAA;AAG3B,IAAA,UAAU,CAAC,GAAY,EAAE,OAAiB,EAAE,KAAe,EAAA;;;;AAIjE,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,KAAK,GAAG,IAAI,CAAA;SACd;;QAGA,IAAI,GAAG,EAAE;AACP,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAA;;YAG/C,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA;;YAGnC,IAAI,IAAI,CAAC,cAAc,KAAK,GAAG,IAAI,SAAS,EAAE;AAC5C,gBAAA,OAAO,IAAI,CAAA;aACb;AACA,YAAA,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA;AACzB,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;;;AAI7B,YAAA,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,IAAI,GAAG,CAAA;;YAGzD,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;aACxD;iBAAO;AACL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;aACrD;YAEA,IAAI,CAAC,UAAU,EAAE,CAAA;AAEjB,YAAA,OAAO,IAAI,CAAA;;SAEb;aAAO;AACL,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAA;SACnC;KACF;;IAGQ,eAAe,GAAY,IAAI,CAAA;IAC/B,UAAU,GAAA;;QAEhB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAA;AACnD,QAAA,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW,EAAE;AAC3C,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;SACzB;;QAGA,IAAI,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE;AACrD,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAA;SACzC;AAEA,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAA;AACvC,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAA;KAC1C;AAEA;;;AAGG;IACK,YAAY,GAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAA;KACzB;IAEQ,YAAY,CAAC,IAAY,EAAE,GAAW,EAAA;AAC5C,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACxB,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC/B;AACA,QAAA,OAAO,SAAS,CAAA;KAClB;IAEQ,aAAa,GAAA;QACnB,MAAM,EAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,gBAAgB,CAAA;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAA;QACpD,IAAI,GAAG,GAAG,CAAG,EAAA,QAAQ,KAAK,QAAQ,CAAA,EAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,QAAQ,IAAI,GAAG,CAAA,CAAE,CAAA;AAC/E,QAAA,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;KAC5C;AAEQ,IAAA,WAAW,CAAC,GAAW,EAAA;AAC7B,QAAA,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAChC,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,GAAG,CAAA,CAAE,CAAC,CAAA;SAC5E;QAEA,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;QACpC,IAAI,QAAQ,EAAE;AACZ,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;SACjB;AACA,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;AAC1D,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAA,CAAE,CAAC,CAAA;SACvD;AACA,QAAA,IAAI,IAAI,GACN,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAA;QAC7F,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;AAC5C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;AACxD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;;AAGlD,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAChD,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;SACjC;KACF;AAEA;;;;;;;;;;;;AAYG;IACH,QAAQ,CACN,EAA4E,EAC5E,GAAA,GAA0B,CAAC,CAAQ,KAAI,GAAG,EAAA;QAE1C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;KACxC;;IAGA,uBAAuB,CACrB,MAAc,EAAE,EAChB,KAAc,EACd,MAAA,GAAiB,EAAE,EACnB,QAAiB,EAAA;AAEjB,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,KAAI;AAC3C,YAAA,IAAI;gBACF,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;aAClC;YAAE,OAAO,CAAC,EAAE;gBACV,GAAG,CAAC,CAAU,CAAC,CAAA;aACjB;AACF,SAAC,CAAC,CAAA;KACJ;AAEA;;;;AAIG;AACH,IAAA,OAAO,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,OAA2B,CAAA;AAC/B,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACvB,OAAO,GAAG,GAAG,CAAA;SACf;aAAO;;AAEL,YAAA,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,CAAA;SACxD;AACA,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,GAAG,CAAA,wBAAA,EAA2B,IAAI,CAAC,aAAa,EAAE,CAAI,EAAA,CAAA,CAAC,CAAA;SACzF;AAEA,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AAEzB,QAAA,IAAI,CAAC,MAAM,KAAK,GAAG,CAAA;QACnB,IAAI,CAAC,WAAW,EAAE,CAAA;KACpB;AAEA;;;;;AAKG;IACH,cAAc,CAAC,GAAW,EAAE,OAAuB,EAAA;;QAEjD,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3B,YAAA,OAAO,IAAI,CAAA;SACb;AACA,QAAA,IAAI,YAAY,CAAA;AAChB,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,CAAA;AACzD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,YAAA,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,CAAA;SAC9C;aAAO,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,GAAG,EAAE;AAC7C,YAAA,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;SACrC;;QAEA,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;SAC5B;QACA,OAAO,CAAC,CAAC,YAAY,CAAA;KACvB;AAEQ,IAAA,yBAAyB,CAAC,GAAW,EAAE,OAAgB,EAAE,KAAc,EAAA;AAC7E,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAA;AAC7B,QAAA,IAAI;YACF,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;;;;AAKpC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;SACpC;QAAE,OAAO,CAAC,EAAE;;AAEV,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAA;AAEvB,YAAA,MAAM,CAAC,CAAA;SACT;KACF;IAEQ,WAAW,GAAA;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;AAC7E,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;KAC3B;AAEA;;;;;;;;;;;AAWG;IACH,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,QAAQ,CAAA;KACtB;AAcA,IAAA,GAAG,CAAC,GAAY,EAAA;AACd,QAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC3B,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;gBACf,GAAG,GAAG,GAAG,CAAA;aACX;YAEA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAClC,YAAA,IAAI,CAAC,KAAK;AAAE,gBAAA,OAAO,IAAI,CAAA;AACvB,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,EAAE;AAAE,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACzE,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,EAAE;gBAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;YACnE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;;AAGzB,YAAA,OAAO,IAAI,CAAA;SACb;QAEA,OAAO,IAAI,CAAC,KAAK,CAAA;KACnB;AAEA;;;;;;;;AAQG;IACH,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAA;KACxB;AAEA;;;;;;;;;;;;;;;;;;AAkBG;IACH,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,MAAM,CAAA;KACpB;AAEA;;;;;;;;AAQG;IACH,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,MAAM,CAAA;KACpB;AAiBA,IAAA,IAAI,CAAC,IAA6B,EAAA;AAChC,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC/B,OAAO,IAAI,CAAC,MAAM,CAAA;SACpB;;AAGA,QAAA,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAA;AAC3C,QAAA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAA;AAEjD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAElB,IAAI,CAAC,WAAW,EAAE,CAAA;AAClB,QAAA,OAAO,IAAI,CAAA;KACb;IA8CA,MAAM,CACJ,MAAmD,EACnD,UAAoE,EAAA;AAEpE,QAAA,QAAQ,SAAS,CAAC,MAAM;AACtB,YAAA,KAAK,CAAC;gBACJ,OAAO,IAAI,CAAC,QAAQ,CAAA;AACtB,YAAA,KAAK,CAAC;gBACJ,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC5D,oBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;iBAC/D;qBAAO,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;;AAExD,oBAAA,MAAM,GAAG,EAAC,GAAG,MAAM,EAAC,CAAA;;AAEpB,oBAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AACxB,wBAAA,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI;AAAE,4BAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;qBAC7C;AAEA,oBAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAA;iBACxB;qBAAO;AACL,oBAAA,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E,CAAA;iBACH;gBACA,MAAM;AACR,YAAA;AACE,gBAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,oBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;oBACnC,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU,KAAK,IAAI,EAAE;AAC5D,wBAAA,OAAO,aAAa,CAAC,MAAM,CAAC,CAAA;AAC5B,wBAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;qBACnC;yBAAO;AACL,wBAAA,aAAa,CAAC,MAAM,CAAC,GAAG,UAAU,CAAA;AAClC,wBAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;qBACnC;iBACF;SACJ;QACA,IAAI,CAAC,WAAW,EAAE,CAAA;AAClB,QAAA,OAAO,IAAI,CAAA;KACb;AAcA,IAAA,IAAI,CAAC,IAA6B,EAAA;AAChC,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC/B,OAAO,IAAI,CAAC,MAAM,CAAA;SACpB;AAEA,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAA;QAElD,IAAI,CAAC,WAAW,EAAE,CAAA;AAClB,QAAA,OAAO,IAAI,CAAA;KACb;AAEA;;;AAGG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;AACrB,QAAA,OAAO,IAAI,CAAA;KACb;AAeA,IAAA,KAAK,CAAC,KAAe,EAAA;AACnB,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,OAAO,IAAI,CAAC,OAAO,CAAA;SACrB;AAEA,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;AACpB,QAAA,OAAO,IAAI,CAAA;KACb;AACD,CAAA;AAED;;;;;AAKG;MACU,qBAAqB,CAAA;AAEtB,IAAA,SAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,gBAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,gBAAA,CAAA;IALV,WACU,CAAA,SAAwB,EACxB,QAAkB,EAClB,gBAAkC,EAClC,QAAkB,EAClB,gBAAkC,EAAA;QAJlC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAA;QACT,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAA;QACR,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAA;QAChB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAA;QACR,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAA;KACvB;AAEH;;AAEG;IACH,IAAI,GAAA;QACF,OAAO,IAAI,aAAa,CACtB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,gBAAgB,CACtB,CAAA;KACH;AAEA;;;AAGG;AACH,IAAA,UAAU,CAAC,MAAe,EAAA;AACxB,QAAA,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAA;KAC3F;AAEA;;;AAGG;AACH,IAAA,SAAS,CAAC,IAAU,EAAA;AAClB,QAAA,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAA;KAC3F;AACD;;ACzwBD;;;;AAII;MACkB,QAAQ,CAAA;AA2F7B,CAAA;AAED;;;;;AAKG;MACU,iBAAiB,CAAA;;AAE5B,IAAA,UAAU,CAAC,IAAY,EAAA;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AAChC,QAAA,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAA;QAEvB,OAAO,CAAC,EAAE,EAAE;;AAEV,YAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;SAClE;AAEA,QAAA,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACzB,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,EAAE,IAAI,IAAI,CAAC,CAAA;KACzE;;AAGA,IAAA,YAAY,CAAC,MAAuC,EAAA;AAClD,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,YAAA,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;SAChC;AAEA,QAAA,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAA;QAC3B,OAAO,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE,CAAA;KACnC;;AAGA,IAAA,UAAU,CAAC,IAAY,EAAA;AACrB,QAAA,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAC7B,OAAO,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,CAAA;KAC/B;;AAGA,IAAA,UAAU,CAAC,IAAY,EAAE,SAAS,GAAG,IAAI,EAAA;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AAChC,QAAA,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAA;QAEvB,OAAO,CAAC,EAAE,EAAE;YACV,QAAQ,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAC7C,IAAI,SAAS,EAAE;;AAEb,gBAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;aACjD;SACF;AAEA,QAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KAC3B;;AAGA,IAAA,YAAY,CAAC,MAAc,EAAA;AACzB,QAAA,OAAO,aAAa,CAAC,MAAM,CAAC,CAAA;KAC9B;;AAGA,IAAA,UAAU,CAAC,IAAY,EAAA;AACrB,QAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;KACnD;AAMA,IAAA,SAAS,CACP,UAAkB,EAClB,MAA+B,EAC/B,IAAa,EACb,OAAgB,EAAA;AAEhB,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;AAE9C,YAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,gBAAA,OAAO,MAAM,CAAA;aACf;YAEA,MAAM,SAAS,GAAG,CAAA,EAAG,MAAM,CAAC,QAAQ,CAAA,GAAA,EAAM,MAAM,CAAC,QAAQ,CAAA,EACvD,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,EACpC,CAAA,CAAE,CAAA;AAEF,YAAA,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAChC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAC5B,SAAS,CACV,CAAA;SACH;aAAO;YACL,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;AAC3C,YAAA,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;AAC7D,YAAA,MAAM,OAAO,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA;YAErD,IAAI,UAAU,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,OAAO,CAAA;AAE1C,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC/C,gBAAA,UAAU,GAAG,GAAG,GAAG,UAAU,CAAA;aAC/B;AACA,YAAA,OAAO,UAAU,GAAG,SAAS,GAAG,OAAO,CAAA;SACzC;KACF;IAEA,QAAQ,CAAC,IAAY,EAAE,IAAY,EAAA;AACjC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;KACtD;;IAGA,KAAK,CAAC,GAAW,EAAE,IAAa,EAAA;AAC9B,QAAA,IAAI;;YAEF,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACxD,OAAO;gBACL,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE;gBAClE,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE;gBAC7D,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE;gBACtD,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ;aACtF,CAAA;SACH;QAAE,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,GAAG,CAAgB,aAAA,EAAA,IAAI,CAAG,CAAA,CAAA,CAAC,CAAA;SAC7D;KACF;AACD,CAAA;AAED,SAAS,eAAe,CAAC,GAAW,EAAA;IAClC,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAA;AACzC,CAAA;AAEA;;;;;AAKG;AACH,SAAS,qBAAqB,CAAC,KAAa,EAAA;AAC1C,IAAA,IAAI;AACF,QAAA,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAA;KAClC;IAAE,OAAO,CAAC,EAAE;;AAEV,QAAA,OAAO,SAAS,CAAA;KAClB;AACF,CAAA;AAEA;;;AAGG;AACH,SAAS,aAAa,CAAC,QAAgB,EAAA;IACrC,MAAM,GAAG,GAA2B,EAAE,CAAA;AACtC,IAAA,CAAC,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC/C,QAAA,IAAI,UAAU,EAAE,GAAG,EAAE,GAAG,CAAA;QACxB,IAAI,QAAQ,EAAE;YACZ,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AAC/C,YAAA,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AAClC,YAAA,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;gBACrB,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;gBACvC,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;aAC1C;AACA,YAAA,GAAG,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAA;AAChC,YAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC9B,gBAAA,GAAG,GAAG,OAAO,GAAG,KAAK,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;gBACpE,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC5B,oBAAA,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;iBAChB;qBAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;oBACjC,GAAG,CAAC,GAAG,CAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;iBACnC;qBAAO;AACL,oBAAA,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;iBAC5B;aACF;SACF;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAO,GAAG,CAAA;AACZ,CAAA;AAEA;;;AAGG;AACH,SAAS,UAAU,CAAC,GAA2B,EAAA;IAC7C,MAAM,KAAK,GAAc,EAAE,CAAA;AAC3B,IAAA,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AACrB,QAAA,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;AACpB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;gBAC3B,KAAK,CAAC,IAAI,CACR,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;qBACtB,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CACtE,CAAA;AACH,aAAC,CAAC,CAAA;SACJ;aAAO;YACL,KAAK,CAAC,IAAI,CACR,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;iBACtB,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,cAAc,CAAC,KAAY,EAAE,IAAI,CAAC,CAAC,CACnE,CAAA;SACH;KACF;AACA,IAAA,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;AAC5C,CAAA;AAEA;;;;;;;;;;;;AAYG;AACH,SAAS,gBAAgB,CAAC,GAAW,EAAA;IACnC,OAAO,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;AACnG,CAAA;AAEA;;;;;;;;;;;;AAYG;AACH,SAAS,cAAc,CAAC,GAAW,EAAE,kBAA2B,KAAK,EAAA;IACnE,OAAO,kBAAkB,CAAC,GAAG,CAAA;AAC1B,SAAA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAA;AACnB,SAAA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAA;AACpB,SAAA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAA;AACnB,SAAA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAA;AACpB,SAAA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAA;AACpB,SAAA,OAAO,CAAC,MAAM,EAAE,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC,CAAA;AACnD;;ACzSA;;;;AAIG;AACU,MAAA,8BAA8B,GAAG,IAAI,cAAc,CAC9D,SAAS,GAAG,gCAAgC,GAAG,EAAE,EAClD;AAED,MAAM,sBAAsB,GAAG,IAAI,cAAc,CAC/C,SAAS,GAAG,wBAAwB,GAAG,EAAE,CAC1C,CAAA;AAED;;;;;;AAMG;MAEU,qBAAqB,CAAA;IAChC,OAAO,MAAM,CAAC,MAA8B,EAAA;QAC1C,OAAO;AACL,YAAA,QAAQ,EAAE,qBAAqB;AAC/B,YAAA,SAAS,EAAE;gBACT,QAAQ;AACR,gBAAA;AACE,oBAAA,OAAO,EAAE,aAAa;AACtB,oBAAA,UAAU,EAAE,gBAAgB;oBAC5B,IAAI,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,CAAC;AAC9E,iBAAA;AACD,gBAAA,EAAC,OAAO,EAAE,8BAA8B,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,EAAE,EAAC;AACzE,gBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,8BAA8B,CAAC,EAAC;AACxF,gBAAA;AACE,oBAAA,OAAO,EAAE,sBAAsB;AAC/B,oBAAA,UAAU,EAAE,kBAAkB;AAC9B,oBAAA,IAAI,EAAE,CAAC,8BAA8B,EAAE,CAAC,IAAI,MAAM,CAAC,aAAa,CAAC,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;AACpF,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,gBAAgB;AACzB,oBAAA,UAAU,EAAE,uBAAuB;AACnC,oBAAA,IAAI,EAAE,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,8BAA8B,CAAC;AACjF,iBAAA;AACF,aAAA;SACF,CAAA;KACH;kHAzBW,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YADb,YAAY,CAAA,EAAA,CAAA,CAAA;AACpB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YADb,YAAY,CAAA,EAAA,CAAA,CAAA;;sGACpB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,OAAO,EAAE,CAAC,YAAY,CAAC,EAAC,CAAA;;AA6BnB,SAAA,kBAAkB,CAAC,MAA6B,EAAE,WAAoB,EAAA;IACpF,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE;QACxC,OAAO,MAAM,CAAC,WAAW,CAAA;KAC3B;AAAO,SAAA,IAAI,WAAW,IAAI,IAAI,EAAE;AAC9B,QAAA,OAAO,WAAW,CAAA;KACpB;AACA,IAAA,OAAO,EAAE,CAAA;AACX,CAAA;AAEM,SAAU,eAAe,CAAC,MAA6B,EAAA;IAC3D,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,iBAAiB,CAAA;IAC9D,OAAO,IAAK,KAAa,EAAE,CAAA;AAC7B,CAAA;AAEM,SAAU,uBAAuB,CACrC,gBAAkC,EAClC,QAAgB,EAChB,UAAiC,EAAE,EAAA;IAEnC,OAAO,OAAO,CAAC,OAAO;AACpB,UAAE,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,CAAA;UACnD,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;AAC1D,CAAA;AAEM,SAAU,gBAAgB,CAC9B,SAAwB,EACxB,QAAkB,EAClB,gBAAkC,EAClC,QAAkB,EAClB,gBAAkC,EAAA;AAElC,IAAA,MAAM,iBAAiB,GAAG,IAAI,qBAAqB,CACjD,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,gBAAgB,CACjB,CAAA;AAED,IAAA,OAAO,iBAAiB,CAAC,IAAI,EAAE,CAAA;AACjC;;ACrIA;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}