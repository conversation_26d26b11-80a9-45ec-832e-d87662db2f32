{"$schema": "http://json-schema.org/draft-07/schema", "title": "Application schema for Build Facade.", "description": "Application builder target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/assetPattern"}}, "browser": {"type": "string", "description": "The full path for the browser entry point to the application, relative to the current workspace."}, "server": {"type": "string", "description": "The full path for the server entry point to the application, relative to the current workspace.", "oneOf": [{"type": "string", "description": "The full path for the server entry point to the application, relative to the current workspace."}, {"const": false, "type": "boolean", "description": "Indicates that a server entry point is not provided."}]}, "polyfills": {"description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "type": "array", "items": {"type": "string", "uniqueItems": true}, "default": []}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "security": {"description": "Security features to protect against XSS and other common attacks", "type": "object", "additionalProperties": false, "properties": {"autoCsp": {"description": "Enables automatic generation of a hash-based Strict Content Security Policy (https://web.dev/articles/strict-csp#choose-hash) based on scripts in index.html. Will default to true once we are out of experimental/preview phases.", "default": false, "oneOf": [{"type": "object", "properties": {"unsafeEval": {"type": "boolean", "description": "Include the `unsafe-eval` directive (https://web.dev/articles/strict-csp#remove-eval) in the auto-CSP. Please only enable this if you are absolutely sure that you need to, as allowing calls to eval will weaken the XSS defenses provided by the auto-CSP.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The JavaScript/TypeScript file or package containing the file to include."}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}, "sass": {"description": "Options to pass to the sass preprocessor.", "type": "object", "properties": {"fatalDeprecations": {"description": "A set of deprecations to treat as fatal. If a deprecation warning of any provided type is encountered during compilation, the compiler will error instead. If a Version is provided, then all deprecations that were active in that compiler version will be treated as fatal.", "type": "array", "items": {"type": "string"}}, "silenceDeprecations": {"description": " A set of active deprecations to ignore. If a deprecation warning of any provided type is encountered during compilation, the compiler will ignore it instead.", "type": "array", "items": {"type": "string"}}, "futureDeprecations": {"description": "A set of future deprecations to opt into early. Future deprecations passed here will be treated as active by the compiler, emitting warnings as necessary.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "clearScreen": {"type": "boolean", "default": false, "description": "Automatically clear the terminal screen during rebuilds."}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "x-user-analytics": "ep.ng_optimization", "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}, "removeSpecialComments": {"type": "boolean", "description": "Remove comments in global CSS that contains '@license' or '@preserve' or that starts with '//!' or '/*!'.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "loader": {"description": "Defines the type of loader to use with a specified file extension when used with a JavaScript `import`. `text` inlines the content as a string; `binary` inlines the content as a Uint8Array; `file` emits the file and provides the runtime location of the file; `empty` considers the content to be empty and not include it in bundles.", "type": "object", "patternProperties": {"^\\.\\S+$": {"enum": ["text", "binary", "file", "empty"]}}}, "define": {"description": "Defines global identifiers that will be replaced with a specified constant value when found in any JavaScript or TypeScript code including libraries. The value will be used directly. String values must be put in quotes. Identifiers within Angular metadata such as Component Decorators will not be replaced.", "type": "object", "additionalProperties": {"type": "string"}}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/fileReplacement"}, "default": []}, "outputPath": {"description": "Specify the output path relative to workspace root.", "oneOf": [{"type": "object", "properties": {"base": {"type": "string", "description": "Specify the output path relative to workspace root."}, "browser": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "browser", "description": "The output directory name of your browser build within the output path base. Defaults to 'browser'."}, "server": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "server", "description": "The output directory name of your server build within the output path base. Defaults to 'server'."}, "media": {"type": "string", "pattern": "^[-\\w\\.]+$", "default": "media", "description": "The output directory name of your media files within the output browser directory. Defaults to 'media'."}}, "required": ["base"], "additionalProperties": false}, {"type": "string"}]}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "x-user-analytics": "ep.ng_aot", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"description": "Generates a service worker configuration.", "default": false, "oneOf": [{"type": "string", "description": "Path to ngsw-config.json."}, {"const": false, "type": "boolean", "description": "Does not generate a service worker configuration."}]}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}, "preloadInitial": {"type": "boolean", "default": true, "description": "Generates 'preload', 'modulepreload', and 'preconnect' link elements for initial application files and resources."}}, "required": ["input"]}, {"const": false, "type": "boolean", "description": "Does not generate an `index.html` file."}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed with https://esbuild.github.io/analyze/.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}, "prerender": {"description": "Prerender (SSG) pages of your application during build time.", "oneOf": [{"type": "boolean", "description": "Enable prerending of pages of your application during build time."}, {"type": "object", "properties": {"routesFile": {"type": "string", "description": "The path to a file that contains a list of all routes to prerender, separated by newlines. This option is useful if you want to prerender routes with parameterized URLs."}, "discoverRoutes": {"type": "boolean", "description": "Whether the builder should process the Angular Router configuration to find all unparameterized routes and prerender them.", "default": true}}, "additionalProperties": false}]}, "ssr": {"description": "Server side render (SSR) pages of your application during runtime.", "default": false, "oneOf": [{"type": "boolean", "description": "Enable the server bundles to be written to disk."}, {"type": "object", "properties": {"entry": {"type": "string", "description": "The server entry-point that when executed will spawn the web server."}, "experimentalPlatform": {"description": "Specifies the platform for which the server bundle is generated. This affects the APIs and modules available in the server-side code. \n\n- `node`:  (<PERSON><PERSON><PERSON>) Generates a bundle optimized for Node.js environments. \n- `neutral`: Generates a platform-neutral bundle suitable for environments like edge workers, and other serverless platforms. This option avoids using Node.js-specific APIs, making the bundle more portable. \n\nPlease note that this feature does not provide polyfills for Node.js modules. Additionally, it is experimental, and the schematics may undergo changes in future versions.", "default": "node", "enum": ["node", "neutral"]}}, "additionalProperties": false}]}, "appShell": {"type": "boolean", "description": "Generates an application shell during build time."}, "outputMode": {"type": "string", "description": "Defines the build output target. 'static': Generates a static site for deployment on any static hosting service. 'server': Produces an application designed for deployment on a server that supports server-side rendering (SSR).", "enum": ["static", "server"]}}, "additionalProperties": false, "required": ["outputPath", "index", "browser", "tsConfig"], "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false, "required": ["glob", "input"]}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["replace", "with"]}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false, "required": ["type"]}}}