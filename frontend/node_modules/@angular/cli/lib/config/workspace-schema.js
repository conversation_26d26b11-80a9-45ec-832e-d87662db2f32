"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.Implement = exports.SchematicsAngularComponentStyle = exports.ChangeDetection = exports.ViewEncapsulation = exports.SchematicsAngularApplicationStyle = exports.PackageManager = exports.Environment = void 0;
/**
 * Configure in which environment disk cache is enabled.
 */
var Environment;
(function (Environment) {
    Environment["All"] = "all";
    Environment["Ci"] = "ci";
    Environment["Local"] = "local";
})(Environment || (exports.Environment = Environment = {}));
/**
 * Specify which package manager tool to use.
 *
 * The package manager used to install dependencies.
 */
var PackageManager;
(function (PackageManager) {
    PackageManager["Bun"] = "bun";
    PackageManager["Cnpm"] = "cnpm";
    PackageManager["Npm"] = "npm";
    PackageManager["Pnpm"] = "pnpm";
    PackageManager["Yarn"] = "yarn";
})(PackageManager || (exports.PackageManager = PackageManager = {}));
/**
 * The type of stylesheet files to be created for components in the application.
 *
 * The type of stylesheet files to be created for components in the initial project.
 */
var SchematicsAngularApplicationStyle;
(function (SchematicsAngularApplicationStyle) {
    SchematicsAngularApplicationStyle["Css"] = "css";
    SchematicsAngularApplicationStyle["Less"] = "less";
    SchematicsAngularApplicationStyle["Sass"] = "sass";
    SchematicsAngularApplicationStyle["Scss"] = "scss";
})(SchematicsAngularApplicationStyle || (exports.SchematicsAngularApplicationStyle = SchematicsAngularApplicationStyle = {}));
/**
 * Sets the view encapsulation mode for the application's components. This determines how
 * component styles are scoped and applied.
 *
 * Sets the view encapsulation mode for the component. This determines how the component's
 * styles are scoped and applied.
 *
 * Sets the view encapsulation mode for components in the initial project. This determines
 * how component styles are scoped and applied. Options include: `Emulated` (default, styles
 * are scoped to the component), `None` (styles are global), and `ShadowDom` (styles are
 * encapsulated using Shadow DOM).
 */
var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation["Emulated"] = "Emulated";
    ViewEncapsulation["None"] = "None";
    ViewEncapsulation["ShadowDom"] = "ShadowDom";
})(ViewEncapsulation || (exports.ViewEncapsulation = ViewEncapsulation = {}));
/**
 * Configures the change detection strategy for the component.
 */
var ChangeDetection;
(function (ChangeDetection) {
    ChangeDetection["Default"] = "Default";
    ChangeDetection["OnPush"] = "OnPush";
})(ChangeDetection || (exports.ChangeDetection = ChangeDetection = {}));
/**
 * Specify the type of stylesheet to be created for the component, or `none` to skip
 * creating a stylesheet.
 */
var SchematicsAngularComponentStyle;
(function (SchematicsAngularComponentStyle) {
    SchematicsAngularComponentStyle["Css"] = "css";
    SchematicsAngularComponentStyle["Less"] = "less";
    SchematicsAngularComponentStyle["None"] = "none";
    SchematicsAngularComponentStyle["Sass"] = "sass";
    SchematicsAngularComponentStyle["Scss"] = "scss";
})(SchematicsAngularComponentStyle || (exports.SchematicsAngularComponentStyle = SchematicsAngularComponentStyle = {}));
var Implement;
(function (Implement) {
    Implement["CanActivate"] = "CanActivate";
    Implement["CanActivateChild"] = "CanActivateChild";
    Implement["CanDeactivate"] = "CanDeactivate";
    Implement["CanMatch"] = "CanMatch";
})(Implement || (exports.Implement = Implement = {}));
