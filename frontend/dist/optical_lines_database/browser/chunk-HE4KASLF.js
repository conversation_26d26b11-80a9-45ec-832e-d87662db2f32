import{$ as o,B as j,Ba as Me,Bb as G,Cb as $,D as Ae,Da as ct,Db as ce,E as nt,Ea as se,Fa as dt,Ia as lt,Ib as mt,Jb as ht,Kb as de,Lb as B,Lc as Et,Mb as M,N as ie,O as st,Ob as pt,Pb as ft,Q as ne,Qb as _t,S as rt,Ub as gt,V as d,Va as z,W as m,Wb as bt,Y as _,Ya as re,a as qe,c as ee,ea as ot,f,fb as y,g as Je,gb as h,gc as Ce,hb as b,jc as vt,k as et,kb as W,lb as we,mc as Y,na as O,o as F,p as tt,qb as ut,qc as yt,ra as C,rb as xe,rc as It,s as it,sa as g,sb as oe,ub as T,va as D,wc as I,x as te,xa as K,xb as ae,ya as at,za as De}from"./chunk-BS5MTC5G.js";import{a as u}from"./chunk-C6Q5SG76.js";var ke;try{ke=typeof Intl<"u"&&Intl.v8BreakIterator}catch{ke=!1}var E=(()=>{class t{_platformId=o(ct);isBrowser=this._platformId?Et(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||ke)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var U,At=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function Cn(){if(U)return U;if(typeof document!="object"||!document)return U=new Set(At),U;let t=document.createElement("input");return U=new Set(At.filter(n=>(t.setAttribute("type",n),t.type===n))),U}var Z;function Qt(){if(Z==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>Z=!0}))}finally{Z=Z||!1}return Z}function Q(t){return Qt()?t:!!t.capture}var X=function(t){return t[t.NORMAL=0]="NORMAL",t[t.NEGATED=1]="NEGATED",t[t.INVERTED=2]="INVERTED",t}(X||{}),le,N;function Tn(){if(N==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return N=!1,N;if("scrollBehavior"in document.documentElement.style)N=!0;else{let t=Element.prototype.scrollTo;t?N=!/\{\s*\[native code\]\s*\}/.test(t.toString()):N=!1}}return N}function kn(){if(typeof document!="object"||!document)return X.NORMAL;if(le==null){let t=document.createElement("div"),n=t.style;t.dir="rtl",n.width="1px",n.overflow="auto",n.visibility="hidden",n.pointerEvents="none",n.position="absolute";let e=document.createElement("div"),i=e.style;i.width="2px",i.height="1px",t.appendChild(e),document.body.appendChild(t),le=X.NORMAL,t.scrollLeft===0&&(t.scrollLeft=1,le=t.scrollLeft===0?X.NEGATED:X.INVERTED),t.remove()}return le}var Te;function qt(){if(Te==null){let t=typeof document<"u"?document.head:null;Te=!!(t&&(t.createShadowRoot||t.attachShadow))}return Te}function Dt(t){if(qt()){let n=t.getRootNode?t.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&n instanceof ShadowRoot)return n}return null}function Jt(){let t=typeof document<"u"&&document?document.activeElement:null;for(;t&&t.shadowRoot;){let n=t.shadowRoot.activeElement;if(n===t)break;t=n}return t}function w(t){return t.composedPath?t.composedPath()[0]:t.target}function Fn(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}function V(t,n,e,i,s){let r=parseInt(Ce.major),a=parseInt(Ce.minor);return r>19||r===19&&a>0||r===0&&a===0?t.listen(n,e,i,s):(n.addEventListener(e,i,s),()=>{n.removeEventListener(e,i,s)})}var ue=new WeakMap,H=(()=>{class t{_appRef;_injector=o(O);_environmentInjector=o(ot);load(e){let i=this._appRef=this._appRef||this._injector.get(ut),s=ue.get(i);s||(s={loaders:new Set,refs:[]},ue.set(i,s),i.onDestroy(()=>{ue.get(i)?.refs.forEach(r=>r.destroy()),ue.delete(i)})),s.loaders.has(e)||(s.loaders.add(e),s.refs.push(It(e,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),q=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(i,s){},styles:[".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}"],encapsulation:2,changeDetection:0})}return t})();function me(t,...n){return n.length?n.some(e=>t[e]):t.altKey||t.shiftKey||t.ctrlKey||t.metaKey}function Vn(t){return t!=null&&`${t}`!="false"}function Mt(t,n=0){return ei(t)?Number(t):arguments.length===2?n:0}function ei(t){return!isNaN(parseFloat(t))&&!isNaN(Number(t))}function Fe(t){return Array.isArray(t)?t:[t]}function Hn(t){return t==null?"":typeof t=="string"?t:`${t}px`}function x(t){return t instanceof D?t.nativeElement:t}function ti(t){if(t.type==="characterData"&&t.target instanceof Comment)return!0;if(t.type==="childList"){for(let n=0;n<t.addedNodes.length;n++)if(!(t.addedNodes[n]instanceof Comment))return!1;for(let n=0;n<t.removedNodes.length;n++)if(!(t.removedNodes[n]instanceof Comment))return!1;return!0}return!1}var wt=(()=>{class t{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),xt=(()=>{class t{_mutationObserverFactory=o(wt);_observedElements=new Map;_ngZone=o(g);constructor(){}ngOnDestroy(){this._observedElements.forEach((e,i)=>this._cleanupObserver(i))}observe(e){let i=x(e);return new ee(s=>{let a=this._observeElement(i).pipe(F(c=>c.filter(p=>!ti(p))),te(c=>!!c.length)).subscribe(c=>{this._ngZone.run(()=>{s.next(c)})});return()=>{a.unsubscribe(),this._unobserveElement(i)}})}_observeElement(e){return this._ngZone.runOutsideAngular(()=>{if(this._observedElements.has(e))this._observedElements.get(e).count++;else{let i=new f,s=this._mutationObserverFactory.create(r=>i.next(r));s&&s.observe(e,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(e,{observer:s,stream:i,count:1})}return this._observedElements.get(e).stream})}_unobserveElement(e){this._observedElements.has(e)&&(this._observedElements.get(e).count--,this._observedElements.get(e).count||this._cleanupObserver(e))}_cleanupObserver(e){if(this._observedElements.has(e)){let{observer:i,stream:s}=this._observedElements.get(e);i&&i.disconnect(),s.complete(),this._observedElements.delete(e)}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Jn=(()=>{class t{_contentObserver=o(xt);_elementRef=o(D);event=new C;get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._disabled?this._unsubscribe():this._subscribe()}_disabled=!1;get debounce(){return this._debounce}set debounce(e){this._debounce=Mt(e),this._subscribe()}_debounce;_currentSubscription=null;constructor(){}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let e=this._contentObserver.observe(this._elementRef);this._currentSubscription=(this.debounce?e.pipe(j(this.debounce)):e).subscribe(this.event)}_unsubscribe(){this._currentSubscription?.unsubscribe()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[2,"cdkObserveContentDisabled","disabled",Y],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"],features:[W]})}return t})(),Ct=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({providers:[wt]})}return t})();var Tt=new Set,L,ii=(()=>{class t{_platform=o(E);_nonce=o(dt,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):si}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&ni(e,this._nonce),this._matchMedia(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function ni(t,n){if(!Tt.has(t))try{L||(L=document.createElement("style"),n&&L.setAttribute("nonce",n),L.setAttribute("type","text/css"),document.head.appendChild(L)),L.sheet&&(L.sheet.insertRule(`@media ${t} {body{ }}`,0),Tt.add(t))}catch(e){console.error(e)}}function si(t){return{matches:t==="all"||t==="",media:t,addListener:()=>{},removeListener:()=>{}}}var Ft=(()=>{class t{_mediaMatcher=o(ii);_zone=o(g);_queries=new Map;_destroySubject=new f;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return kt(Fe(e)).some(s=>this._registerQuery(s).mql.matches)}observe(e){let s=kt(Fe(e)).map(a=>this._registerQuery(a).observable),r=tt(s);return r=it(r.pipe(Ae(1)),r.pipe(ie(1),j(0))),r.pipe(F(a=>{let c={matches:!1,breakpoints:{}};return a.forEach(({matches:p,query:A})=>{c.matches=c.matches||p,c.breakpoints[A]=p}),c}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);let i=this._mediaMatcher.matchMedia(e),r={observable:new ee(a=>{let c=p=>this._zone.run(()=>a.next(p));return i.addListener(c),()=>{i.removeListener(c)}}).pipe(st(i),F(({matches:a})=>({query:e,matches:a})),ne(this._destroySubject)),mql:i};return this._queries.set(e,r),r}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function kt(t){return t.map(n=>n.split(",")).reduce((n,e)=>n.concat(e)).map(n=>n.trim())}var jt=" ";function Ai(t,n,e){let i=_e(t,n);e=e.trim(),!i.some(s=>s.trim()===e)&&(i.push(e),t.setAttribute(n,i.join(jt)))}function Di(t,n,e){let i=_e(t,n);e=e.trim();let s=i.filter(r=>r!==e);s.length?t.setAttribute(n,s.join(jt)):t.removeAttribute(n)}function _e(t,n){return t.getAttribute(n)?.match(/\S+/g)??[]}var Bt="cdk-describedby-message",he="cdk-describedby-host",Se=0,ws=(()=>{class t{_platform=o(E);_document=o(I);_messageRegistry=new Map;_messagesContainer=null;_id=`${Se++}`;constructor(){o(H).load(q),this._id=o(Me)+"-"+Se++}describe(e,i,s){if(!this._canBeDescribed(e,i))return;let r=Oe(i,s);typeof i!="string"?(Ot(i,this._id),this._messageRegistry.set(r,{messageElement:i,referenceCount:0})):this._messageRegistry.has(r)||this._createMessageElement(i,s),this._isElementDescribedByMessage(e,r)||this._addMessageReference(e,r)}removeDescription(e,i,s){if(!i||!this._isElementNode(e))return;let r=Oe(i,s);if(this._isElementDescribedByMessage(e,r)&&this._removeMessageReference(e,r),typeof i=="string"){let a=this._messageRegistry.get(r);a&&a.referenceCount===0&&this._deleteMessageElement(r)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let e=this._document.querySelectorAll(`[${he}="${this._id}"]`);for(let i=0;i<e.length;i++)this._removeCdkDescribedByReferenceIds(e[i]),e[i].removeAttribute(he);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(e,i){let s=this._document.createElement("div");Ot(s,this._id),s.textContent=e,i&&s.setAttribute("role",i),this._createMessagesContainer(),this._messagesContainer.appendChild(s),this._messageRegistry.set(Oe(e,i),{messageElement:s,referenceCount:0})}_deleteMessageElement(e){this._messageRegistry.get(e)?.messageElement?.remove(),this._messageRegistry.delete(e)}_createMessagesContainer(){if(this._messagesContainer)return;let e="cdk-describedby-message-container",i=this._document.querySelectorAll(`.${e}[platform="server"]`);for(let r=0;r<i.length;r++)i[r].remove();let s=this._document.createElement("div");s.style.visibility="hidden",s.classList.add(e),s.classList.add("cdk-visually-hidden"),this._platform.isBrowser||s.setAttribute("platform","server"),this._document.body.appendChild(s),this._messagesContainer=s}_removeCdkDescribedByReferenceIds(e){let i=_e(e,"aria-describedby").filter(s=>s.indexOf(Bt)!=0);e.setAttribute("aria-describedby",i.join(" "))}_addMessageReference(e,i){let s=this._messageRegistry.get(i);Ai(e,"aria-describedby",s.messageElement.id),e.setAttribute(he,this._id),s.referenceCount++}_removeMessageReference(e,i){let s=this._messageRegistry.get(i);s.referenceCount--,Di(e,"aria-describedby",s.messageElement.id),e.removeAttribute(he)}_isElementDescribedByMessage(e,i){let s=_e(e,"aria-describedby"),r=this._messageRegistry.get(i),a=r&&r.messageElement.id;return!!a&&s.indexOf(a)!=-1}_canBeDescribed(e,i){if(!this._isElementNode(e))return!1;if(i&&typeof i=="object")return!0;let s=i==null?"":`${i}`.trim(),r=e.getAttribute("aria-label");return s?!r||r.trim()!==s:!1}_isElementNode(e){return e.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Oe(t,n){return typeof t=="string"?`${n||""}/${t}`:t}function Ot(t,n){t.id||(t.id=`${Bt}-${n}-${Se++}`)}var Mi=200,Pe=class{_letterKeyStream=new f;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new f;selectedItem=this._selectedItem;constructor(n,e){let i=typeof e?.debounceInterval=="number"?e.debounceInterval:Mi;e?.skipPredicate&&(this._skipPredicateFn=e.skipPredicate),this.setItems(n),this._setupKeyHandler(i)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(n){this._selectedItemIndex=n}setItems(n){this._items=n}handleKey(n){let e=n.keyCode;n.key&&n.key.length===1?this._letterKeyStream.next(n.key.toLocaleUpperCase()):(e>=65&&e<=90||e>=48&&e<=57)&&this._letterKeyStream.next(String.fromCharCode(e))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(n){this._letterKeyStream.pipe(rt(e=>this._pressedLetters.push(e)),j(n),te(()=>this._pressedLetters.length>0),F(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(e=>{for(let i=1;i<this._items.length+1;i++){let s=(this._selectedItemIndex+i)%this._items.length,r=this._items[s];if(!this._skipPredicateFn?.(r)&&r.getLabel?.().toLocaleUpperCase().trim().indexOf(e)===0){this._selectedItem.next(r);break}}this._pressedLetters=[]})}},ge=class{_items;_activeItemIndex=-1;_activeItem=at(null);_wrap=!1;_typeaheadSubscription=qe.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=n=>n.disabled;constructor(n,e){this._items=n,n instanceof De?this._itemChangesSubscription=n.changes.subscribe(i=>this._itemsChanged(i.toArray())):K(n)&&(this._effectRef=yt(()=>this._itemsChanged(n()),{injector:e}))}tabOut=new f;change=new f;skipPredicate(n){return this._skipPredicateFn=n,this}withWrap(n=!0){return this._wrap=n,this}withVerticalOrientation(n=!0){return this._vertical=n,this}withHorizontalOrientation(n){return this._horizontal=n,this}withAllowedModifierKeys(n){return this._allowedModifierKeys=n,this}withTypeAhead(n=200){this._typeaheadSubscription.unsubscribe();let e=this._getItemsArray();return this._typeahead=new Pe(e,{debounceInterval:typeof n=="number"?n:void 0,skipPredicate:i=>this._skipPredicateFn(i)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(i=>{this.setActiveItem(i)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(n=!0){return this._homeAndEnd=n,this}withPageUpDown(n=!0,e=10){return this._pageUpAndDown={enabled:n,delta:e},this}setActiveItem(n){let e=this._activeItem();this.updateActiveItem(n),this._activeItem()!==e&&this.change.next(this._activeItemIndex)}onKeydown(n){let e=n.keyCode,s=["altKey","ctrlKey","metaKey","shiftKey"].every(r=>!n[r]||this._allowedModifierKeys.indexOf(r)>-1);switch(e){case 9:this.tabOut.next();return;case 40:if(this._vertical&&s){this.setNextItemActive();break}else return;case 38:if(this._vertical&&s){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&s){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&s){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&s){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&s){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&s){let r=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(r>0?r:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&s){let r=this._activeItemIndex+this._pageUpAndDown.delta,a=this._getItemsArray().length;this._setActiveItemByIndex(r<a?r:a-1,-1);break}else return;default:(s||me(n,"shiftKey"))&&this._typeahead?.handleKey(n);return}this._typeahead?.reset(),n.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(n){let e=this._getItemsArray(),i=typeof n=="number"?n:e.indexOf(n),s=e[i];this._activeItem.set(s??null),this._activeItemIndex=i,this._typeahead?.setCurrentSelectedItemIndex(i)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(n){this._wrap?this._setActiveInWrapMode(n):this._setActiveInDefaultMode(n)}_setActiveInWrapMode(n){let e=this._getItemsArray();for(let i=1;i<=e.length;i++){let s=(this._activeItemIndex+n*i+e.length)%e.length,r=e[s];if(!this._skipPredicateFn(r)){this.setActiveItem(s);return}}}_setActiveInDefaultMode(n){this._setActiveItemByIndex(this._activeItemIndex+n,n)}_setActiveItemByIndex(n,e){let i=this._getItemsArray();if(i[n]){for(;this._skipPredicateFn(i[n]);)if(n+=e,!i[n])return;this.setActiveItem(n)}}_getItemsArray(){return K(this._items)?this._items():this._items instanceof De?this._items.toArray():this._items}_itemsChanged(n){this._typeahead?.setItems(n);let e=this._activeItem();if(e){let i=n.indexOf(e);i>-1&&i!==this._activeItemIndex&&(this._activeItemIndex=i,this._typeahead?.setCurrentSelectedItemIndex(i))}}},Nt=class extends ge{setActiveItem(n){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(n),this.activeItem&&this.activeItem.setActiveStyles()}},Rt=class extends ge{_origin="program";setFocusOrigin(n){return this._origin=n,this}setActiveItem(n){super.setActiveItem(n),this.activeItem&&this.activeItem.focus(this._origin)}};var wi=(()=>{class t{_platform=o(E);constructor(){}isDisabled(e){return e.hasAttribute("disabled")}isVisible(e){return Ci(e)&&getComputedStyle(e).visibility==="visible"}isTabbable(e){if(!this._platform.isBrowser)return!1;let i=xi(Si(e));if(i&&(Lt(i)===-1||!this.isVisible(i)))return!1;let s=e.nodeName.toLowerCase(),r=Lt(e);return e.hasAttribute("contenteditable")?r!==-1:s==="iframe"||s==="object"||this._platform.WEBKIT&&this._platform.IOS&&!Ri(e)?!1:s==="audio"?e.hasAttribute("controls")?r!==-1:!1:s==="video"?r===-1?!1:r!==null?!0:this._platform.FIREFOX||e.hasAttribute("controls"):e.tabIndex>=0}isFocusable(e,i){return Li(e)&&!this.isDisabled(e)&&(i?.ignoreVisibility||this.isVisible(e))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function xi(t){try{return t.frameElement}catch{return null}}function Ci(t){return!!(t.offsetWidth||t.offsetHeight||typeof t.getClientRects=="function"&&t.getClientRects().length)}function Ti(t){let n=t.nodeName.toLowerCase();return n==="input"||n==="select"||n==="button"||n==="textarea"}function ki(t){return Oi(t)&&t.type=="hidden"}function Fi(t){return Ni(t)&&t.hasAttribute("href")}function Oi(t){return t.nodeName.toLowerCase()=="input"}function Ni(t){return t.nodeName.toLowerCase()=="a"}function Ut(t){if(!t.hasAttribute("tabindex")||t.tabIndex===void 0)return!1;let n=t.getAttribute("tabindex");return!!(n&&!isNaN(parseInt(n,10)))}function Lt(t){if(!Ut(t))return null;let n=parseInt(t.getAttribute("tabindex")||"",10);return isNaN(n)?-1:n}function Ri(t){let n=t.nodeName.toLowerCase(),e=n==="input"&&t.type;return e==="text"||e==="password"||n==="select"||n==="textarea"}function Li(t){return ki(t)?!1:Ti(t)||Fi(t)||t.hasAttribute("contenteditable")||Ut(t)}function Si(t){return t.ownerDocument&&t.ownerDocument.defaultView||window}var je=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(n){this._enabled=n,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(n,this._startAnchor),this._toggleAnchorTabIndex(n,this._endAnchor))}_enabled=!0;constructor(n,e,i,s,r=!1,a){this._element=n,this._checker=e,this._ngZone=i,this._document=s,this._injector=a,r||this.attachAnchors()}destroy(){let n=this._startAnchor,e=this._endAnchor;n&&(n.removeEventListener("focus",this.startAnchorListener),n.remove()),e&&(e.removeEventListener("focus",this.endAnchorListener),e.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusInitialElement(n)))})}focusFirstTabbableElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusFirstTabbableElement(n)))})}focusLastTabbableElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusLastTabbableElement(n)))})}_getRegionBoundary(n){let e=this._element.querySelectorAll(`[cdk-focus-region-${n}], [cdkFocusRegion${n}], [cdk-focus-${n}]`);return n=="start"?e.length?e[0]:this._getFirstTabbableElement(this._element):e.length?e[e.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(n){let e=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(e){if(!this._checker.isFocusable(e)){let i=this._getFirstTabbableElement(e);return i?.focus(n),!!i}return e.focus(n),!0}return this.focusFirstTabbableElement(n)}focusFirstTabbableElement(n){let e=this._getRegionBoundary("start");return e&&e.focus(n),!!e}focusLastTabbableElement(n){let e=this._getRegionBoundary("end");return e&&e.focus(n),!!e}hasAttached(){return this._hasAttached}_getFirstTabbableElement(n){if(this._checker.isFocusable(n)&&this._checker.isTabbable(n))return n;let e=n.children;for(let i=0;i<e.length;i++){let s=e[i].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(e[i]):null;if(s)return s}return null}_getLastTabbableElement(n){if(this._checker.isFocusable(n)&&this._checker.isTabbable(n))return n;let e=n.children;for(let i=e.length-1;i>=0;i--){let s=e[i].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(e[i]):null;if(s)return s}return null}_createAnchor(){let n=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,n),n.classList.add("cdk-visually-hidden"),n.classList.add("cdk-focus-trap-anchor"),n.setAttribute("aria-hidden","true"),n}_toggleAnchorTabIndex(n,e){n?e.setAttribute("tabindex","0"):e.removeAttribute("tabindex")}toggleAnchors(n){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(n,this._startAnchor),this._toggleAnchorTabIndex(n,this._endAnchor))}_executeOnStable(n){this._injector?lt(n,{injector:this._injector}):setTimeout(n)}},xs=(()=>{class t{_checker=o(wi);_ngZone=o(g);_document=o(I);_injector=o(O);constructor(){o(H).load(q)}create(e,i=!1){return new je(e,this._checker,this._ngZone,this._document,i,this._injector)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Be(t){return t.buttons===0||t.detail===0}function Ue(t){let n=t.touches&&t.touches[0]||t.changedTouches&&t.changedTouches[0];return!!n&&n.identifier===-1&&(n.radiusX==null||n.radiusX===1)&&(n.radiusY==null||n.radiusY===1)}var Pi=new _("cdk-input-modality-detector-options"),ji={ignoreKeys:[18,17,224,91,16]},Vt=650,Ne={passive:!0,capture:!0},Bi=(()=>{class t{_platform=o(E);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new Je(null);_options;_lastTouchMs=0;_onKeydown=e=>{this._options?.ignoreKeys?.some(i=>i===e.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=w(e))};_onMousedown=e=>{Date.now()-this._lastTouchMs<Vt||(this._modality.next(Be(e)?"keyboard":"mouse"),this._mostRecentTarget=w(e))};_onTouchstart=e=>{if(Ue(e)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=w(e)};constructor(){let e=o(g),i=o(I),s=o(Pi,{optional:!0});if(this._options=u(u({},ji),s),this.modalityDetected=this._modality.pipe(ie(1)),this.modalityChanged=this.modalityDetected.pipe(nt()),this._platform.isBrowser){let r=o(re).createRenderer(null,null);this._listenerCleanups=e.runOutsideAngular(()=>[V(r,i,"keydown",this._onKeydown,Ne),V(r,i,"mousedown",this._onMousedown,Ne),V(r,i,"touchstart",this._onTouchstart,Ne)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(e=>e())}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ui=new _("liveAnnouncerElement",{providedIn:"root",factory:Vi});function Vi(){return null}var Hi=new _("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),Ki=0,Cs=(()=>{class t{_ngZone=o(g);_defaultOptions=o(Hi,{optional:!0});_liveElement;_document=o(I);_previousTimeout;_currentPromise;_currentResolve;constructor(){let e=o(Ui,{optional:!0});this._liveElement=e||this._createLiveElement()}announce(e,...i){let s=this._defaultOptions,r,a;return i.length===1&&typeof i[0]=="number"?a=i[0]:[r,a]=i,this.clear(),clearTimeout(this._previousTimeout),r||(r=s&&s.politeness?s.politeness:"polite"),a==null&&s&&(a=s.duration),this._liveElement.setAttribute("aria-live",r),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(c=>this._currentResolve=c)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=e,typeof a=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),a)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let e="cdk-live-announcer-element",i=this._document.getElementsByClassName(e),s=this._document.createElement("div");for(let r=0;r<i.length;r++)i[r].remove();return s.classList.add(e),s.classList.add("cdk-visually-hidden"),s.setAttribute("aria-atomic","true"),s.setAttribute("aria-live","polite"),s.id=`cdk-live-announcer-${Ki++}`,this._document.body.appendChild(s),s}_exposeAnnouncerToModals(e){let i=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let s=0;s<i.length;s++){let r=i[s],a=r.getAttribute("aria-owns");a?a.indexOf(e)===-1&&r.setAttribute("aria-owns",a+" "+e):r.setAttribute("aria-owns",e)}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var fe=function(t){return t[t.IMMEDIATE=0]="IMMEDIATE",t[t.EVENTUAL=1]="EVENTUAL",t}(fe||{}),zi=new _("cdk-focus-monitor-default-options"),pe=Q({passive:!0,capture:!0}),Wi=(()=>{class t{_ngZone=o(g);_platform=o(E);_inputModalityDetector=o(Bi);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=o(I,{optional:!0});_stopInputModalityDetector=new f;constructor(){let e=o(zi,{optional:!0});this._detectionMode=e?.detectionMode||fe.IMMEDIATE}_rootNodeFocusAndBlurListener=e=>{let i=w(e);for(let s=i;s;s=s.parentElement)e.type==="focus"?this._onFocus(e,s):this._onBlur(e,s)};monitor(e,i=!1){let s=x(e);if(!this._platform.isBrowser||s.nodeType!==1)return et();let r=Dt(s)||this._getDocument(),a=this._elementInfo.get(s);if(a)return i&&(a.checkChildren=!0),a.subject;let c={checkChildren:i,subject:new f,rootNode:r};return this._elementInfo.set(s,c),this._registerGlobalListeners(c),c.subject}stopMonitoring(e){let i=x(e),s=this._elementInfo.get(i);s&&(s.subject.complete(),this._setClasses(i),this._elementInfo.delete(i),this._removeGlobalListeners(s))}focusVia(e,i,s){let r=x(e),a=this._getDocument().activeElement;r===a?this._getClosestElementsInfo(r).forEach(([c,p])=>this._originChanged(c,i,p)):(this._setOrigin(i),typeof r.focus=="function"&&r.focus(s))}ngOnDestroy(){this._elementInfo.forEach((e,i)=>this.stopMonitoring(i))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===fe.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,i){e.classList.toggle("cdk-focused",!!i),e.classList.toggle("cdk-touch-focused",i==="touch"),e.classList.toggle("cdk-keyboard-focused",i==="keyboard"),e.classList.toggle("cdk-mouse-focused",i==="mouse"),e.classList.toggle("cdk-program-focused",i==="program")}_setOrigin(e,i=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=e,this._originFromTouchInteraction=e==="touch"&&i,this._detectionMode===fe.IMMEDIATE){clearTimeout(this._originTimeoutId);let s=this._originFromTouchInteraction?Vt:1;this._originTimeoutId=setTimeout(()=>this._origin=null,s)}})}_onFocus(e,i){let s=this._elementInfo.get(i),r=w(e);!s||!s.checkChildren&&i!==r||this._originChanged(i,this._getFocusOrigin(r),s)}_onBlur(e,i){let s=this._elementInfo.get(i);!s||s.checkChildren&&e.relatedTarget instanceof Node&&i.contains(e.relatedTarget)||(this._setClasses(i),this._emitOrigin(s,null))}_emitOrigin(e,i){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(i))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;let i=e.rootNode,s=this._rootNodeFocusListenerCount.get(i)||0;s||this._ngZone.runOutsideAngular(()=>{i.addEventListener("focus",this._rootNodeFocusAndBlurListener,pe),i.addEventListener("blur",this._rootNodeFocusAndBlurListener,pe)}),this._rootNodeFocusListenerCount.set(i,s+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(ne(this._stopInputModalityDetector)).subscribe(r=>{this._setOrigin(r,!0)}))}_removeGlobalListeners(e){let i=e.rootNode;if(this._rootNodeFocusListenerCount.has(i)){let s=this._rootNodeFocusListenerCount.get(i);s>1?this._rootNodeFocusListenerCount.set(i,s-1):(i.removeEventListener("focus",this._rootNodeFocusAndBlurListener,pe),i.removeEventListener("blur",this._rootNodeFocusAndBlurListener,pe),this._rootNodeFocusListenerCount.delete(i))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,i,s){this._setClasses(e,i),this._emitOrigin(s,i),this._lastFocusOrigin=i}_getClosestElementsInfo(e){let i=[];return this._elementInfo.forEach((s,r)=>{(r===e||s.checkChildren&&r.contains(e))&&i.push([r,s])}),i}_isLastInteractionFromInputLabel(e){let{_mostRecentTarget:i,mostRecentModality:s}=this._inputModalityDetector;if(s!=="mouse"||!i||i===e||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA"||e.disabled)return!1;let r=e.labels;if(r){for(let a=0;a<r.length;a++)if(r[a].contains(i))return!0}return!1}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ts=(()=>{class t{_elementRef=o(D);_focusMonitor=o(Wi);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new C;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let e=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(e,e.nodeType===1&&e.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(i=>{this._focusOrigin=i,this.cdkFocusChange.emit(i)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return t})(),S=function(t){return t[t.NONE=0]="NONE",t[t.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",t[t.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",t}(S||{}),St="cdk-high-contrast-black-on-white",Pt="cdk-high-contrast-white-on-black",Re="cdk-high-contrast-active",Ve=(()=>{class t{_platform=o(E);_hasCheckedHighContrastMode;_document=o(I);_breakpointSubscription;constructor(){this._breakpointSubscription=o(Ft).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return S.NONE;let e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);let i=this._document.defaultView||window,s=i&&i.getComputedStyle?i.getComputedStyle(e):null,r=(s&&s.backgroundColor||"").replace(/ /g,"");switch(e.remove(),r){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return S.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return S.BLACK_ON_WHITE}return S.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let e=this._document.body.classList;e.remove(Re,St,Pt),this._hasCheckedHighContrastMode=!0;let i=this.getHighContrastMode();i===S.BLACK_ON_WHITE?e.add(Re,St):i===S.WHITE_ON_BLACK&&e.add(Re,Pt)}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ks=(()=>{class t{constructor(){o(Ve)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[Ct]})}return t})(),Le={},Ht=(()=>{class t{_appId=o(Me);getId(e){return this._appId!=="ng"&&(e+=this._appId),Le.hasOwnProperty(e)||(Le[e]=0),`${e}${Le[e]++}`}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var $i=new _("cdk-dir-doc",{providedIn:"root",factory:Yi});function Yi(){return o(I)}var Zi=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function Xi(t){let n=t?.toLowerCase()||"";return n==="auto"&&typeof navigator<"u"&&navigator?.language?Zi.test(navigator.language)?"rtl":"ltr":n==="rtl"?"rtl":"ltr"}var Us=(()=>{class t{value="ltr";change=new C;constructor(){let e=o($i,{optional:!0});if(e){let i=e.body?e.body.dir:null,s=e.documentElement?e.documentElement.dir:null;this.value=Xi(i||s||"ltr")}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var He=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({})}return t})();var Ji=["text"],en=[[["mat-icon"]],"*"],tn=["mat-icon","*"];function nn(t,n){if(t&1&&ce(0,"mat-pseudo-checkbox",1),t&2){let e=de();oe("disabled",e.disabled)("state",e.selected?"checked":"unchecked")}}function sn(t,n){if(t&1&&ce(0,"mat-pseudo-checkbox",3),t&2){let e=de();oe("disabled",e.disabled)}}function rn(t,n){if(t&1&&(G(0,"span",4),gt(1),$()),t&2){let e=de();z(),bt("(",e.group.label,")")}}var on=["mat-internal-form-field",""],an=["*"];var P=(()=>{class t{constructor(){o(Ve)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[He,He]})}return t})(),Kt=class{_defaultMatcher;ngControl;_parentFormGroup;_parentForm;_stateChanges;errorState=!1;matcher;constructor(n,e,i,s,r){this._defaultMatcher=n,this.ngControl=e,this._parentFormGroup=i,this._parentForm=s,this._stateChanges=r}updateErrorState(){let n=this.errorState,e=this._parentFormGroup||this._parentForm,i=this.matcher||this._defaultMatcher,s=this.ngControl?this.ngControl.control:null,r=i?.isErrorState(s,e)??!1;r!==n&&(this.errorState=r,this._stateChanges.next())}};var dr=(()=>{class t{isErrorState(e,i){return!!(e&&e.invalid&&(e.touched||i&&i.submitted))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),cn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["structural-styles"]],decls:0,vars:0,template:function(i,s){},styles:['.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}'],encapsulation:2,changeDetection:0})}return t})();var v=function(t){return t[t.FADING_IN=0]="FADING_IN",t[t.VISIBLE=1]="VISIBLE",t[t.FADING_OUT=2]="FADING_OUT",t[t.HIDDEN=3]="HIDDEN",t}(v||{}),We=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=v.HIDDEN;constructor(n,e,i,s=!1){this._renderer=n,this.element=e,this.config=i,this._animationForciblyDisabledThroughCss=s}fadeOut(){this._renderer.fadeOutRipple(this)}},zt=Q({passive:!0,capture:!0}),Ge=class{_events=new Map;addHandler(n,e,i,s){let r=this._events.get(e);if(r){let a=r.get(i);a?a.add(s):r.set(i,new Set([s]))}else this._events.set(e,new Map([[i,new Set([s])]])),n.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,zt)})}removeHandler(n,e,i){let s=this._events.get(n);if(!s)return;let r=s.get(e);r&&(r.delete(i),r.size===0&&s.delete(e),s.size===0&&(this._events.delete(n),document.removeEventListener(n,this._delegateEventHandler,zt)))}_delegateEventHandler=n=>{let e=w(n);e&&this._events.get(n.type)?.forEach((i,s)=>{(s===e||s.contains(e))&&i.forEach(r=>r.handleEvent(n))})}},ve={enterDuration:225,exitDuration:150},dn=800,Wt=Q({passive:!0,capture:!0}),Gt=["mousedown","touchstart"],$t=["mouseup","mouseleave","touchend","touchcancel"],ln=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(i,s){},styles:[".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}"],encapsulation:2,changeDetection:0})}return t})(),ye=class t{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new Ge;constructor(n,e,i,s,r){this._target=n,this._ngZone=e,this._platform=s,s.isBrowser&&(this._containerElement=x(i)),r&&r.get(H).load(ln)}fadeInRipple(n,e,i={}){let s=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),r=u(u({},ve),i.animation);i.centered&&(n=s.left+s.width/2,e=s.top+s.height/2);let a=i.radius||un(n,e,s),c=n-s.left,p=e-s.top,A=r.enterDuration,l=document.createElement("div");l.classList.add("mat-ripple-element"),l.style.left=`${c-a}px`,l.style.top=`${p-a}px`,l.style.height=`${a*2}px`,l.style.width=`${a*2}px`,i.color!=null&&(l.style.backgroundColor=i.color),l.style.transitionDuration=`${A}ms`,this._containerElement.appendChild(l);let Ye=window.getComputedStyle(l),Xt=Ye.transitionProperty,Ze=Ye.transitionDuration,Ie=Xt==="none"||Ze==="0s"||Ze==="0s, 0s"||s.width===0&&s.height===0,k=new We(this,l,i,Ie);l.style.transform="scale3d(1, 1, 1)",k.state=v.FADING_IN,i.persistent||(this._mostRecentTransientRipple=k);let J=null;return!Ie&&(A||r.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let Xe=()=>{J&&(J.fallbackTimer=null),clearTimeout(Qe),this._finishRippleTransition(k)},Ee=()=>this._destroyRipple(k),Qe=setTimeout(Ee,A+100);l.addEventListener("transitionend",Xe),l.addEventListener("transitioncancel",Ee),J={onTransitionEnd:Xe,onTransitionCancel:Ee,fallbackTimer:Qe}}),this._activeRipples.set(k,J),(Ie||!A)&&this._finishRippleTransition(k),k}fadeOutRipple(n){if(n.state===v.FADING_OUT||n.state===v.HIDDEN)return;let e=n.element,i=u(u({},ve),n.config.animation);e.style.transitionDuration=`${i.exitDuration}ms`,e.style.opacity="0",n.state=v.FADING_OUT,(n._animationForciblyDisabledThroughCss||!i.exitDuration)&&this._finishRippleTransition(n)}fadeOutAll(){this._getActiveRipples().forEach(n=>n.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(n=>{n.config.persistent||n.fadeOut()})}setupTriggerEvents(n){let e=x(n);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,Gt.forEach(i=>{t._eventManager.addHandler(this._ngZone,i,e,this)}))}handleEvent(n){n.type==="mousedown"?this._onMousedown(n):n.type==="touchstart"?this._onTouchStart(n):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{$t.forEach(e=>{this._triggerElement.addEventListener(e,this,Wt)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(n){n.state===v.FADING_IN?this._startFadeOutTransition(n):n.state===v.FADING_OUT&&this._destroyRipple(n)}_startFadeOutTransition(n){let e=n===this._mostRecentTransientRipple,{persistent:i}=n.config;n.state=v.VISIBLE,!i&&(!e||!this._isPointerDown)&&n.fadeOut()}_destroyRipple(n){let e=this._activeRipples.get(n)??null;this._activeRipples.delete(n),this._activeRipples.size||(this._containerRect=null),n===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),n.state=v.HIDDEN,e!==null&&(n.element.removeEventListener("transitionend",e.onTransitionEnd),n.element.removeEventListener("transitioncancel",e.onTransitionCancel),e.fallbackTimer!==null&&clearTimeout(e.fallbackTimer)),n.element.remove()}_onMousedown(n){let e=Be(n),i=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+dn;!this._target.rippleDisabled&&!e&&!i&&(this._isPointerDown=!0,this.fadeInRipple(n.clientX,n.clientY,this._target.rippleConfig))}_onTouchStart(n){if(!this._target.rippleDisabled&&!Ue(n)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let e=n.changedTouches;if(e)for(let i=0;i<e.length;i++)this.fadeInRipple(e[i].clientX,e[i].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(n=>{let e=n.state===v.VISIBLE||n.config.terminateOnPointerUp&&n.state===v.FADING_IN;!n.config.persistent&&e&&n.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let n=this._triggerElement;n&&(Gt.forEach(e=>t._eventManager.removeHandler(e,n,this)),this._pointerUpEventsRegistered&&($t.forEach(e=>n.removeEventListener(e,this,Wt)),this._pointerUpEventsRegistered=!1))}};function un(t,n,e){let i=Math.max(Math.abs(t-e.left),Math.abs(t-e.right)),s=Math.max(Math.abs(n-e.top),Math.abs(n-e.bottom));return Math.sqrt(i*i+s*s)}var Zt=new _("mat-ripple-global-options"),mn=(()=>{class t{_elementRef=o(D);_animationMode=o(se,{optional:!0});color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(e){e&&this.fadeOutAllNonPersistent(),this._disabled=e,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(e){this._trigger=e,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let e=o(g),i=o(E),s=o(Zt,{optional:!0}),r=o(O);this._globalOptions=s||{},this._rippleRenderer=new ye(this,e,this._elementRef,i,r)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:u(u(u({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(e,i=0,s){return typeof e=="number"?this._rippleRenderer.fadeInRipple(e,i,u(u({},this.rippleConfig),s)):this._rippleRenderer.fadeInRipple(0,0,u(u({},this.rippleConfig),e))}static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(i,s){i&2&&T("mat-ripple-unbounded",s.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return t})(),hn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[P,P]})}return t})(),pn=(()=>{class t{_animationMode=o(se,{optional:!0});state="unchecked";disabled=!1;appearance="full";constructor(){}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(i,s){i&2&&T("mat-pseudo-checkbox-indeterminate",s.state==="indeterminate")("mat-pseudo-checkbox-checked",s.state==="checked")("mat-pseudo-checkbox-disabled",s.disabled)("mat-pseudo-checkbox-minimal",s.appearance==="minimal")("mat-pseudo-checkbox-full",s.appearance==="full")("_mat-animation-noopable",s._animationMode==="NoopAnimations")},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},decls:0,vars:0,template:function(i,s){},styles:['.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}'],encapsulation:2,changeDetection:0})}return t})(),fn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[P]})}return t})(),_n=new _("MAT_OPTION_PARENT_COMPONENT"),gn=new _("MatOptgroup");var $e=class{source;isUserInput;constructor(n,e=!1){this.source=n,this.isUserInput=e}},lr=(()=>{class t{_element=o(D);_changeDetectorRef=o(vt);_parent=o(_n,{optional:!0});group=o(gn,{optional:!0});_signalDisableRipple=!1;_selected=!1;_active=!1;_disabled=!1;_mostRecentViewValue="";get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}value;id=o(Ht).getId("mat-option-");get disabled(){return this.group&&this.group.disabled||this._disabled}set disabled(e){this._disabled=e}get disableRipple(){return this._signalDisableRipple?this._parent.disableRipple():!!this._parent?.disableRipple}get hideSingleSelectionIndicator(){return!!(this._parent&&this._parent.hideSingleSelectionIndicator)}onSelectionChange=new C;_text;_stateChanges=new f;constructor(){let e=o(H);e.load(cn),e.load(q),this._signalDisableRipple=!!this._parent&&K(this._parent.disableRipple)}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(e=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}deselect(e=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}focus(e,i){let s=this._getHostElement();typeof s.focus=="function"&&s.focus(i)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(e){(e.keyCode===13||e.keyCode===32)&&!me(e)&&(this._selectViaInteraction(),e.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=this.multiple?!this._selected:!0,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){let e=this.viewValue;e!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=e)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(e=!1){this.onSelectionChange.emit(new $e(this,e))}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["mat-option"]],viewQuery:function(i,s){if(i&1&&pt(Ji,7),i&2){let r;ft(r=_t())&&(s._text=r.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(i,s){i&1&&ht("click",function(){return s._selectViaInteraction()})("keydown",function(a){return s._handleKeydown(a)}),i&2&&(mt("id",s.id),xe("aria-selected",s.selected)("aria-disabled",s.disabled.toString()),T("mdc-list-item--selected",s.selected)("mat-mdc-option-multiple",s.multiple)("mat-mdc-option-active",s.active)("mdc-list-item--disabled",s.disabled))},inputs:{value:"value",id:"id",disabled:[2,"disabled","disabled",Y]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],features:[W],ngContentSelectors:tn,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(i,s){i&1&&(B(en),we(0,nn,1,2,"mat-pseudo-checkbox",1),M(1),G(2,"span",2,0),M(4,1),$(),we(5,sn,1,1,"mat-pseudo-checkbox",3)(6,rn,2,1,"span",4),ce(7,"div",5)),i&2&&(ae(s.multiple?0:-1),z(5),ae(!s.multiple&&s.selected&&!s.hideSingleSelectionIndicator?5:-1),z(),ae(s.group&&s.group._inert?6:-1),z(),oe("matRippleTrigger",s._getHostElement())("matRippleDisabled",s.disabled||s.disableRipple))},dependencies:[pn,mn],styles:['.mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0})}return t})();function ur(t,n,e){if(e.length){let i=n.toArray(),s=e.toArray(),r=0;for(let a=0;a<t+1;a++)i[a].group&&i[a].group===s[r]&&r++;return r}return 0}function mr(t,n,e,i){return t<e?t:t+n>e+i?Math.max(0,t-i+n):e}var hr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[hn,P,fn]})}return t})(),bn={capture:!0},vn=["focus","mousedown","mouseenter","touchstart"],Ke="mat-ripple-loader-uninitialized",ze="mat-ripple-loader-class-name",Yt="mat-ripple-loader-centered",be="mat-ripple-loader-disabled",pr=(()=>{class t{_document=o(I);_animationMode=o(se,{optional:!0});_globalRippleOptions=o(Zt,{optional:!0});_platform=o(E);_ngZone=o(g);_injector=o(O);_eventCleanups;_hosts=new Map;constructor(){let e=o(re).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>vn.map(i=>V(e,this._document,i,this._onInteraction,bn)))}ngOnDestroy(){let e=this._hosts.keys();for(let i of e)this.destroyRipple(i);this._eventCleanups.forEach(i=>i())}configureRipple(e,i){e.setAttribute(Ke,this._globalRippleOptions?.namespace??""),(i.className||!e.hasAttribute(ze))&&e.setAttribute(ze,i.className||""),i.centered&&e.setAttribute(Yt,""),i.disabled&&e.setAttribute(be,"")}setDisabled(e,i){let s=this._hosts.get(e);s?(s.target.rippleDisabled=i,!i&&!s.hasSetUpEvents&&(s.hasSetUpEvents=!0,s.renderer.setupTriggerEvents(e))):i?e.setAttribute(be,""):e.removeAttribute(be)}_onInteraction=e=>{let i=w(e);if(i instanceof HTMLElement){let s=i.closest(`[${Ke}="${this._globalRippleOptions?.namespace??""}"]`);s&&this._createRipple(s)}};_createRipple(e){if(!this._document||this._hosts.has(e))return;e.querySelector(".mat-ripple")?.remove();let i=this._document.createElement("span");i.classList.add("mat-ripple",e.getAttribute(ze)),e.append(i);let s=this._animationMode==="NoopAnimations",r=this._globalRippleOptions,a=s?0:r?.animation?.enterDuration??ve.enterDuration,c=s?0:r?.animation?.exitDuration??ve.exitDuration,p={rippleDisabled:s||r?.disabled||e.hasAttribute(be),rippleConfig:{centered:e.hasAttribute(Yt),terminateOnPointerUp:r?.terminateOnPointerUp,animation:{enterDuration:a,exitDuration:c}}},A=new ye(p,this._ngZone,i,this._platform,this._injector),l=!p.rippleDisabled;l&&A.setupTriggerEvents(e),this._hosts.set(e,{target:p,renderer:A,hasSetUpEvents:l}),e.removeAttribute(Ke)}destroyRipple(e){let i=this._hosts.get(e);i&&(i.renderer._removeTriggerEvents(),this._hosts.delete(e))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=d({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),fr=(()=>{class t{labelPosition;static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(i,s){i&2&&T("mdc-form-field--align-end",s.labelPosition==="before")},inputs:{labelPosition:"labelPosition"},attrs:on,ngContentSelectors:an,decls:1,vars:0,template:function(i,s){i&1&&(B(),M(0))},styles:[".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}"],encapsulation:2,changeDetection:0})}return t})();var yn=["*"];var In=[[["","mat-card-avatar",""],["","matCardAvatar",""]],[["mat-card-title"],["mat-card-subtitle"],["","mat-card-title",""],["","mat-card-subtitle",""],["","matCardTitle",""],["","matCardSubtitle",""]],"*"],En=["[mat-card-avatar], [matCardAvatar]",`mat-card-title, mat-card-subtitle,
      [mat-card-title], [mat-card-subtitle],
      [matCardTitle], [matCardSubtitle]`,"*"],An=new _("MAT_CARD_CONFIG"),wr=(()=>{class t{appearance;constructor(){let e=o(An,{optional:!0});this.appearance=e?.appearance||"raised"}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(i,s){i&2&&T("mat-mdc-card-outlined",s.appearance==="outlined")("mdc-card--outlined",s.appearance==="outlined")},inputs:{appearance:"appearance"},exportAs:["matCard"],ngContentSelectors:yn,decls:1,vars:0,template:function(i,s){i&1&&(B(),M(0))},styles:['.mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:"";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}'],encapsulation:2,changeDetection:0})}return t})(),xr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["mat-card-title"],["","mat-card-title",""],["","matCardTitle",""]],hostAttrs:[1,"mat-mdc-card-title"]})}return t})();var Cr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"]})}return t})(),Tr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["mat-card-subtitle"],["","mat-card-subtitle",""],["","matCardSubtitle",""]],hostAttrs:[1,"mat-mdc-card-subtitle"]})}return t})(),kr=(()=>{class t{align="start";static \u0275fac=function(i){return new(i||t)};static \u0275dir=b({type:t,selectors:[["mat-card-actions"]],hostAttrs:[1,"mat-mdc-card-actions","mdc-card__actions"],hostVars:2,hostBindings:function(i,s){i&2&&T("mat-mdc-card-actions-align-end",s.align==="end")},inputs:{align:"align"},exportAs:["matCardActions"]})}return t})(),Fr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=y({type:t,selectors:[["mat-card-header"]],hostAttrs:[1,"mat-mdc-card-header"],ngContentSelectors:En,decls:4,vars:0,consts:[[1,"mat-mdc-card-header-text"]],template:function(i,s){i&1&&(B(In),M(0),G(1,"div",0),M(2,1),$(),M(3,2))},encapsulation:2,changeDetection:0})}return t})();var Or=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=h({type:t});static \u0275inj=m({imports:[P,P]})}return t})();export{E as a,Cn as b,Q as c,X as d,Tn as e,kn as f,Jt as g,w as h,Fn as i,V as j,H as k,q as l,me as m,Vn as n,Mt as o,ei as p,Fe as q,Hn as r,x as s,Jn as t,Ct as u,Ai as v,Di as w,ws as x,Nt as y,Rt as z,wi as A,xs as B,Be as C,Ue as D,Cs as E,Wi as F,Ts as G,ks as H,Ht as I,Us as J,He as K,P as L,Kt as M,dr as N,cn as O,Zt as P,mn as Q,hn as R,_n as S,gn as T,lr as U,ur as V,mr as W,hr as X,pr as Y,fr as Z,wr as _,xr as $,Cr as aa,Tr as ba,kr as ca,Fr as da,Or as ea};
