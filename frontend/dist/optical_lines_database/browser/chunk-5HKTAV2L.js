import{$ as d,_ as a,aa as p,da as s,ea as c}from"./chunk-HE4KASLF.js";import{Bb as t,Cb as n,Ub as o,fb as r}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var m=class i{static \u0275fac=function(e){return new(e||i)};static \u0275cmp=r({type:i,selectors:[["app-provider-points"]],decls:9,vars:0,consts:[[1,"provider-points-container"],[1,"provider-points-placeholder"]],template:function(e,f){e&1&&(t(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),o(4,"Provider Connection Points Management"),n()(),t(5,"mat-card-content")(6,"div",1)(7,"p"),o(8,"Provider connection points management will be implemented in Phase 5"),n()()()()())},dependencies:[c,a,p,s,d],styles:[".provider-points-container[_ngcontent-%COMP%]{padding:20px}.provider-points-placeholder[_ngcontent-%COMP%]{height:300px;display:flex;justify-content:center;align-items:center;background-color:#f5f5f5;border:1px solid #ddd}"]})};export{m as ProviderPointsComponent};
