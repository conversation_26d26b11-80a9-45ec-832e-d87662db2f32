import{b as t}from"./chunk-N6OW56HX.js";import"./chunk-KGIFXD27.js";import"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var n=[{path:"",loadComponent:()=>import("./chunk-ZKZ42SM6.js").then(o=>o.DashboardComponent),canActivate:[t],data:{role:"provider"}},{path:"lines",loadComponent:()=>import("./chunk-PECWAY66.js").then(o=>o.ProviderLinesComponent),canActivate:[t],data:{role:"provider"}},{path:"points",loadComponent:()=>import("./chunk-5HKTAV2L.js").then(o=>o.ProviderPointsComponent),canActivate:[t],data:{role:"provider"}},{path:"capacity",loadComponent:()=>import("./chunk-A2JL5PB2.js").then(o=>o.CapacityManagementComponent),canActivate:[t],data:{role:"provider"}}];export{n as PROVIDER_ROUTES};
