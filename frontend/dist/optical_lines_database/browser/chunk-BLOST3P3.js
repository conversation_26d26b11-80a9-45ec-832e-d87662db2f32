import{a as Y,b as Z}from"./chunk-SSOC3NBY.js";import{a as K,b as Q,d as U,g as W,h as X}from"./chunk-ORNC4OUU.js";import{b as R,d as g,f as j,g as q,j as B,k as G,l as V,o as z,q as H}from"./chunk-Q6MA6IAZ.js";import{b as $}from"./chunk-OJZDOK3T.js";import{a as D,b as N}from"./chunk-OMWDYSFJ.js";import{a as L,b as k,c as O,d as T,e as A}from"./chunk-QB7XPJNY.js";import{k as y,l as S,n as b,p as J}from"./chunk-KGIFXD27.js";import{$ as E,_ as P,aa as w,da as I,ea as F}from"./chunk-HE4KASLF.js";import{Bb as e,Cb as i,Db as s,Fc as x,Jb as d,Jc as M,Kb as C,Ub as o,Va as m,Vb as h,_a as u,fb as v,lb as c,sb as a}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";function te(n,r){if(n&1&&s(0,"app-alert",16),n&2){let l=C();a("message",l.error)}}function ie(n,r){n&1&&s(0,"app-alert",17),n&2&&a("message","")}function ne(n,r){n&1&&(e(0,"mat-error"),o(1," Email is required "),i())}function oe(n,r){n&1&&(e(0,"mat-error"),o(1," Please enter a valid email address "),i())}function re(n,r){n&1&&(e(0,"mat-error"),o(1," Password is required "),i())}function ae(n,r){n&1&&s(0,"mat-spinner",18)}function me(n,r){n&1&&(e(0,"span"),o(1,"Login"),i())}var ee=class n{constructor(r,l,t){this.fb=r;this.authService=l;this.router=t;this.loginForm=this.fb.group({email:["",[g.required,g.email]],password:["",g.required]})}loginForm;loading=!1;error="";hidePassword=!0;onSubmit(){if(this.loginForm.invalid)return;this.loading=!0,this.error="";let{email:r,password:l}=this.loginForm.value;this.authService.login({email:r,password:l}).subscribe({next:()=>{this.loading=!1,this.router.navigate(["/"])},error:t=>{this.loading=!1,this.error=t.message||"Login failed. Please try again."}})}fillDemoAccount(r,l){this.loginForm.setValue({email:r,password:l})}static \u0275fac=function(l){return new(l||n)(u(z),u(J),u(y))};static \u0275cmp=v({type:n,selectors:[["app-login"]],decls:47,vars:11,consts:[[1,"login-container"],["type","error",3,"message",4,"ngIf"],["type","success",3,"message",4,"ngIf"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","email","type","email","placeholder","Enter your email"],[4,"ngIf"],["matInput","","formControlName","password","placeholder","Enter your password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],[1,"register-link"],["routerLink","/auth/register"],[1,"demo-accounts"],[1,"demo-account",3,"click"],["type","error",3,"message"],["type","success",3,"message"],["diameter","20"]],template:function(l,t){if(l&1&&(e(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),o(4,"Login"),i()(),e(5,"mat-card-content"),c(6,te,1,1,"app-alert",1)(7,ie,1,1,"app-alert",2),e(8,"form",3),d("ngSubmit",function(){return t.onSubmit()}),e(9,"mat-form-field",4)(10,"mat-label"),o(11,"Email"),i(),s(12,"input",5),c(13,ne,2,0,"mat-error",6)(14,oe,2,0,"mat-error",6),i(),e(15,"mat-form-field",4)(16,"mat-label"),o(17,"Password"),i(),s(18,"input",7),e(19,"button",8),d("click",function(){return t.hidePassword=!t.hidePassword}),e(20,"mat-icon"),o(21),i()(),c(22,re,2,0,"mat-error",6),i(),e(23,"div",9)(24,"button",10),c(25,ae,1,0,"mat-spinner",11)(26,me,2,0,"span",6),i()()(),e(27,"div",12)(28,"p"),o(29,"Don't have an account? "),e(30,"a",13),o(31,"Register"),i()()(),e(32,"div",14)(33,"h3"),o(34,"Demo Accounts"),i(),e(35,"div",15),d("click",function(){return t.fillDemoAccount("<EMAIL>","admin123")}),e(36,"strong"),o(37,"Admin:"),i(),o(38," <EMAIL> / admin123 "),i(),e(39,"div",15),d("click",function(){return t.fillDemoAccount("<EMAIL>","provider123")}),e(40,"strong"),o(41,"Provider:"),i(),o(42," <EMAIL> / provider123 "),i(),e(43,"div",15),d("click",function(){return t.fillDemoAccount("<EMAIL>","viewer123")}),e(44,"strong"),o(45,"Viewer:"),i(),o(46," <EMAIL> / viewer123 "),i()()()()()),l&2){let p,f,_;m(6),a("ngIf",t.error),m(),a("ngIf",!1),m(),a("formGroup",t.loginForm),m(5),a("ngIf",(p=t.loginForm.get("email"))==null?null:p.hasError("required")),m(),a("ngIf",(f=t.loginForm.get("email"))==null?null:f.hasError("email")),m(4),a("type",t.hidePassword?"password":"text"),m(3),h(t.hidePassword?"visibility_off":"visibility"),m(),a("ngIf",(_=t.loginForm.get("password"))==null?null:_.hasError("required")),m(2),a("disabled",t.loginForm.invalid||t.loading),m(),a("ngIf",t.loading),m(),a("ngIf",!t.loading)}},dependencies:[M,x,H,B,R,j,q,G,V,b,S,F,P,w,I,E,X,W,K,Q,U,Z,Y,O,L,k,A,T,N,D,$],styles:[".login-container[_ngcontent-%COMP%]{padding:20px;display:flex;justify-content:center;align-items:center;min-height:calc(100vh - 120px)}mat-card[_ngcontent-%COMP%]{max-width:400px;width:100%}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:15px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:20px}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px;min-height:36px;display:flex;justify-content:center;align-items:center}.register-link[_ngcontent-%COMP%]{margin-top:20px;text-align:center}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.demo-accounts[_ngcontent-%COMP%]{margin-top:30px;padding:15px;background-color:#f5f5f5;border-radius:4px}.demo-accounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-top:0;margin-bottom:10px;font-size:16px;color:#000000b3}.demo-account[_ngcontent-%COMP%]{margin-bottom:5px;padding:5px;cursor:pointer;border-radius:4px;transition:background-color .2s}.demo-account[_ngcontent-%COMP%]:hover{background-color:#e0e0e0}"]})};export{ee as LoginComponent};
