import{b as je}from"./chunk-4N6OITQX.js";import{a as Be,b as Ge}from"./chunk-SSOC3NBY.js";import{a as ze,b as Ne,d as Le,g as qe,h as Ue}from"./chunk-ORNC4OUU.js";import{a as Me,b as we,c as Te,d as v,f as Re,g as Fe,j as Se,k as Pe,l as Ae,o as Oe,q as Ve}from"./chunk-Q6MA6IAZ.js";import{b as Xe}from"./chunk-OJZDOK3T.js";import{a as Ie,b as Ee}from"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import{a as fe,b as ge,c as ve,d as ye,e as Ce}from"./chunk-QB7XPJNY.js";import{k as oe,l as ne,n as de,p as De}from"./chunk-KGIFXD27.js";import{$ as ue,I as se,L as O,O as le,Q as he,Z as be,_ as ke,aa as pe,da as _e,ea as xe,k as me}from"./chunk-HE4KASLF.js";import{$ as u,Bb as a,Cb as r,Db as b,Ea as H,Fc as re,Hb as M,Ib as Y,Jb as y,Jc as ae,Kb as C,Lb as $,Mb as J,Ob as S,Pb as P,Qb as A,Tb as W,U as z,Ub as m,Va as d,Vb as ee,W as N,Y as L,_a as I,_b as te,fb as E,ga as q,gb as Q,ha as p,ia as _,ja as U,jc as ce,ka as j,kb as K,lb as f,mc as g,nc as ie,oa as B,ra as T,rb as R,sa as G,sb as o,ub as F,va as X,vb as Z}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var $e=["input"],Je=["label"],We=["*"],et=new L("mat-checkbox-default-options",{providedIn:"root",factory:Qe});function Qe(){return{color:"accent",clickAction:"check-indeterminate",disabledInteractive:!1}}var l=function(c){return c[c.Init=0]="Init",c[c.Checked=1]="Checked",c[c.Unchecked=2]="Unchecked",c[c.Indeterminate=3]="Indeterminate",c}(l||{}),tt={provide:Me,useExisting:z(()=>w),multi:!0},V=class{source;checked},He=Qe(),w=(()=>{class c{_elementRef=u(X);_changeDetectorRef=u(ce);_ngZone=u(G);_animationMode=u(H,{optional:!0});_options=u(et,{optional:!0});focus(){this._inputElement.nativeElement.focus()}_createChangeEvent(e){let i=new V;return i.source=this,i.checked=e,i}_getAnimationTargetElement(){return this._inputElement?.nativeElement}_animationClasses={uncheckedToChecked:"mdc-checkbox--anim-unchecked-checked",uncheckedToIndeterminate:"mdc-checkbox--anim-unchecked-indeterminate",checkedToUnchecked:"mdc-checkbox--anim-checked-unchecked",checkedToIndeterminate:"mdc-checkbox--anim-checked-indeterminate",indeterminateToChecked:"mdc-checkbox--anim-indeterminate-checked",indeterminateToUnchecked:"mdc-checkbox--anim-indeterminate-unchecked"};ariaLabel="";ariaLabelledby=null;ariaDescribedby;ariaExpanded;ariaControls;ariaOwns;_uniqueId;id;get inputId(){return`${this.id||this._uniqueId}-input`}required;labelPosition="after";name=null;change=new T;indeterminateChange=new T;value;disableRipple;_inputElement;_labelElement;tabIndex;color;disabledInteractive;_onTouched=()=>{};_currentAnimationClass="";_currentCheckState=l.Init;_controlValueAccessorChangeFn=()=>{};_validatorChangeFn=()=>{};constructor(){u(me).load(le);let e=u(new B("tabindex"),{optional:!0});this._options=this._options||He,this.color=this._options.color||He.color,this.tabIndex=e==null?0:parseInt(e)||0,this.id=this._uniqueId=u(se).getId("mat-mdc-checkbox-"),this.disabledInteractive=this._options?.disabledInteractive??!1}ngOnChanges(e){e.required&&this._validatorChangeFn()}ngAfterViewInit(){this._syncIndeterminate(this._indeterminate)}get checked(){return this._checked}set checked(e){e!=this.checked&&(this._checked=e,this._changeDetectorRef.markForCheck())}_checked=!1;get disabled(){return this._disabled}set disabled(e){e!==this.disabled&&(this._disabled=e,this._changeDetectorRef.markForCheck())}_disabled=!1;get indeterminate(){return this._indeterminate}set indeterminate(e){let i=e!=this._indeterminate;this._indeterminate=e,i&&(this._indeterminate?this._transitionCheckState(l.Indeterminate):this._transitionCheckState(this.checked?l.Checked:l.Unchecked),this.indeterminateChange.emit(this._indeterminate)),this._syncIndeterminate(this._indeterminate)}_indeterminate=!1;_isRippleDisabled(){return this.disableRipple||this.disabled}_onLabelTextChange(){this._changeDetectorRef.detectChanges()}writeValue(e){this.checked=!!e}registerOnChange(e){this._controlValueAccessorChangeFn=e}registerOnTouched(e){this._onTouched=e}setDisabledState(e){this.disabled=e}validate(e){return this.required&&e.value!==!0?{required:!0}:null}registerOnValidatorChange(e){this._validatorChangeFn=e}_transitionCheckState(e){let i=this._currentCheckState,t=this._getAnimationTargetElement();if(!(i===e||!t)&&(this._currentAnimationClass&&t.classList.remove(this._currentAnimationClass),this._currentAnimationClass=this._getAnimationClassForCheckStateTransition(i,e),this._currentCheckState=e,this._currentAnimationClass.length>0)){t.classList.add(this._currentAnimationClass);let n=this._currentAnimationClass;this._ngZone.runOutsideAngular(()=>{setTimeout(()=>{t.classList.remove(n)},1e3)})}}_emitChangeEvent(){this._controlValueAccessorChangeFn(this.checked),this.change.emit(this._createChangeEvent(this.checked)),this._inputElement&&(this._inputElement.nativeElement.checked=this.checked)}toggle(){this.checked=!this.checked,this._controlValueAccessorChangeFn(this.checked)}_handleInputClick(){let e=this._options?.clickAction;!this.disabled&&e!=="noop"?(this.indeterminate&&e!=="check"&&Promise.resolve().then(()=>{this._indeterminate=!1,this.indeterminateChange.emit(this._indeterminate)}),this._checked=!this._checked,this._transitionCheckState(this._checked?l.Checked:l.Unchecked),this._emitChangeEvent()):(this.disabled&&this.disabledInteractive||!this.disabled&&e==="noop")&&(this._inputElement.nativeElement.checked=this.checked,this._inputElement.nativeElement.indeterminate=this.indeterminate)}_onInteractionEvent(e){e.stopPropagation()}_onBlur(){Promise.resolve().then(()=>{this._onTouched(),this._changeDetectorRef.markForCheck()})}_getAnimationClassForCheckStateTransition(e,i){if(this._animationMode==="NoopAnimations")return"";switch(e){case l.Init:if(i===l.Checked)return this._animationClasses.uncheckedToChecked;if(i==l.Indeterminate)return this._checked?this._animationClasses.checkedToIndeterminate:this._animationClasses.uncheckedToIndeterminate;break;case l.Unchecked:return i===l.Checked?this._animationClasses.uncheckedToChecked:this._animationClasses.uncheckedToIndeterminate;case l.Checked:return i===l.Unchecked?this._animationClasses.checkedToUnchecked:this._animationClasses.checkedToIndeterminate;case l.Indeterminate:return i===l.Checked?this._animationClasses.indeterminateToChecked:this._animationClasses.indeterminateToUnchecked}return""}_syncIndeterminate(e){let i=this._inputElement;i&&(i.nativeElement.indeterminate=e)}_onInputClick(){this._handleInputClick()}_onTouchTargetClick(){this._handleInputClick(),this.disabled||this._inputElement.nativeElement.focus()}_preventBubblingFromLabel(e){e.target&&this._labelElement.nativeElement.contains(e.target)&&e.stopPropagation()}static \u0275fac=function(i){return new(i||c)};static \u0275cmp=E({type:c,selectors:[["mat-checkbox"]],viewQuery:function(i,t){if(i&1&&(S($e,5),S(Je,5)),i&2){let n;P(n=A())&&(t._inputElement=n.first),P(n=A())&&(t._labelElement=n.first)}},hostAttrs:[1,"mat-mdc-checkbox"],hostVars:16,hostBindings:function(i,t){i&2&&(Y("id",t.id),R("tabindex",null)("aria-label",null)("aria-labelledby",null),Z(t.color?"mat-"+t.color:"mat-accent"),F("_mat-animation-noopable",t._animationMode==="NoopAnimations")("mdc-checkbox--disabled",t.disabled)("mat-mdc-checkbox-disabled",t.disabled)("mat-mdc-checkbox-checked",t.checked)("mat-mdc-checkbox-disabled-interactive",t.disabledInteractive))},inputs:{ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],ariaExpanded:[2,"aria-expanded","ariaExpanded",g],ariaControls:[0,"aria-controls","ariaControls"],ariaOwns:[0,"aria-owns","ariaOwns"],id:"id",required:[2,"required","required",g],labelPosition:"labelPosition",name:"name",value:"value",disableRipple:[2,"disableRipple","disableRipple",g],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?void 0:ie(e)],color:"color",disabledInteractive:[2,"disabledInteractive","disabledInteractive",g],checked:[2,"checked","checked",g],disabled:[2,"disabled","disabled",g],indeterminate:[2,"indeterminate","indeterminate",g]},outputs:{change:"change",indeterminateChange:"indeterminateChange"},exportAs:["matCheckbox"],features:[te([tt,{provide:Te,useExisting:c,multi:!0}]),K,q],ngContentSelectors:We,decls:15,vars:23,consts:[["checkbox",""],["input",""],["label",""],["mat-internal-form-field","",3,"click","labelPosition"],[1,"mdc-checkbox"],[1,"mat-mdc-checkbox-touch-target",3,"click"],["type","checkbox",1,"mdc-checkbox__native-control",3,"blur","click","change","checked","indeterminate","disabled","id","required","tabIndex"],[1,"mdc-checkbox__ripple"],[1,"mdc-checkbox__background"],["focusable","false","viewBox","0 0 24 24","aria-hidden","true",1,"mdc-checkbox__checkmark"],["fill","none","d","M1.73,12.91 8.1,19.28 22.79,4.59",1,"mdc-checkbox__checkmark-path"],[1,"mdc-checkbox__mixedmark"],["mat-ripple","",1,"mat-mdc-checkbox-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-label",3,"for"]],template:function(i,t){if(i&1){let n=M();$(),a(0,"div",3),y("click",function(x){return p(n),_(t._preventBubblingFromLabel(x))}),a(1,"div",4,0)(3,"div",5),y("click",function(){return p(n),_(t._onTouchTargetClick())}),r(),a(4,"input",6,1),y("blur",function(){return p(n),_(t._onBlur())})("click",function(){return p(n),_(t._onInputClick())})("change",function(x){return p(n),_(t._onInteractionEvent(x))}),r(),b(6,"div",7),a(7,"div",8),U(),a(8,"svg",9),b(9,"path",10),r(),j(),b(10,"div",11),r(),b(11,"div",12),r(),a(12,"label",13,2),J(14),r()()}if(i&2){let n=W(2);o("labelPosition",t.labelPosition),d(4),F("mdc-checkbox--selected",t.checked),o("checked",t.checked)("indeterminate",t.indeterminate)("disabled",t.disabled&&!t.disabledInteractive)("id",t.inputId)("required",t.required)("tabIndex",t.disabled&&!t.disabledInteractive?-1:t.tabIndex),R("aria-label",t.ariaLabel||null)("aria-labelledby",t.ariaLabelledby)("aria-describedby",t.ariaDescribedby)("aria-checked",t.indeterminate?"mixed":null)("aria-controls",t.ariaControls)("aria-disabled",t.disabled&&t.disabledInteractive?!0:null)("aria-expanded",t.ariaExpanded)("aria-owns",t.ariaOwns)("name",t.name)("value",t.value),d(7),o("matRippleTrigger",n)("matRippleDisabled",t.disableRipple||t.disabled)("matRippleCentered",!0),d(),o("for",t.inputId)}},dependencies:[he,be],styles:['.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0})}return c})();var Ke=(()=>{class c{static \u0275fac=function(i){return new(i||c)};static \u0275mod=Q({type:c});static \u0275inj=N({imports:[w,O,O]})}return c})();function it(c,s){if(c&1&&b(0,"app-alert",6),c&2){let e=C();o("message",e.error)}}function rt(c,s){if(c&1&&b(0,"app-alert",7),c&2){let e=C();o("message",e.success)}}function at(c,s){c&1&&(a(0,"mat-error"),m(1," Username is required "),r())}function ot(c,s){c&1&&(a(0,"mat-error"),m(1," Username must be at least 3 characters "),r())}function nt(c,s){c&1&&(a(0,"mat-error"),m(1," Email is required "),r())}function dt(c,s){c&1&&(a(0,"mat-error"),m(1," Please enter a valid email address "),r())}function mt(c,s){c&1&&(a(0,"mat-error"),m(1," Password is required "),r())}function st(c,s){c&1&&(a(0,"mat-error"),m(1," Password must be at least 6 characters "),r())}function lt(c,s){c&1&&(a(0,"mat-error"),m(1," Provider name is required "),r())}function ht(c,s){if(c&1&&(a(0,"mat-form-field",9)(1,"mat-label"),m(2,"Provider Name"),r(),b(3,"input",22),f(4,lt,2,0,"mat-error",11),r()),c&2){let e,i=C(2);d(4),o("ngIf",(e=i.registerForm.get("providerName"))==null?null:e.hasError("required"))}}function bt(c,s){c&1&&b(0,"mat-spinner",23)}function kt(c,s){c&1&&(a(0,"span"),m(1,"Register"),r())}function ut(c,s){if(c&1){let e=M();a(0,"form",8),y("ngSubmit",function(){p(e);let t=C();return _(t.onSubmit())}),a(1,"mat-form-field",9)(2,"mat-label"),m(3,"Username"),r(),b(4,"input",10),f(5,at,2,0,"mat-error",11)(6,ot,2,0,"mat-error",11),r(),a(7,"mat-form-field",9)(8,"mat-label"),m(9,"Email"),r(),b(10,"input",12),f(11,nt,2,0,"mat-error",11)(12,dt,2,0,"mat-error",11),r(),a(13,"mat-form-field",9)(14,"mat-label"),m(15,"Password"),r(),b(16,"input",13),a(17,"button",14),y("click",function(){p(e);let t=C();return _(t.hidePassword=!t.hidePassword)}),a(18,"mat-icon"),m(19),r()(),f(20,mt,2,0,"mat-error",11)(21,st,2,0,"mat-error",11),r(),a(22,"div",15)(23,"mat-checkbox",16),m(24,"Register as a Provider"),r(),f(25,ht,5,1,"mat-form-field",17),r(),a(26,"div",18)(27,"button",19),m(28,"Cancel"),r(),a(29,"button",20),f(30,bt,1,0,"mat-spinner",21)(31,kt,2,0,"span",11),r()()()}if(c&2){let e,i,t,n,k,x,D,h=C();o("formGroup",h.registerForm),d(5),o("ngIf",(e=h.registerForm.get("username"))==null?null:e.hasError("required")),d(),o("ngIf",(i=h.registerForm.get("username"))==null?null:i.hasError("minlength")),d(5),o("ngIf",(t=h.registerForm.get("email"))==null?null:t.hasError("required")),d(),o("ngIf",(n=h.registerForm.get("email"))==null?null:n.hasError("email")),d(4),o("type",h.hidePassword?"password":"text"),d(3),ee(h.hidePassword?"visibility_off":"visibility"),d(),o("ngIf",(k=h.registerForm.get("password"))==null?null:k.hasError("required")),d(),o("ngIf",(x=h.registerForm.get("password"))==null?null:x.hasError("minlength")),d(4),o("ngIf",(D=h.registerForm.get("isProvider"))==null?null:D.value),d(4),o("disabled",h.registerForm.invalid||h.loading),d(),o("ngIf",h.loading),d(),o("ngIf",!h.loading)}}function pt(c,s){c&1&&(a(0,"div",24)(1,"p"),m(2,"Already have an account? "),a(3,"a",25),m(4,"Login"),r()()())}function _t(c,s){c&1&&(a(0,"div",26)(1,"button",27),m(2," Go to Login "),r()())}var Ze=class c{constructor(s,e,i){this.fb=s;this.authService=e;this.router=i;this.registerForm=this.fb.group({username:["",[v.required,v.minLength(3)]],email:["",[v.required,v.email]],password:["",[v.required,v.minLength(6)]],isProvider:[!1],providerName:[""]}),this.registerForm.get("isProvider")?.valueChanges.subscribe(t=>{let n=this.registerForm.get("providerName");t?n?.setValidators([v.required]):n?.clearValidators(),n?.updateValueAndValidity()})}registerForm;loading=!1;error="";success="";hidePassword=!0;onSubmit(){if(this.registerForm.invalid)return;this.loading=!0,this.error="";let{username:s,email:e,password:i,isProvider:t,providerName:n}=this.registerForm.value,k={username:s,email:e,password:i,providerName:t?n:void 0};this.authService.register(k).subscribe({next:()=>{this.loading=!1,this.success="Registration successful! You can now login.",this.registerForm.reset()},error:x=>{this.loading=!1,this.error=x.message||"Registration failed. Please try again."}})}static \u0275fac=function(e){return new(e||c)(I(Oe),I(De),I(oe))};static \u0275cmp=E({type:c,selectors:[["app-register"]],decls:11,vars:5,consts:[[1,"register-container"],["type","error",3,"message",4,"ngIf"],["type","success",3,"message",4,"ngIf"],[3,"formGroup","ngSubmit",4,"ngIf"],["class","login-link",4,"ngIf"],["class","success-actions",4,"ngIf"],["type","error",3,"message"],["type","success",3,"message"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","username","placeholder","Enter your username"],[4,"ngIf"],["matInput","","formControlName","email","type","email","placeholder","Enter your email"],["matInput","","formControlName","password","placeholder","Enter your password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],[1,"provider-section"],["formControlName","isProvider"],["appearance","outline","class","full-width",4,"ngIf"],[1,"form-actions"],["mat-button","","type","button","routerLink","/auth/login"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],["matInput","","formControlName","providerName","placeholder","Enter your provider name"],["diameter","20"],[1,"login-link"],["routerLink","/auth/login"],[1,"success-actions"],["mat-raised-button","","color","primary","routerLink","/auth/login"]],template:function(e,i){e&1&&(a(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),m(4,"Register"),r()(),a(5,"mat-card-content"),f(6,it,1,1,"app-alert",1)(7,rt,1,1,"app-alert",2)(8,ut,32,13,"form",3)(9,pt,5,0,"div",4)(10,_t,3,0,"div",5),r()()()),e&2&&(d(6),o("ngIf",i.error),d(),o("ngIf",i.success),d(),o("ngIf",!i.success),d(),o("ngIf",!i.success),d(),o("ngIf",i.success))},dependencies:[ae,re,Ve,Se,we,Re,Fe,Pe,Ae,de,ne,xe,ke,pe,_e,ue,Ue,qe,ze,Ne,Le,Ge,Be,ve,fe,ge,Ce,ye,Ee,Ie,Ke,w,je,Xe],styles:[".register-container[_ngcontent-%COMP%]{padding:20px;display:flex;justify-content:center;align-items:center;min-height:calc(100vh - 120px)}mat-card[_ngcontent-%COMP%]{max-width:400px;width:100%}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:15px}.provider-section[_ngcontent-%COMP%]{margin-bottom:20px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:20px}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px;min-height:36px;display:flex;justify-content:center;align-items:center}.login-link[_ngcontent-%COMP%]{margin-top:20px;text-align:center}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.success-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:20px}"]})};export{Ze as RegisterComponent};
