import{a as ne,b as re}from"./chunk-C6Q5SG76.js";function ja(e,t){return Object.is(e,t)}var H=null,tn=!1,Wo=1,G=Symbol("SIGNAL");function b(e){let t=H;return H=e,t}function Ba(){return H}function Of(){return tn}var et={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function $n(e){if(tn)throw new Error("");if(H===null)return;H.consumerOnSignalRead(e);let t=H.nextProducerIndex++;if(zn(H),t<H.producerNode.length&&H.producerNode[t]!==e&&nn(H)){let n=H.producerNode[t];Un(n,H.producerIndexOfThis[t])}H.producerNode[t]!==e&&(H.producerNode[t]=e,H.producerIndexOfThis[t]=nn(H)?Ga(e,H,t):0),H.producerLastReadVersion[t]=e.version}function Ff(){Wo++}function Ha(e){if(!(nn(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Wo)){if(!e.producerMustRecompute(e)&&!on(e)){Va(e);return}e.producerRecomputeValue(e),Va(e)}}function $a(e){if(e.liveConsumerNode===void 0)return;let t=tn;tn=!0;try{for(let n of e.liveConsumerNode)n.dirty||za(n)}finally{tn=t}}function Ua(){return H?.consumerAllowSignalWrites!==!1}function za(e){e.dirty=!0,$a(e),e.consumerMarkedDirty?.(e)}function Va(e){e.dirty=!1,e.lastCleanEpoch=Wo}function Rt(e){return e&&(e.nextProducerIndex=0),b(e)}function rn(e,t){if(b(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(nn(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Un(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function on(e){zn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ha(n),r!==n.version))return!0}return!1}function Ot(e){if(zn(e),nn(e))for(let t=0;t<e.producerNode.length;t++)Un(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ga(e,t,n){if(Wa(e),e.liveConsumerNode.length===0&&qa(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ga(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Un(e,t){if(Wa(e),e.liveConsumerNode.length===1&&qa(e))for(let r=0;r<e.producerNode.length;r++)Un(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];zn(o),o.producerIndexOfThis[r]=t}}function nn(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function zn(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Wa(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function qa(e){return e.producerNode!==void 0}function qo(e){let t=Object.create(kf);t.computation=e;let n=()=>{if(Ha(t),$n(t),t.value===Hn)throw t.error;return t.value};return n[G]=t,n}var Uo=Symbol("UNSET"),zo=Symbol("COMPUTING"),Hn=Symbol("ERRORED"),kf=re(ne({},et),{value:Uo,dirty:!0,error:null,equal:ja,kind:"computed",producerMustRecompute(e){return e.value===Uo||e.value===zo},producerRecomputeValue(e){if(e.value===zo)throw new Error("Detected cycle in computations.");let t=e.value;e.value=zo;let n=Rt(e),r,o=!1;try{r=e.computation(),b(null),o=t!==Uo&&t!==Hn&&r!==Hn&&e.equal(t,r)}catch(i){r=Hn,e.error=i}finally{rn(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Pf(){throw new Error}var Za=Pf;function Qa(){Za()}function Ya(e){Za=e}var Lf=null;function Ka(e){let t=Object.create(Zo);t.value=e;let n=()=>($n(t),t.value);return n[G]=t,n}function Gn(e,t){Ua()||Qa(),e.equal(e.value,t)||(e.value=t,Vf(e))}function Ja(e,t){Ua()||Qa(),Gn(e,t(e.value))}var Zo=re(ne({},et),{equal:ja,value:void 0,kind:"signal"});function Vf(e){e.version++,Ff(),$a(e),Lf?.()}function Xa(e,t,n){let r=Object.create(jf);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;let o=u=>{r.cleanupFn=u};function i(u){return u.fn===null&&u.schedule===null}function s(u){i(u)||(Ot(u),u.cleanupFn(),u.fn=null,u.schedule=null,u.cleanupFn=Go)}let a=()=>{if(r.fn===null)return;if(Of())throw new Error("Schedulers cannot synchronously execute watches while scheduling.");if(r.dirty=!1,r.hasRun&&!on(r))return;r.hasRun=!0;let u=Rt(r);try{r.cleanupFn(),r.cleanupFn=Go,r.fn(o)}finally{rn(r,u)}};return r.ref={notify:()=>za(r),run:a,cleanup:()=>r.cleanupFn(),destroy:()=>s(r),[G]:r},r.ref}var Go=()=>{},jf=re(ne({},et),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule!==null&&e.schedule(e.ref)},hasRun:!1,cleanupFn:Go});function E(e){return typeof e=="function"}function Ft(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Wn=Ft(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function tt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var j=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(E(r))try{r()}catch(i){t=i instanceof Wn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{eu(i)}catch(s){t=t??[],s instanceof Wn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Wn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)eu(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&tt(n,t)}remove(t){let{_finalizers:n}=this;n&&tt(n,t),t instanceof e&&t._removeParent(this)}};j.EMPTY=(()=>{let e=new j;return e.closed=!0,e})();var Qo=j.EMPTY;function qn(e){return e instanceof j||e&&"closed"in e&&E(e.remove)&&E(e.add)&&E(e.unsubscribe)}function eu(e){E(e)?e():e.unsubscribe()}var me={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var kt={setTimeout(e,t,...n){let{delegate:r}=kt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=kt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Zn(e){kt.setTimeout(()=>{let{onUnhandledError:t}=me;if(t)t(e);else throw e})}function sn(){}var tu=Yo("C",void 0,void 0);function nu(e){return Yo("E",void 0,e)}function ru(e){return Yo("N",e,void 0)}function Yo(e,t,n){return{kind:e,value:t,error:n}}var nt=null;function Pt(e){if(me.useDeprecatedSynchronousErrorHandling){let t=!nt;if(t&&(nt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=nt;if(nt=null,n)throw r}}else e()}function ou(e){me.useDeprecatedSynchronousErrorHandling&&nt&&(nt.errorThrown=!0,nt.error=e)}var rt=class extends j{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,qn(t)&&t.add(this)):this.destination=$f}static create(t,n,r){return new xe(t,n,r)}next(t){this.isStopped?Jo(ru(t),this):this._next(t)}error(t){this.isStopped?Jo(nu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Jo(tu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Bf=Function.prototype.bind;function Ko(e,t){return Bf.call(e,t)}var Xo=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Qn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Qn(r)}else Qn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Qn(n)}}},xe=class extends rt{constructor(t,n,r){super();let o;if(E(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&me.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Ko(t.next,i),error:t.error&&Ko(t.error,i),complete:t.complete&&Ko(t.complete,i)}):o=t}this.destination=new Xo(o)}};function Qn(e){me.useDeprecatedSynchronousErrorHandling?ou(e):Zn(e)}function Hf(e){throw e}function Jo(e,t){let{onStoppedNotification:n}=me;n&&kt.setTimeout(()=>n(e,t))}var $f={closed:!0,next:sn,error:Hf,complete:sn};var Lt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function K(e){return e}function Uf(...e){return ei(e)}function ei(e){return e.length===0?K:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var T=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Gf(n)?n:new xe(n,r,o);return Pt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=iu(r),new r((o,i)=>{let s=new xe({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Lt](){return this}pipe(...n){return ei(n)(this)}toPromise(n){return n=iu(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function iu(e){var t;return(t=e??me.Promise)!==null&&t!==void 0?t:Promise}function zf(e){return e&&E(e.next)&&E(e.error)&&E(e.complete)}function Gf(e){return e&&e instanceof rt||zf(e)&&qn(e)}function ti(e){return E(e?.lift)}function v(e){return t=>{if(ti(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function m(e,t,n,r,o){return new ni(e,t,n,r,o)}var ni=class extends rt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function ri(){return v((e,t)=>{let n=null;e._refCount++;let r=m(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var oi=class extends T{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,ti(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new j;let n=this.getSubject();t.add(this.source.subscribe(m(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=j.EMPTY)}return t}refCount(){return ri()(this)}};var su=Ft(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var oe=(()=>{class e extends T{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Yn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new su}next(n){Pt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Pt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Pt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Qo:(this.currentObservers=null,i.push(n),new j(()=>{this.currentObservers=null,tt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new T;return n.source=this,n}}return e.create=(t,n)=>new Yn(t,n),e})(),Yn=class extends oe{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Qo}};var an=class extends oe{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var un={now(){return(un.delegate||Date).now()},delegate:void 0};var cn=class extends oe{constructor(t=1/0,n=1/0,r=un){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let u=1;u<r.length&&r[u]<=s;u+=2)a=u;a&&r.splice(0,a+1)}}};var Kn=class extends j{constructor(t,n){super()}schedule(t,n=0){return this}};var ln={setInterval(e,t,...n){let{delegate:r}=ln;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=ln;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Jn=class extends Kn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return ln.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&ln.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,tt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Vt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Vt.now=un.now;var Xn=class extends Vt{constructor(t,n=Vt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var dn=new Xn(Jn),au=dn;var ot=new T(e=>e.complete());function er(e){return e&&E(e.schedule)}function ii(e){return e[e.length-1]}function tr(e){return E(ii(e))?e.pop():void 0}function be(e){return er(ii(e))?e.pop():void 0}function uu(e,t){return typeof ii(e)=="number"?e.pop():t}function lu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function cu(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function it(e){return this instanceof it?(this.v=e,this):new it(e)}function du(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(O,N){i.push([f,g,O,N])>1||u(f,g)})},h&&(o[f]=h(o[f])))}function u(f,h){try{c(r[f](h))}catch(g){p(i[0][3],g)}}function c(f){f.value instanceof it?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){u("next",f)}function d(f){u("throw",f)}function p(f,h){f(h),i.shift(),i.length&&u(i[0][0],i[0][1])}}function fu(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof cu=="function"?cu(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var nr=e=>e&&typeof e.length=="number"&&typeof e!="function";function rr(e){return E(e?.then)}function or(e){return E(e[Lt])}function ir(e){return Symbol.asyncIterator&&E(e?.[Symbol.asyncIterator])}function sr(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Wf(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ar=Wf();function ur(e){return E(e?.[ar])}function cr(e){return du(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield it(n.read());if(o)return yield it(void 0);yield yield it(r)}}finally{n.releaseLock()}})}function lr(e){return E(e?.getReader)}function x(e){if(e instanceof T)return e;if(e!=null){if(or(e))return qf(e);if(nr(e))return Zf(e);if(rr(e))return Qf(e);if(ir(e))return pu(e);if(ur(e))return Yf(e);if(lr(e))return Kf(e)}throw sr(e)}function qf(e){return new T(t=>{let n=e[Lt]();if(E(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Zf(e){return new T(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Qf(e){return new T(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Zn)})}function Yf(e){return new T(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function pu(e){return new T(t=>{Jf(e,t).catch(n=>t.error(n))})}function Kf(e){return pu(cr(e))}function Jf(e,t){var n,r,o,i;return lu(this,void 0,void 0,function*(){try{for(n=fu(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ie(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function dr(e,t=0){return v((n,r)=>{n.subscribe(m(r,o=>ie(r,e,()=>r.next(o),t),()=>ie(r,e,()=>r.complete(),t),o=>ie(r,e,()=>r.error(o),t)))})}function fr(e,t=0){return v((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function hu(e,t){return x(e).pipe(fr(t),dr(t))}function gu(e,t){return x(e).pipe(fr(t),dr(t))}function mu(e,t){return new T(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function yu(e,t){return new T(n=>{let r;return ie(n,t,()=>{r=e[ar](),ie(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>E(r?.return)&&r.return()})}function pr(e,t){if(!e)throw new Error("Iterable cannot be null");return new T(n=>{ie(n,t,()=>{let r=e[Symbol.asyncIterator]();ie(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function vu(e,t){return pr(cr(e),t)}function Du(e,t){if(e!=null){if(or(e))return hu(e,t);if(nr(e))return mu(e,t);if(rr(e))return gu(e,t);if(ir(e))return pr(e,t);if(ur(e))return yu(e,t);if(lr(e))return vu(e,t)}throw sr(e)}function _e(e,t){return t?Du(e,t):x(e)}function Xf(...e){let t=be(e);return _e(e,t)}function ep(e,t){let n=E(e)?e:()=>e,r=o=>o.error(n());return new T(t?o=>t.schedule(r,0,o):r)}function tp(e){return!!e&&(e instanceof T||E(e.lift)&&E(e.subscribe))}var st=Ft(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Eu(e){return e instanceof Date&&!isNaN(e)}function $e(e,t){return v((n,r)=>{let o=0;n.subscribe(m(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:np}=Array;function rp(e,t){return np(t)?e(...t):e(t)}function hr(e){return $e(t=>rp(e,t))}var{isArray:op}=Array,{getPrototypeOf:ip,prototype:sp,keys:ap}=Object;function gr(e){if(e.length===1){let t=e[0];if(op(t))return{args:t,keys:null};if(up(t)){let n=ap(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function up(e){return e&&typeof e=="object"&&ip(e)===sp}function mr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function cp(...e){let t=be(e),n=tr(e),{args:r,keys:o}=gr(e);if(r.length===0)return _e([],t);let i=new T(lp(r,t,o?s=>mr(o,s):K));return n?i.pipe(hr(n)):i}function lp(e,t,n=K){return r=>{Iu(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)Iu(t,()=>{let c=_e(e[u],t),l=!1;c.subscribe(m(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Iu(e,t,n){e?ie(n,e,t):t()}function wu(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,p=()=>{d&&!u.length&&!c&&t.complete()},f=g=>c<r?h(g):u.push(g),h=g=>{i&&t.next(g),c++;let O=!1;x(n(g,l++)).subscribe(m(t,N=>{o?.(N),i?f(N):t.next(N)},()=>{O=!0},void 0,()=>{if(O)try{for(c--;u.length&&c<r;){let N=u.shift();s?ie(t,s,()=>h(N)):h(N)}p()}catch(N){t.error(N)}}))};return e.subscribe(m(t,f,()=>{d=!0,p()})),()=>{a?.()}}function at(e,t,n=1/0){return E(t)?at((r,o)=>$e((i,s)=>t(r,i,o,s))(x(e(r,o))),n):(typeof t=="number"&&(n=t),v((r,o)=>wu(r,o,e,n)))}function fn(e=1/0){return at(K,e)}function Cu(){return fn(1)}function yr(...e){return Cu()(_e(e,be(e)))}function dp(e){return new T(t=>{x(e()).subscribe(t)})}function fp(...e){let t=tr(e),{args:n,keys:r}=gr(e),o=new T(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;x(n[l]).subscribe(m(i,p=>{d||(d=!0,c--),a[l]=p},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?mr(r,a):a),i.complete())}))}});return t?o.pipe(hr(t)):o}function si(e=0,t,n=au){let r=-1;return t!=null&&(er(t)?n=t:r=t),new T(o=>{let i=Eu(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function pp(...e){let t=be(e),n=uu(e,1/0),r=e;return r.length?r.length===1?x(r[0]):fn(n)(_e(r,t)):ot}function ut(e,t){return v((n,r)=>{let o=0;n.subscribe(m(r,i=>e.call(t,i,o++)&&r.next(i)))})}function bu(e){return v((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};t.subscribe(m(n,c=>{r=!0,o=c,i||x(e(c)).subscribe(i=m(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function hp(e,t=dn){return bu(()=>si(e,t))}function _u(e){return v((t,n)=>{let r=null,o=!1,i;r=t.subscribe(m(n,void 0,void 0,s=>{i=x(e(s,_u(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Mu(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(m(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function gp(e,t){return E(t)?at(e,t,1):at(e,1)}function mp(e,t=dn){return v((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(m(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function pn(e){return v((t,n)=>{let r=!1;t.subscribe(m(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function vr(e){return e<=0?()=>ot:v((t,n)=>{let r=0;t.subscribe(m(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function yp(e,t=K){return e=e??vp,v((n,r)=>{let o,i=!0;n.subscribe(m(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function vp(e,t){return e===t}function Dr(e=Dp){return v((t,n)=>{let r=!1;t.subscribe(m(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Dp(){return new st}function Ep(e){return v((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ip(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ut((o,i)=>e(o,i,r)):K,vr(1),n?pn(t):Dr(()=>new st))}function ai(e){return e<=0?()=>ot:v((t,n)=>{let r=[];t.subscribe(m(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function wp(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ut((o,i)=>e(o,i,r)):K,ai(1),n?pn(t):Dr(()=>new st))}function Cp(){return v((e,t)=>{let n,r=!1;e.subscribe(m(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function bp(e,t){return v(Mu(e,t,arguments.length>=2,!0))}function ci(e={}){let{connector:t=()=>new oe,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=u=void 0,l=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return v((g,O)=>{c++,!d&&!l&&p();let N=u=u??t();O.add(()=>{c--,c===0&&!d&&!l&&(a=ui(h,o))}),N.subscribe(O),!s&&c>0&&(s=new xe({next:te=>N.next(te),error:te=>{d=!0,p(),a=ui(f,n,te),N.error(te)},complete:()=>{l=!0,p(),a=ui(f,r),N.complete()}}),x(g).subscribe(s))})(i)}}function ui(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new xe({next:()=>{r.unsubscribe(),e()}});return x(t(...n)).subscribe(r)}function _p(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,ci({connector:()=>new cn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Mp(e){return ut((t,n)=>e<=n)}function Tp(...e){let t=be(e);return v((n,r)=>{(t?yr(e,n,t):yr(e,n)).subscribe(r)})}function Sp(e,t){return v((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(m(r,u=>{o?.unsubscribe();let c=0,l=i++;x(e(u,l)).subscribe(o=m(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Np(e){return v((t,n)=>{x(e).subscribe(m(n,()=>n.complete(),sn)),!n.closed&&t.subscribe(n)})}function xp(e,t=!1){return v((n,r)=>{let o=0;n.subscribe(m(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Ap(e,t,n){let r=E(e)||t||n?{next:e,error:t,complete:n}:e;return r?v((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(m(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):K}var vc="https://g.co/ng/security#xss",_=class extends Error{code;constructor(t,n){super(Dc(t,n)),this.code=t}};function Dc(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var Ec=Symbol("InputSignalNode#UNSET"),Op=re(ne({},Zo),{transformFn:void 0,applyValueToInputSignal(e,t){Gn(e,t)}});function Ic(e,t){let n=Object.create(Op);n.value=e,n.transformFn=t?.transform;function r(){if($n(n),n.value===Ec)throw new _(-950,!1);return n.value}return r[G]=n,r}function Sn(e){return{toString:e}.toString()}var Er="__parameters__";function Fp(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function wc(e,t,n){return Sn(()=>{let r=Fp(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(Er)?u[Er]:Object.defineProperty(u,Er,{value:[]})[Er];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var ze=globalThis;function A(e){for(let t in e)if(e[t]===A)return t;throw Error("Could not find renamed property on target object.")}function kp(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function X(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(X).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Ci(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Pp=A({__forward_ref__:A});function Cc(e){return e.__forward_ref__=Cc,e.toString=function(){return X(this())},e}function U(e){return bc(e)?e():e}function bc(e){return typeof e=="function"&&e.hasOwnProperty(Pp)&&e.__forward_ref__===Cc}function F(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function _c(e){return{providers:e.providers||[],imports:e.imports||[]}}function lo(e){return Tu(e,Mc)||Tu(e,Tc)}function KT(e){return lo(e)!==null}function Tu(e,t){return e.hasOwnProperty(t)?e[t]:null}function Lp(e){let t=e&&(e[Mc]||e[Tc]);return t||null}function Su(e){return e&&(e.hasOwnProperty(Nu)||e.hasOwnProperty(Vp))?e[Nu]:null}var Mc=A({\u0275prov:A}),Nu=A({\u0275inj:A}),Tc=A({ngInjectableDef:A}),Vp=A({ngInjectorDef:A}),S=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=F({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Sc(e){return e&&!!e.\u0275providers}var jp=A({\u0275cmp:A}),Bp=A({\u0275dir:A}),Hp=A({\u0275pipe:A}),$p=A({\u0275mod:A}),Or=A({\u0275fac:A}),yn=A({__NG_ELEMENT_ID__:A}),xu=A({__NG_ENV_ID__:A});function fo(e){return typeof e=="string"?e:e==null?"":String(e)}function Up(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():fo(e)}function zp(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new _(-200,e)}function Fs(e,t){throw new _(-201,!1)}var M=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(M||{}),bi;function Nc(){return bi}function se(e){let t=bi;return bi=e,t}function xc(e,t,n){let r=lo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&M.Optional)return null;if(t!==void 0)return t;Fs(e,"Injector")}var Gp={},vn=Gp,_i="__NG_DI_FLAG__",Fr="ngTempTokenPath",Wp="ngTokenPath",qp=/\n/gm,Zp="\u0275",Au="__source",Ut;function Qp(){return Ut}function Ue(e){let t=Ut;return Ut=e,t}function Yp(e,t=M.Default){if(Ut===void 0)throw new _(-203,!1);return Ut===null?xc(e,void 0,t):Ut.get(e,t&M.Optional?null:void 0,t)}function q(e,t=M.Default){return(Nc()||Yp)(U(e),t)}function I(e,t=M.Default){return q(e,po(t))}function po(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Mi(e){let t=[];for(let n=0;n<e.length;n++){let r=U(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=M.Default;for(let s=0;s<r.length;s++){let a=r[s],u=Kp(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(q(o,i))}else t.push(q(r))}return t}function Ac(e,t){return e[_i]=t,e.prototype[_i]=t,e}function Kp(e){return e[_i]}function Jp(e,t,n,r){let o=e[Fr];throw t[Au]&&o.unshift(t[Au]),e.message=Xp(`
`+e.message,o,n,r),e[Wp]=o,e[Fr]=null,e}function Xp(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Zp?e.slice(2):e;let o=X(t);if(Array.isArray(t))o=t.map(X).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):X(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(qp,`
  `)}`}var eh=Ac(wc("Optional"),8);var th=Ac(wc("SkipSelf"),4);function dt(e,t){let n=e.hasOwnProperty(Or);return n?e[Or]:null}function nh(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function rh(e){return e.flat(Number.POSITIVE_INFINITY)}function ks(e,t){e.forEach(n=>Array.isArray(n)?ks(n,t):t(n))}function Rc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function kr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function oh(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function ih(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ho(e,t,n){let r=Nn(e,t);return r>=0?e[r|1]=n:(r=~r,ih(e,r,t,n)),r}function li(e,t){let n=Nn(e,t);if(n>=0)return e[n|1]}function Nn(e,t){return sh(e,t,1)}function sh(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ae={},J=[],Pr=new S(""),Oc=new S("",-1),Fc=new S(""),Lr=class{get(t,n=vn){if(n===vn){let r=new Error(`NullInjectorError: No provider for ${X(t)}!`);throw r.name="NullInjectorError",r}return n}};function kc(e,t){let n=e[$p]||null;if(!n&&t===!0)throw new Error(`Type ${X(e)} does not have '\u0275mod' property.`);return n}function Re(e){return e[jp]||null}function Ps(e){return e[Bp]||null}function Pc(e){return e[Hp]||null}function ah(e){let t=Re(e)||Ps(e)||Pc(e);return t!==null&&t.standalone}function uh(e){return{\u0275providers:e}}function ch(...e){return{\u0275providers:Lc(!0,e),\u0275fromNgModule:!0}}function Lc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ks(t,s=>{let a=s;Ti(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Vc(o,i),n}function Vc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Ls(o,i=>{t(i,r)})}}function Ti(e,t,n,r){if(e=U(e),!e)return!1;let o=null,i=Su(e),s=!i&&Re(e);if(!i&&!s){let u=e.ngModule;if(i=Su(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)Ti(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{ks(i.imports,l=>{Ti(l,t,n,r)&&(c||=[],c.push(l))})}finally{}c!==void 0&&Vc(c,t)}if(!a){let c=dt(o)||(()=>new o);t({provide:o,useFactory:c,deps:J},o),t({provide:Fc,useValue:o,multi:!0},o),t({provide:Pr,useValue:()=>q(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;Ls(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Ls(e,t){for(let n of e)Sc(n)&&(n=n.\u0275providers),Array.isArray(n)?Ls(n,t):t(n)}var lh=A({provide:String,useValue:A});function jc(e){return e!==null&&typeof e=="object"&&lh in e}function dh(e){return!!(e&&e.useExisting)}function fh(e){return!!(e&&e.useFactory)}function Gt(e){return typeof e=="function"}function ph(e){return!!e.useClass}var Bc=new S(""),Mr={},hh={},di;function go(){return di===void 0&&(di=new Lr),di}var Ze=class{},Dn=class extends Ze{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Ni(t,s=>this.processProvider(s)),this.records.set(Oc,jt(void 0,this)),o.has("environment")&&this.records.set(Ze,jt(void 0,this));let i=this.records.get(Bc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Fc,J,M.Self))}destroy(){gn(this),this._destroyed=!0;let t=b(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),b(t)}}onDestroy(t){return gn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){gn(this);let n=Ue(this),r=se(void 0),o;try{return t()}finally{Ue(n),se(r)}}get(t,n=vn,r=M.Default){if(gn(this),t.hasOwnProperty(xu))return t[xu](this);r=po(r);let o,i=Ue(this),s=se(void 0);try{if(!(r&M.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=Dh(t)&&lo(t);c&&this.injectableDefInScope(c)?u=jt(Si(t),Mr):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&M.Self?go():this.parent;return n=r&M.Optional&&n===vn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Fr]=a[Fr]||[]).unshift(X(t)),i)throw a;return Jp(a,t,"R3InjectorError",this.source)}else throw a}finally{se(s),Ue(i)}}resolveInjectorInitializers(){let t=b(null),n=Ue(this),r=se(void 0),o;try{let i=this.get(Pr,J,M.Self);for(let s of i)s()}finally{Ue(n),se(r),b(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(X(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=U(t);let n=Gt(t)?t:U(t&&t.provide),r=mh(t);if(!Gt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=jt(void 0,Mr,!0),o.factory=()=>Mi(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=b(null);try{return n.value===Mr&&(n.value=hh,n.value=n.factory()),typeof n.value=="object"&&n.value&&vh(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{b(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=U(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Si(e){let t=lo(e),n=t!==null?t.factory:dt(e);if(n!==null)return n;if(e instanceof S)throw new _(204,!1);if(e instanceof Function)return gh(e);throw new _(204,!1)}function gh(e){if(e.length>0)throw new _(204,!1);let n=Lp(e);return n!==null?()=>n.factory(e):()=>new e}function mh(e){if(jc(e))return jt(void 0,e.useValue);{let t=Hc(e);return jt(t,Mr)}}function Hc(e,t,n){let r;if(Gt(e)){let o=U(e);return dt(o)||Si(o)}else if(jc(e))r=()=>U(e.useValue);else if(fh(e))r=()=>e.useFactory(...Mi(e.deps||[]));else if(dh(e))r=()=>q(U(e.useExisting));else{let o=U(e&&(e.useClass||e.provide));if(yh(e))r=()=>new o(...Mi(e.deps));else return dt(o)||Si(o)}return r}function gn(e){if(e.destroyed)throw new _(205,!1)}function jt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function yh(e){return!!e.deps}function vh(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Dh(e){return typeof e=="function"||typeof e=="object"&&e instanceof S}function Ni(e,t){for(let n of e)Array.isArray(n)?Ni(n,t):n&&Sc(n)?Ni(n.\u0275providers,t):t(n)}function $c(e,t){e instanceof Dn&&gn(e);let n,r=Ue(e),o=se(void 0);try{return t()}finally{Ue(r),se(o)}}function Uc(){return Nc()!==void 0||Qp()!=null}function mo(e){if(!Uc())throw new _(-203,!1)}function Eh(e){return typeof e=="function"}var Le=0,w=1,y=2,Z=3,De=4,ae=5,Wt=6,Vr=7,z=8,En=9,Oe=10,P=11,In=12,Ru=13,Xt=14,le=15,ft=16,Bt=17,Fe=18,yo=19,zc=20,We=21,Tr=22,pt=23,ue=24,$=25,Gc=1;var ht=7,jr=8,qt=9,W=10;function qe(e){return Array.isArray(e)&&typeof e[Gc]=="object"}function Ve(e){return Array.isArray(e)&&e[Gc]===!0}function Vs(e){return(e.flags&4)!==0}function bt(e){return e.componentOffset>-1}function vo(e){return(e.flags&1)===1}function ke(e){return!!e.template}function Br(e){return(e[y]&512)!==0}function xn(e){return(e[y]&256)===256}var xi=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Wc(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var qc=(()=>{let e=()=>Zc;return e.ngInherit=!0,e})();function Zc(e){return e.type.prototype.ngOnChanges&&(e.setInput=wh),Ih}function Ih(){let e=Yc(this),t=e?.current;if(t){let n=e.previous;if(n===Ae)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function wh(e,t,n,r,o){let i=this.declaredInputs[r],s=Yc(e)||Ch(e,{previous:Ae,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new xi(c&&c.currentValue,n,u===Ae),Wc(e,t,o,n)}var Qc="__ngSimpleChanges__";function Yc(e){return e[Qc]||null}function Ch(e,t){return e[Qc]=t}var Ou=null;var fe=function(e,t,n){Ou?.(e,t,n)},Kc="svg",bh="math";function Te(e){for(;Array.isArray(e);)e=e[Le];return e}function Jc(e,t){return Te(t[e])}function Ee(e,t){return Te(t[e.index])}function js(e,t){return e.data[t]}function Bs(e,t){return e[t]}function Se(e,t){let n=t[e];return qe(n)?n:n[Le]}function _h(e){return(e[y]&4)===4}function Hs(e){return(e[y]&128)===128}function Mh(e){return Ve(e[Z])}function Qe(e,t){return t==null?null:e[t]}function Xc(e){e[Bt]=0}function $s(e){e[y]&1024||(e[y]|=1024,Hs(e)&&An(e))}function Th(e,t){for(;e>0;)t=t[Xt],e--;return t}function Do(e){return!!(e[y]&9216||e[ue]?.dirty)}function Ai(e){e[Oe].changeDetectionScheduler?.notify(9),e[y]&64&&(e[y]|=1024),Do(e)&&An(e)}function An(e){e[Oe].changeDetectionScheduler?.notify(0);let t=gt(e);for(;t!==null&&!(t[y]&8192||(t[y]|=8192,!Hs(t)));)t=gt(t)}function el(e,t){if(xn(e))throw new _(911,!1);e[We]===null&&(e[We]=[]),e[We].push(t)}function Sh(e,t){if(e[We]===null)return;let n=e[We].indexOf(t);n!==-1&&e[We].splice(n,1)}function gt(e){let t=e[Z];return Ve(t)?t[Z]:t}function tl(e){return e[Vr]??=[]}function nl(e){return e.cleanup??=[]}function Nh(e,t,n,r){let o=tl(t);o.push(n),e.firstCreatePass&&nl(e).push(r,o.length-1)}var C={lFrame:cl(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Ri=!1;function xh(){return C.lFrame.elementDepthCount}function Ah(){C.lFrame.elementDepthCount++}function Rh(){C.lFrame.elementDepthCount--}function Eo(){return C.bindingsEnabled}function rl(){return C.skipHydrationRootTNode!==null}function Oh(e){return C.skipHydrationRootTNode===e}function Fh(){C.skipHydrationRootTNode=null}function D(){return C.lFrame.lView}function L(){return C.lFrame.tView}function JT(e){return C.lFrame.contextLView=e,e[z]}function XT(e){return C.lFrame.contextLView=null,e}function Q(){let e=ol();for(;e!==null&&e.type===64;)e=e.parent;return e}function ol(){return C.lFrame.currentTNode}function kh(){let e=C.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function _t(e,t){let n=C.lFrame;n.currentTNode=e,n.isParent=t}function Us(){return C.lFrame.isParent}function zs(){C.lFrame.isParent=!1}function Ph(){return C.lFrame.contextLView}function il(){return Ri}function Hr(e){let t=Ri;return Ri=e,t}function Rn(){let e=C.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Lh(e){return C.lFrame.bindingIndex=e}function Mt(){return C.lFrame.bindingIndex++}function sl(e){let t=C.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Vh(){return C.lFrame.inI18n}function jh(e,t){let n=C.lFrame;n.bindingIndex=n.bindingRootIndex=e,Oi(t)}function Bh(){return C.lFrame.currentDirectiveIndex}function Oi(e){C.lFrame.currentDirectiveIndex=e}function Hh(e){let t=C.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Gs(){return C.lFrame.currentQueryIndex}function Io(e){C.lFrame.currentQueryIndex=e}function $h(e){let t=e[w];return t.type===2?t.declTNode:t.type===1?e[ae]:null}function al(e,t,n){if(n&M.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&M.Host);)if(o=$h(i),o===null||(i=i[Xt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=C.lFrame=ul();return r.currentTNode=t,r.lView=e,!0}function Ws(e){let t=ul(),n=e[w];C.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ul(){let e=C.lFrame,t=e===null?null:e.child;return t===null?cl(e):t}function cl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function ll(){let e=C.lFrame;return C.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var dl=ll;function qs(){let e=ll();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Uh(e){return(C.lFrame.contextLView=Th(e,C.lFrame.contextLView))[z]}function Je(){return C.lFrame.selectedIndex}function mt(e){C.lFrame.selectedIndex=e}function wo(){let e=C.lFrame;return js(e.tView,e.selectedIndex)}function eS(){C.lFrame.currentNamespace=Kc}function tS(){zh()}function zh(){C.lFrame.currentNamespace=null}function Gh(){return C.lFrame.currentNamespace}var fl=!0;function Co(){return fl}function bo(e){fl=e}function Wh(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Zc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Zs(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Sr(e,t,n){pl(e,t,3,n)}function Nr(e,t,n,r){(e[y]&3)===n&&pl(e,t,n,r)}function fi(e,t){let n=e[y];(n&3)===t&&(n&=16383,n+=1,e[y]=n)}function pl(e,t,n,r){let o=r!==void 0?e[Bt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[Bt]+=65536),(a<i||i==-1)&&(qh(e,n,t,u),e[Bt]=(e[Bt]&**********)+u+2),u++}function Fu(e,t){fe(4,e,t);let n=b(null);try{t.call(e)}finally{b(n),fe(5,e,t)}}function qh(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[y]>>14<e[Bt]>>16&&(e[y]&3)===t&&(e[y]+=16384,Fu(a,i)):Fu(a,i)}var zt=-1,yt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Zh(e){return e instanceof yt}function Qh(e){return(e.flags&8)!==0}function Yh(e){return(e.flags&16)!==0}function Kh(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Jh(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function hl(e){return e===3||e===4||e===6}function Jh(e){return e.charCodeAt(0)===64}function Zt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?ku(e,n,o,null,t[++r]):ku(e,n,o,null,null))}}return e}function ku(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var pi={},Fi=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=po(r);let o=this.injector.get(t,pi,r);return o!==pi||n===pi?o:this.parentInjector.get(t,n,r)}};function gl(e){return e!==zt}function $r(e){return e&32767}function Xh(e){return e>>16}function Ur(e,t){let n=Xh(e),r=t;for(;n>0;)r=r[Xt],n--;return r}var ki=!0;function zr(e){let t=ki;return ki=e,t}var eg=256,ml=eg-1,yl=5,tg=0,Me={};function ng(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(yn)&&(r=n[yn]),r==null&&(r=n[yn]=tg++);let o=r&ml,i=1<<o;t.data[e+(o>>yl)]|=i}function Gr(e,t){let n=vl(e,t);if(n!==-1)return n;let r=t[w];r.firstCreatePass&&(e.injectorIndex=t.length,hi(r.data,e),hi(t,null),hi(r.blueprint,null));let o=Qs(e,t),i=e.injectorIndex;if(gl(o)){let s=$r(o),a=Ur(o,t),u=a[w].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function hi(e,t){e.push(0,0,0,0,0,0,0,0,t)}function vl(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Qs(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Cl(o),r===null)return zt;if(n++,o=o[Xt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return zt}function Pi(e,t,n){ng(e,t,n)}function rg(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(hl(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Dl(e,t,n){if(n&M.Optional||e!==void 0)return e;Fs(t,"NodeInjector")}function El(e,t,n,r){if(n&M.Optional&&r===void 0&&(r=null),!(n&(M.Self|M.Host))){let o=e[En],i=se(void 0);try{return o?o.get(t,r,n&M.Optional):xc(t,r,n&M.Optional)}finally{se(i)}}return Dl(r,t,n)}function Il(e,t,n,r=M.Default,o){if(e!==null){if(t[y]&2048&&!(r&M.Self)){let s=ag(e,t,n,r,Me);if(s!==Me)return s}let i=wl(e,t,n,r,Me);if(i!==Me)return i}return El(t,n,r,o)}function wl(e,t,n,r,o){let i=ig(n);if(typeof i=="function"){if(!al(t,e,r))return r&M.Host?Dl(o,n,r):El(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&M.Optional))Fs(n);else return s}finally{dl()}}else if(typeof i=="number"){let s=null,a=vl(e,t),u=zt,c=r&M.Host?t[le][ae]:null;for((a===-1||r&M.SkipSelf)&&(u=a===-1?Qs(e,t):t[a+8],u===zt||!Lu(r,!1)?a=-1:(s=t[w],a=$r(u),t=Ur(u,t)));a!==-1;){let l=t[w];if(Pu(i,a,l.data)){let d=og(a,t,n,s,r,c);if(d!==Me)return d}u=t[a+8],u!==zt&&Lu(r,t[w].data[a+8]===c)&&Pu(i,a,t)?(s=l,a=$r(u),t=Ur(u,t)):a=-1}}return o}function og(e,t,n,r,o,i){let s=t[w],a=s.data[e+8],u=r==null?bt(a)&&ki:r!=s&&(a.type&3)!==0,c=o&M.Host&&i===a,l=xr(a,s,n,u,c);return l!==null?wn(t,s,l,a):Me}function xr(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:c;for(let f=d;f<p;f++){let h=s[f];if(f<u&&n===h||f>=u&&h.type===n)return f}if(o){let f=s[u];if(f&&ke(f)&&f.type===n)return u}return null}function wn(e,t,n,r){let o=e[n],i=t.data;if(Zh(o)){let s=o;s.resolving&&zp(Up(i[n]));let a=zr(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?se(s.injectImpl):null,l=al(e,r,M.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Wh(n,i[n],t)}finally{c!==null&&se(c),zr(a),s.resolving=!1,dl()}}return o}function ig(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(yn)?e[yn]:void 0;return typeof t=="number"?t>=0?t&ml:sg:t}function Pu(e,t,n){let r=1<<e;return!!(n[t+(e>>yl)]&r)}function Lu(e,t){return!(e&M.Self)&&!(e&M.Host&&t)}var lt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Il(this._tNode,this._lView,t,po(r),n)}};function sg(){return new lt(Q(),D())}function nS(e){return Sn(()=>{let t=e.prototype.constructor,n=t[Or]||Li(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Or]||Li(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Li(e){return bc(e)?()=>{let t=Li(U(e));return t&&t()}:dt(e)}function ag(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[y]&2048&&!Br(s);){let a=wl(i,s,n,r|M.Self,Me);if(a!==Me)return a;let u=i.parent;if(!u){let c=s[zc];if(c){let l=c.get(n,Me,r);if(l!==Me)return l}u=Cl(s),s=s[Xt]}i=u}return o}function Cl(e){let t=e[w],n=t.type;return n===2?t.declTNode:n===1?e[ae]:null}function bl(e){return rg(Q(),e)}function Vu(e,t=null,n=null,r){let o=_l(e,t,n,r);return o.resolveInjectorInitializers(),o}function _l(e,t=null,n=null,r,o=new Set){let i=[n||J,ch(e)];return r=r||(typeof e=="object"?void 0:X(e)),new Dn(i,t||go(),r||null,o)}var Ne=class e{static THROW_IF_NOT_FOUND=vn;static NULL=new Lr;static create(t,n){if(Array.isArray(t))return Vu({name:""},n,t,"");{let r=t.name??"";return Vu({name:r},t.parent,t.providers,r)}}static \u0275prov=F({token:e,providedIn:"any",factory:()=>q(Oc)});static __NG_ELEMENT_ID__=-1};var ju=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>bl(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},ug=new S("");ug.__NG_ELEMENT_ID__=e=>{let t=Q();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&M.Optional)return null;throw new _(204,!1)};var Ml=!1,On=(()=>{class e{static __NG_ELEMENT_ID__=cg;static __NG_ENV_ID__=n=>n}return e})(),Wr=class extends On{_lView;constructor(t){super(),this._lView=t}onDestroy(t){return el(this._lView,t),()=>Sh(this._lView,t)}};function cg(){return new Wr(D())}var vt=class{},Ys=new S("",{providedIn:"root",factory:()=>!1});var Tl=new S(""),Sl=new S(""),Fn=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new an(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=F({token:e,providedIn:"root",factory:()=>new e})}return e})();var Vi=class extends oe{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Uc()&&(this.destroyRef=I(On,{optional:!0})??void 0,this.pendingTasks=I(Fn,{optional:!0})??void 0)}emit(t){let n=b(null);try{super.next(t)}finally{b(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof j&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},Ge=Vi;function Cn(...e){}function Nl(e){let t,n;function r(){e=Cn;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Bu(e){return queueMicrotask(()=>e()),()=>{e=Cn}}var Ks="isAngularZone",qr=Ks+"_ID",lg=0,ce=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Ge(!1);onMicrotaskEmpty=new Ge(!1);onStable=new Ge(!1);onError=new Ge(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Ml}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,pg(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ks)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,dg,Cn,Cn);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},dg={};function Js(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function fg(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Nl(()=>{e.callbackScheduled=!1,ji(e),e.isCheckStableRunning=!0,Js(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),ji(e)}function pg(e){let t=()=>{fg(e)},n=lg++;e._inner=e._inner.fork({name:"angular",properties:{[Ks]:!0,[qr]:n,[qr+n]:!0},onInvokeTask:(r,o,i,s,a,u)=>{if(hg(u))return r.invokeTask(i,s,a,u);try{return Hu(e),r.invokeTask(i,s,a,u)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),$u(e)}},onInvoke:(r,o,i,s,a,u,c)=>{try{return Hu(e),r.invoke(i,s,a,u,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!gg(u)&&t(),$u(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ji(e),Js(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ji(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Hu(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function $u(e){e._nesting--,Js(e)}var Bi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Ge;onMicrotaskEmpty=new Ge;onStable=new Ge;onError=new Ge;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function hg(e){return xl(e,"__ignore_ng_zone__")}function gg(e){return xl(e,"__scheduler_tick__")}function xl(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Dt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},mg=new S("",{providedIn:"root",factory:()=>{let e=I(ce),t=I(Dt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Uu(e,t){return Ic(e,t)}function yg(e){return Ic(Ec,e)}var rS=(Uu.required=yg,Uu);function vg(){return en(Q(),D())}function en(e,t){return new Tt(Ee(e,t))}var Tt=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=vg}return e})();function Al(e){return e instanceof Tt?e.nativeElement:e}var zu=new Set;function Ie(e){zu.has(e)||(zu.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Dg(e){return typeof e=="function"&&e[G]!==void 0}function Eg(e,t){Ie("NgSignals");let n=Ka(e),r=n[G];return t?.equal&&(r.equal=t.equal),n.set=o=>Gn(r,o),n.update=o=>Ja(r,o),n.asReadonly=Ig.bind(n),n}function Ig(){let e=this[G];if(e.readonlyFn===void 0){let t=()=>this();t[G]=e,e.readonlyFn=t}return e.readonlyFn}function Rl(e){return Dg(e)&&typeof e.set=="function"}function wg(){return this._results[Symbol.iterator]()}var Hi=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new oe}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=rh(t);(this._changesDetected=!nh(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=wg};function Ol(e){return(e.flags&128)===128}var Fl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Fl||{}),kl=new Map,Cg=0;function bg(){return Cg++}function _g(e){kl.set(e[yo],e)}function $i(e){kl.delete(e[yo])}var Gu="__ngContext__";function St(e,t){qe(t)?(e[Gu]=t[yo],_g(t)):e[Gu]=t}function Pl(e){return Vl(e[In])}function Ll(e){return Vl(e[De])}function Vl(e){for(;e!==null&&!Ve(e);)e=e[De];return e}var Ui;function oS(e){Ui=e}function Mg(){if(Ui!==void 0)return Ui;if(typeof document<"u")return document;throw new _(210,!1)}var iS=new S("",{providedIn:"root",factory:()=>Tg}),Tg="ng",Sg=new S(""),Ng=new S("",{providedIn:"platform",factory:()=>"unknown"});var sS=new S(""),aS=new S("",{providedIn:"root",factory:()=>Mg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var xg="h",Ag="b";var jl=!1,Rg=new S("",{providedIn:"root",factory:()=>jl});var Xs=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Xs||{}),_o=new S("");var Ht=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Ht||{}),Bl=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=F({token:e,providedIn:"root",factory:()=>new e})}return e})(),Og=[Ht.EarlyRead,Ht.Write,Ht.MixedReadWrite,Ht.Read],Fg=(()=>{class e{ngZone=I(ce);scheduler=I(vt);errorHandler=I(Dt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){I(_o,{optional:!0})}execute(){this.executing=!0;for(let n of Og)for(let r of this.sequences)if(!(r.erroredOrDestroyed||!r.hooks[n]))try{r.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>r.hooks[n](r.pipelinedValue),r.snapshot))}catch(o){r.erroredOrDestroyed=!0,this.errorHandler?.handleError(o)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(8),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(7))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Xs.AFTER_NEXT_RENDER,n):n()}static \u0275prov=F({token:e,providedIn:"root",factory:()=>new e})}return e})(),zi=class{impl;hooks;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i=null){this.impl=t,this.hooks=n,this.once=r,this.snapshot=i,this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function kg(e,t){!t?.injector&&mo(kg);let n=t?.injector??I(Ne);return Ie("NgAfterRender"),Hl(e,n,t,!1)}function Pg(e,t){!t?.injector&&mo(Pg);let n=t?.injector??I(Ne);return Ie("NgAfterNextRender"),Hl(e,n,t,!0)}function Lg(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Hl(e,t,n,r){let o=t.get(Bl);o.impl??=t.get(Fg);let i=t.get(_o,null,{optional:!0}),s=n?.phase??Ht.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(On):null,u=new zi(o.impl,Lg(e,s),r,a,i?.snapshot(null));return o.impl.register(u),u}var Vg=()=>null;function $l(e,t,n=!1){return Vg(e,t,n)}function Ul(e,t){let n=e.contentQueries;if(n!==null){let r=b(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Io(i),a.contentQueries(2,t[s],s)}}}finally{b(r)}}}function Gi(e,t,n){Io(0);let r=b(null);try{t(e,n)}finally{b(r)}}function ea(e,t,n){if(Vs(t)){let r=b(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{b(r)}}}var bn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(bn||{}),Ir;function jg(){if(Ir===void 0&&(Ir=null,ze.trustedTypes))try{Ir=ze.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Ir}function Mo(e){return jg()?.createHTML(e)||e}var wr;function Bg(){if(wr===void 0&&(wr=null,ze.trustedTypes))try{wr=ze.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return wr}function Wu(e){return Bg()?.createScriptURL(e)||e}var Pe=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${vc})`}},Wi=class extends Pe{getTypeName(){return"HTML"}},qi=class extends Pe{getTypeName(){return"Style"}},Zi=class extends Pe{getTypeName(){return"Script"}},Qi=class extends Pe{getTypeName(){return"URL"}},Yi=class extends Pe{getTypeName(){return"ResourceURL"}};function kn(e){return e instanceof Pe?e.changingThisBreaksApplicationSecurity:e}function zl(e,t){let n=Hg(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${vc})`)}return n===t}function Hg(e){return e instanceof Pe&&e.getTypeName()||null}function uS(e){return new Wi(e)}function cS(e){return new qi(e)}function lS(e){return new Zi(e)}function dS(e){return new Qi(e)}function fS(e){return new Yi(e)}function $g(e){let t=new Ji(e);return Ug()?new Ki(t):t}var Ki=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Mo(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Ji=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Mo(t),n}};function Ug(){try{return!!new window.DOMParser().parseFromString(Mo(""),"text/html")}catch{return!1}}var zg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Gl(e){return e=String(e),e.match(zg)?e:"unsafe:"+e}function je(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Pn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Wl=je("area,br,col,hr,img,wbr"),ql=je("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Zl=je("rp,rt"),Gg=Pn(Zl,ql),Wg=Pn(ql,je("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),qg=Pn(Zl,je("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),qu=Pn(Wl,Wg,qg,Gg),Ql=je("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Zg=je("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Qg=je("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Yg=Pn(Ql,Zg,Qg),Kg=je("script,style,template"),Xi=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=em(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Xg(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Zu(t).toLowerCase();if(!qu.hasOwnProperty(n))return this.sanitizedSomething=!0,!Kg.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Yg.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;Ql[a]&&(u=Gl(u)),this.buf.push(" ",s,'="',Qu(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=Zu(t).toLowerCase();qu.hasOwnProperty(n)&&!Wl.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Qu(t))}};function Jg(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Xg(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Yl(t);return t}function em(e){let t=e.firstChild;if(t&&Jg(e,t))throw Yl(t);return t}function Zu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Yl(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var tm=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,nm=/([^\#-~ |!])/g;function Qu(e){return e.replace(/&/g,"&amp;").replace(tm,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(nm,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Cr;function pS(e,t){let n=null;try{Cr=Cr||$g(e);let r=t?String(t):"";n=Cr.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Cr.getInertBodyElement(r)}while(r!==i);let a=new Xi().sanitizeChildren(Yu(n)||n);return Mo(a)}finally{if(n){let r=Yu(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Yu(e){return"content"in e&&rm(e)?e.content:null}function rm(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ta=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ta||{});function om(e){let t=Kl();return t?t.sanitize(ta.URL,e)||"":zl(e,"URL")?kn(e):Gl(fo(e))}function im(e){let t=Kl();if(t)return Wu(t.sanitize(ta.RESOURCE_URL,e)||"");if(zl(e,"ResourceURL"))return Wu(kn(e));throw new _(904,!1)}function sm(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?im:om}function hS(e,t,n){return sm(t,n)(e)}function Kl(){let e=D();return e&&e[Oe].sanitizer}var am=/^>|^->|<!--|-->|--!>|<!-$/g,um=/(<|>)/g,cm="\u200B$1\u200B";function lm(e){return e.replace(am,t=>t.replace(um,cm))}function Jl(e){return e instanceof Function?e():e}function dm(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Xl="ng-template";function fm(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&dm(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(na(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function na(e){return e.type===4&&e.value!==Xl}function pm(e,t,n){let r=e.type===4&&!n?Xl:e.value;return t===r}function hm(e,t,n){let r=4,o=e.attrs,i=o!==null?ym(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!ye(r)&&!ye(u))return!1;if(s&&ye(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!pm(e,u,n)||u===""&&t.length===1){if(ye(r))return!1;s=!0}}else if(r&8){if(o===null||!fm(e,o,u,n)){if(ye(r))return!1;s=!0}}else{let c=t[++a],l=gm(u,o,na(e),n);if(l===-1){if(ye(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(ye(r))return!1;s=!0}}}}return ye(r)||s}function ye(e){return(e&1)===0}function gm(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return vm(t,e)}function ed(e,t,n=!1){for(let r=0;r<t.length;r++)if(hm(e,t[r],n))return!0;return!1}function mm(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if(!(n&1))return t[n+1]}return null}function ym(e){for(let t=0;t<e.length;t++){let n=e[t];if(hl(n))return t}return e.length}function vm(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Dm(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Ku(e,t){return e?":not("+t.trim()+")":t}function Em(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ye(s)&&(t+=Ku(i,o),o=""),r=s,i=i||!ye(r);n++}return o!==""&&(t+=Ku(i,o)),t}function Im(e){return e.map(Em).join(",")}function wm(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ye(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var we={};function Cm(e,t){return e.createText(t)}function bm(e,t,n){e.setValue(t,n)}function _m(e,t){return e.createComment(lm(t))}function td(e,t,n){return e.createElement(t,n)}function Zr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function nd(e,t,n){e.appendChild(t,n)}function Ju(e,t,n,r,o){r!==null?Zr(e,t,n,r,o):nd(e,t,n)}function Mm(e,t,n){e.removeChild(null,t,n)}function Tm(e,t,n){e.setAttribute(t,"style",n)}function Sm(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function rd(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Kh(e,t,r),o!==null&&Sm(e,t,o),i!==null&&Tm(e,t,i)}function gS(e=1){od(L(),D(),Je()+e,!1)}function od(e,t,n,r){if(!r)if((t[y]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Sr(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Nr(t,i,0,n)}mt(n)}var Ye=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ye||{});function id(e,t,n,r,o,i){let s=b(null);try{let a=null;o&Ye.SignalBased&&(a=t[r][G]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ye.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Wc(t,a,r,i)}finally{b(s)}}function ra(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[Le]=o,d[y]=r|4|128|8|64|1024,(c!==null||e&&e[y]&2048)&&(d[y]|=2048),Xc(d),d[Z]=d[Xt]=e,d[z]=n,d[Oe]=s||e&&e[Oe],d[P]=a||e&&e[P],d[En]=u||e&&e[En]||null,d[ae]=i,d[yo]=bg(),d[Wt]=l,d[zc]=c,d[le]=t.type==2?e[le]:d,d}function sd(e,t,n,r,o){let i=Je(),s=r&2;try{mt(-1),s&&t.length>$&&od(e,t,$,!1),fe(s?2:0,o),n(r,o)}finally{mt(i),fe(s?3:1,o)}}function oa(e,t,n){Eo()&&(St(Ee(n,t),t),ad(e,t,n))}function ad(e,t,n){Pm(e,t,n),(n.flags&64)===64&&Lm(e,t,n)}function ia(e,t,n=Ee){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Nm(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=sa(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function sa(e,t,n,r,o,i,s,a,u,c,l){let d=$+r,p=d+o,f=xm(d,p),h=typeof c=="function"?c():c;return f[w]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:h,incompleteFirstPass:!1,ssrId:l}}function xm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:we);return n}function Am(e,t,n,r){let i=r.get(Rg,jl)||n===bn.ShadowDom,s=e.selectRootElement(t,i);return Rm(s),s}function Rm(e){Om(e)}var Om=()=>null;function Fm(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function aa(e,t,n,r,o,i,s,a){let u=Ee(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?(la(e,n,l,r,o),bt(t)&&km(n,t.index)):t.type&3?(r=Fm(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function km(e,t){let n=Se(t,e);n[y]&16||(n[y]|=64)}function Pm(e,t,n){let r=n.directiveStart,o=n.directiveEnd;bt(n)&&jm(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Gr(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],u=wn(t,e,s,n);if(St(u,t),i!==null&&$m(t,s-r,u,a,n,i),ke(a)){let c=Se(n.index,t);c[z]=wn(t,e,s,n)}}}function Lm(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Bh();try{mt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];Oi(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&Vm(u,c)}}finally{mt(-1),Oi(s)}}function Vm(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function ua(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];ed(t,i.selectors,!1)&&(r??=[],ke(i)?r.unshift(i):r.push(i))}return r}function ud(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function jm(e,t,n){let r=Ee(t,e),o=Nm(n),i=e[Oe].rendererFactory,s=ca(e,ra(e,o,null,ud(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Bm(e,t,n,r,o,i){let s=Ee(e,t);Hm(t[P],s,i,e.value,n,r,o)}function Hm(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?fo(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function $m(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];id(r,n,u,c,l,d)}}function cd(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function ca(e,t){return e[In]?e[Ru][De]=t:e[In]=t,e[Ru]=t,t}function ld(e,t){let n=e[En],r=n?n.get(Dt,null):null;r&&r.handleError(t)}function la(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];id(l,c,r,a,u,o)}}function Um(e,t){let n=Se(t,e),r=n[w];zm(r,n);let o=n[Le];o!==null&&n[Wt]===null&&(n[Wt]=$l(o,n[En])),da(r,n,n[z])}function zm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function da(e,t,n){Ws(t);try{let r=e.viewQuery;r!==null&&Gi(1,r,n);let o=e.template;o!==null&&sd(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Fe]?.finishViewCreation(e),e.staticContentQueries&&Ul(e,t),e.staticViewQueries&&Gi(2,e.viewQuery,n);let i=e.components;i!==null&&Gm(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[y]&=-5,qs()}}function Gm(e,t){for(let n=0;n<t.length;n++)Um(e,t[n])}var Qr=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Qr||{}),Wm;function fa(e,t){return Wm(e,t)}function pa(e){return(e.flags&32)===32}function $t(e,t,n,r,o){if(r!=null){let i,s=!1;Ve(r)?i=r:qe(r)&&(s=!0,r=r[Le]);let a=Te(r);e===0&&n!==null?o==null?nd(t,n,a):Zr(t,n,a,o||null,!0):e===1&&n!==null?Zr(t,n,a,o||null,!0):e===2?Mm(t,a,s):e===3&&t.destroyNode(a),i!=null&&ry(t,e,i,n,o)}}function qm(e,t){dd(e,t),t[Le]=null,t[ae]=null}function Zm(e,t,n,r,o,i){r[Le]=o,r[ae]=t,No(e,r,n,1,o,i)}function dd(e,t){t[Oe].changeDetectionScheduler?.notify(10),No(e,t,t[P],2,null,null)}function Qm(e){let t=e[In];if(!t)return gi(e[w],e);for(;t;){let n=null;if(qe(t))n=t[In];else{let r=t[W];r&&(n=r)}if(!n){for(;t&&!t[De]&&t!==e;)qe(t)&&gi(t[w],t),t=t[Z];t===null&&(t=e),qe(t)&&gi(t[w],t),n=t&&t[De]}t=n}}function Ym(e,t,n,r){let o=W+r,i=n.length;r>0&&(n[o-1][De]=t),r<i-W?(t[De]=n[o],Rc(n,W+r,t)):(n.push(t),t[De]=null),t[Z]=n;let s=t[ft];s!==null&&n!==s&&fd(s,t);let a=t[Fe];a!==null&&a.insertView(e),Ai(t),t[y]|=128}function fd(e,t){let n=e[qt],r=t[Z];if(qe(r))e[y]|=2;else{let o=r[Z][le];t[le]!==o&&(e[y]|=2)}n===null?e[qt]=[t]:n.push(t)}function ha(e,t){let n=e[qt],r=n.indexOf(t);n.splice(r,1)}function _n(e,t){if(e.length<=W)return;let n=W+t,r=e[n];if(r){let o=r[ft];o!==null&&o!==e&&ha(o,r),t>0&&(e[n-1][De]=r[De]);let i=kr(e,W+t);qm(r[w],r);let s=i[Fe];s!==null&&s.detachView(i[w]),r[Z]=null,r[De]=null,r[y]&=-129}return r}function To(e,t){if(xn(t))return;let n=t[P];n.destroyNode&&No(e,t,n,3,null,null),Qm(t)}function gi(e,t){if(xn(t))return;let n=b(null);try{t[y]&=-129,t[y]|=256,t[ue]&&Ot(t[ue]),Jm(e,t),Km(e,t),t[w].type===1&&t[P].destroy();let r=t[ft];if(r!==null&&Ve(t[Z])){r!==t[Z]&&ha(r,t);let o=t[Fe];o!==null&&o.detachView(e)}$i(t)}finally{b(n)}}function Km(e,t){let n=e.cleanup,r=t[Vr];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Vr]=null);let o=t[We];if(o!==null){t[We]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[pt];if(i!==null){t[pt]=null;for(let s of i)s.destroy()}}function Jm(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof yt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];fe(4,a,u);try{u.call(a)}finally{fe(5,a,u)}}else{fe(4,o,i);try{i.call(o)}finally{fe(5,o,i)}}}}}function pd(e,t,n){return Xm(e,t.parent,n)}function Xm(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Le];if(bt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===bn.None||o===bn.Emulated)return null}return Ee(r,n)}function hd(e,t,n){return ty(e,t,n)}function ey(e,t,n){return e.type&40?Ee(e,n):null}var ty=ey,Xu;function So(e,t,n,r){let o=pd(e,r,t),i=t[P],s=r.parent||t[ae],a=hd(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)Ju(i,o,n[u],a,!1);else Ju(i,o,n,a,!1);Xu!==void 0&&Xu(i,r,t,n,o)}function mn(e,t){if(t!==null){let n=t.type;if(n&3)return Ee(t,e);if(n&4)return es(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return mn(e,r);{let o=e[t.index];return Ve(o)?es(-1,o):Te(o)}}else{if(n&128)return mn(e,t.next);if(n&32)return fa(t,e)()||Te(e[t.index]);{let r=gd(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=gt(e[le]);return mn(o,r)}else return mn(e,t.next)}}}return null}function gd(e,t){if(t!==null){let r=e[le][ae],o=t.projection;return r.projection[o]}return null}function es(e,t){let n=W+e+1;if(n<t.length){let r=t[n],o=r[w].firstChild;if(o!==null)return mn(r,o)}return t[ht]}function ga(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],u=n.type;if(s&&t===0&&(a&&St(Te(a),r),n.flags|=2),!pa(n))if(u&8)ga(e,t,n.child,r,o,i,!1),$t(t,e,o,a,i);else if(u&32){let c=fa(n,r),l;for(;l=c();)$t(t,e,o,l,i);$t(t,e,o,a,i)}else u&16?md(e,t,r,n,o,i):$t(t,e,o,a,i);n=s?n.projectionNext:n.next}}function No(e,t,n,r,o,i){ga(n,r,e.firstChild,t,o,i,!1)}function ny(e,t,n){let r=t[P],o=pd(e,n,t),i=n.parent||t[ae],s=hd(i,n,t);md(r,0,t,n,o,s)}function md(e,t,n,r,o,i){let s=n[le],u=s[ae].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];$t(t,e,o,l,i)}else{let c=u,l=s[Z];Ol(r)&&(c.flags|=128),ga(e,t,c,l,o,i,!0)}}function ry(e,t,n,r,o){let i=n[ht],s=Te(n);i!==s&&$t(t,e,r,i,o);for(let a=W;a<n.length;a++){let u=n[a];No(u[w],u,e,t,r,i)}}function oy(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Qr.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Qr.Important),e.setStyle(n,r,o,i))}}function Ln(e,t,n,r){let o=b(null);try{let i=t.tView,a=e[y]&4096?4096:16,u=ra(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];u[ft]=c;let l=e[Fe];return l!==null&&(u[Fe]=l.createEmbeddedView(i)),da(i,u,n),u}finally{b(o)}}function yd(e,t){let n=W+t;if(n<e.length)return e[n]}function Qt(e,t){return!t||t.firstChild===null||Ol(e)}function Vn(e,t,n,r=!0){let o=t[w];if(Ym(o,t,e,n),r){let s=es(n,e),a=t[P],u=a.parentNode(e[ht]);u!==null&&Zm(o,e[ae],a,t,u,s)}let i=t[Wt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function vd(e,t){let n=_n(e,t);return n!==void 0&&To(n[w],n),n}function Yr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Te(i)),Ve(i)&&iy(i,r);let s=n.type;if(s&8)Yr(e,t,n.child,r);else if(s&32){let a=fa(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=gd(t,n);if(Array.isArray(a))r.push(...a);else{let u=gt(t[le]);Yr(u[w],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function iy(e,t){for(let n=W;n<e.length;n++){let r=e[n],o=r[w].firstChild;o!==null&&Yr(r[w],r,o,t)}e[ht]!==e[Le]&&t.push(e[ht])}var Dd=[];function sy(e){return e[ue]??ay(e)}function ay(e){let t=Dd.pop()??Object.create(cy);return t.lView=e,t}function uy(e){e.lView[ue]!==e&&(e.lView=null,Dd.push(e))}var cy=re(ne({},et),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{An(e.lView)},consumerOnSignalRead(){this.lView[ue]=this}});function ly(e){let t=e[ue]??Object.create(dy);return t.lView=e,t}var dy=re(ne({},et),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=gt(e.lView);for(;t&&!Ed(t[w]);)t=gt(t);t&&$s(t)},consumerOnSignalRead(){this.lView[ue]=this}});function Ed(e){return e.type!==2}function Id(e){if(e[pt]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[pt])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[y]&8192)}}var fy=100;function wd(e,t=!0,n=0){let o=e[Oe].rendererFactory,i=!1;i||o.begin?.();try{py(e,n)}catch(s){throw t&&ld(e,s),s}finally{i||o.end?.()}}function py(e,t){let n=il();try{Hr(!0),ts(e,t);let r=0;for(;Do(e);){if(r===fy)throw new _(103,!1);r++,ts(e,1)}}finally{Hr(n)}}function hy(e,t,n,r){if(xn(t))return;let o=t[y],i=!1,s=!1;Ws(t);let a=!0,u=null,c=null;i||(Ed(e)?(c=sy(t),u=Rt(c)):Ba()===null?(a=!1,c=ly(t),u=Rt(c)):t[ue]&&(Ot(t[ue]),t[ue]=null));try{Xc(t),Lh(e.bindingStartIndex),n!==null&&sd(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Sr(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Nr(t,f,0,null),fi(t,0)}if(s||gy(t),Id(t),Cd(t,0),e.contentQueries!==null&&Ul(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Sr(t,f)}else{let f=e.contentHooks;f!==null&&Nr(t,f,1),fi(t,1)}yy(e,t);let d=e.components;d!==null&&_d(t,d,0);let p=e.viewQuery;if(p!==null&&Gi(2,p,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Sr(t,f)}else{let f=e.viewHooks;f!==null&&Nr(t,f,2),fi(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Tr]){for(let f of t[Tr])f();t[Tr]=null}i||(t[y]&=-73)}catch(l){throw i||An(t),l}finally{c!==null&&(rn(c,u),a&&uy(c)),qs()}}function Cd(e,t){for(let n=Pl(e);n!==null;n=Ll(n))for(let r=W;r<n.length;r++){let o=n[r];bd(o,t)}}function gy(e){for(let t=Pl(e);t!==null;t=Ll(t)){if(!(t[y]&2))continue;let n=t[qt];for(let r=0;r<n.length;r++){let o=n[r];$s(o)}}}function my(e,t,n){let r=Se(t,e);bd(r,n)}function bd(e,t){Hs(e)&&ts(e,t)}function ts(e,t){let r=e[w],o=e[y],i=e[ue],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&on(i)),s||=!1,i&&(i.dirty=!1),e[y]&=-9217,s)hy(r,e,r.template,e[z]);else if(o&8192){Id(e),Cd(e,1);let a=r.components;a!==null&&_d(e,a,1)}}function _d(e,t,n){for(let r=0;r<t.length;r++)my(e,t[r],n)}function yy(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)mt(~o);else{let i=o,s=n[++r],a=n[++r];jh(s,i);let u=t[i];fe(24,u),a(2,u),fe(25,u)}}}finally{mt(-1)}}function ma(e,t){let n=il()?64:1088;for(e[Oe].changeDetectionScheduler?.notify(t);e;){e[y]|=n;let r=gt(e);if(Br(e)&&!r)return e;e=r}return null}var Et=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[w];return Yr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[z]}get dirty(){return!!(this._lView[y]&9280)||!!this._lView[ue]?.dirty}set context(t){this._lView[z]=t}get destroyed(){return xn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Z];if(Ve(t)){let n=t[jr],r=n?n.indexOf(this):-1;r>-1&&(_n(t,r),kr(n,r))}this._attachedToViewContainer=!1}To(this._lView[w],this._lView)}onDestroy(t){el(this._lView,t)}markForCheck(){ma(this._cdRefInjectingView||this._lView,4)}markForRefresh(){$s(this._cdRefInjectingView||this._lView)}detach(){this._lView[y]&=-129}reattach(){Ai(this._lView),this._lView[y]|=128}detectChanges(){this._lView[y]|=1024,wd(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Br(this._lView),n=this._lView[ft];n!==null&&!t&&ha(n,this._lView),dd(this._lView[w],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=Br(this._lView),r=this._lView[ft];r!==null&&!n&&fd(r,this._lView),Ai(this._lView)}},It=(()=>{class e{static __NG_ELEMENT_ID__=Ey}return e})(),vy=It,Dy=class extends vy{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Ln(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Et(o)}};function Ey(){return xo(Q(),D())}function xo(e,t){return e.type&4?new Dy(t,e,en(e,t)):null}function jn(e,t,n,r,o){let i=e.data[t];if(i===null)i=Iy(e,t,n,r,o),Vh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=kh();i.injectorIndex=s===null?-1:s.injectorIndex}return _t(i,!0),i}function Iy(e,t,n,r,o){let i=ol(),s=Us(),a=s?i:i&&i.parent,u=e.data[t]=Cy(e,a,n,t,r,o);return wy(e,u,i,s),u}function wy(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Cy(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return rl()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var yS=new RegExp(`^(\\d+)*(${Ag}|${xg})*(.*)`);var by=()=>null;function Yt(e,t){return by(e,t)}var ns=class{},Kr=class{},rs=class{resolveComponentFactory(t){throw Error(`No component factory found for ${X(t)}.`)}},Kt=class{static NULL=new rs},Jr=class{},ya=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>_y()}return e})();function _y(){let e=D(),t=Q(),n=Se(t.index,e);return(qe(n)?n:e)[P]}var My=(()=>{class e{static \u0275prov=F({token:e,providedIn:"root",factory:()=>null})}return e})();function os(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ci(o,a);else if(i==2){let u=a,c=t[++s];r=Ci(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Y(e,t=M.Default){let n=D();if(n===null)return q(e,t);let r=Q();return Il(r,n,U(e),t)}function vS(){let e="invalid";throw new Error(e)}function Md(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function va(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let[a,u]=Sy(e,n,s);xy(e,t,n,a,i,u)}i!==null&&r!==null&&Ty(n,r,i)}function Ty(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function Sy(e,t,n){let r=[],o=null;for(let i of n)i.findHostDirectiveDefs!==null&&(o??=new Map,i.findHostDirectiveDefs(i,r,o)),ke(i)&&(r.push(i),Ny(e,t,r.length-1));return bt(t)?r.push(...n.slice(1)):r.push(...n),[r,o]}function Ny(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function xy(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Pi(Gr(n,t),e,r[c].type);Ly(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=Md(e,t,r.length,null);for(let c=0;c<r.length;c++){let l=r[c];n.mergedAttrs=Zt(n.mergedAttrs,l.hostAttrs),Oy(e,n,t,u,l),Py(u,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}Ay(e,n,i)}function Ay(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;u=ec(0,d.inputs,l,u,f),c=ec(1,d.outputs,l,c,h);let g=u!==null&&s!==null&&!na(t)?Ry(u,l,s):null;a.push(g)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function ec(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,u=Ye.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?tc(r,n,c,a,u):tc(r,n,c,a)}return r}function tc(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function Ry(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function Oy(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=dt(o.type,!0)),s=new yt(i,ke(o),Y);e.blueprint[r]=s,n[r]=s,Fy(e,t,r,Md(e,n,o.hostVars,we),o)}function Fy(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ky(s)!=a&&s.push(a),s.push(n,r,i)}}function ky(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Py(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;ke(t)&&(n[""]=e)}}function Ly(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Td(e,t,n,r,o,i,s,a){let u=t.consts,c=Qe(u,s),l=jn(t,e,2,r,c);return i&&va(t,n,l,Qe(u,a),o),l.mergedAttrs=Zt(l.mergedAttrs,l.attrs),l.attrs!==null&&os(l,l.attrs,!1),l.mergedAttrs!==null&&os(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function Sd(e,t){Zs(e,t),Vs(t)&&e.queries.elementEnd(t)}var Xr=class extends Kt{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Re(t);return new wt(n,this.ngModule)}};function nc(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:Ye.None;t?n.push({propName:s,templateName:r,isSignal:(a&Ye.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function Vy(e,t,n){let r=t instanceof Ze?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Fi(n,r):n}function jy(e){let t=e.get(Jr,null);if(t===null)throw new _(407,!1);let n=e.get(My,null),r=e.get(vt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function By(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return td(t,n,n==="svg"?Kc:n==="math"?bh:null)}var wt=class extends Kr{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;get inputs(){let t=this.componentDef,n=t.inputTransforms,r=nc(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return nc(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Im(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){let i=b(null);try{let s=this.componentDef,a=r?["ng-version","19.1.5"]:wm(this.componentDef.selectors[0]),u=sa(0,null,null,1,0,null,null,null,null,[a],null),c=Vy(s,o||this.ngModule,t),l=jy(c),d=l.rendererFactory.createRenderer(null,s),p=r?Am(d,r,s.encapsulation,c):By(s,d),f=ra(null,u,null,512|ud(s),null,null,l,d,c,null,$l(p,c,!0));f[$]=p,Ws(f);let h=null;try{let g=Td($,u,f,"#host",()=>[this.componentDef],!0,0);p&&(rd(d,p,g),St(p,f)),ad(u,f,g),ea(u,g,f),Sd(u,g),n!==void 0&&Hy(g,this.ngContentSelectors,n),h=Se(g.index,f),f[z]=h[z],da(u,f,null)}catch(g){throw h!==null&&$i(h),$i(f),g}finally{qs()}return new is(this.componentType,f)}finally{b(i)}}},is=class extends ns{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=js(n[w],$),this.location=en(this._tNode,n),this.instance=Se(this._tNode.index,n)[z],this.hostView=this.changeDetectorRef=new Et(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;la(i[w],i,o,t,n),this.previousInputValues.set(t,n);let s=Se(this._tNode.index,i);ma(s,1)}}get injector(){return new lt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Hy(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Nt=(()=>{class e{static __NG_ELEMENT_ID__=$y}return e})();function $y(){let e=Q();return xd(e,D())}var Uy=Nt,Nd=class extends Uy{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return en(this._hostTNode,this._hostLView)}get injector(){return new lt(this._hostTNode,this._hostLView)}get parentInjector(){let t=Qs(this._hostTNode,this._hostLView);if(gl(t)){let n=Ur(t,this._hostLView),r=$r(t),o=n[w].data[r+8];return new lt(o,n)}else return new lt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=rc(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-W}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Yt(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Qt(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Eh(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let u=s?t:new wt(Re(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let g=(s?c:this.parentInjector).get(Ze,null);g&&(i=g)}let l=Re(u.componentType??{}),d=Yt(this._lContainer,l?.id??null),p=d?.firstChild??null,f=u.create(c,o,p,i);return this.insertImpl(f.hostView,a,Qt(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Mh(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[Z],c=new Nd(u,u[ae],u[Z]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Vn(s,o,i,r),t.attachToViewContainerRef(),Rc(mi(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=rc(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=_n(this._lContainer,n);r&&(kr(mi(this._lContainer),n),To(r[w],r))}detach(t){let n=this._adjustIndex(t,-1),r=_n(this._lContainer,n);return r&&kr(mi(this._lContainer),n)!=null?new Et(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function rc(e){return e[jr]}function mi(e){return e[jr]||(e[jr]=[])}function xd(e,t){let n,r=t[e.index];return Ve(r)?n=r:(n=cd(r,t,null,e),t[e.index]=n,ca(t,n)),Gy(n,t,e,r),new Nd(n,e,t)}function zy(e,t){let n=e[P],r=n.createComment(""),o=Ee(t,e),i=n.parentNode(o);return Zr(n,i,r,n.nextSibling(o),!1),r}var Gy=Zy,Wy=()=>!1;function qy(e,t,n){return Wy(e,t,n)}function Zy(e,t,n,r){if(e[ht])return;let o;n.type&8?o=Te(r):o=zy(t,n),e[ht]=o}var ss=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},as=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Ea(t,n).matches!==null&&this.queries[n].setDirty()}},eo=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=ev(t):this.predicate=t}},us=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},cs=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Qy(n,i)),this.matchTNodeWithReadOption(t,n,xr(n,t,i,!1,!1))}else r===It?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Tt||o===Nt||o===It&&n.type&4)this.addMatch(n.index,-2);else{let i=xr(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Qy(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Yy(e,t){return e.type&11?en(e,t):e.type&4?xo(e,t):null}function Ky(e,t,n,r){return n===-1?Yy(t,e):n===-2?Jy(e,t,r):wn(e,e[w],n,t)}function Jy(e,t,n){if(n===Tt)return en(t,e);if(n===It)return xo(t,e);if(n===Nt)return xd(t,e)}function Ad(e,t,n,r){let o=t[Fe].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push(Ky(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function ls(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Ad(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=W;d<l.length;d++){let p=l[d];p[ft]===p[Z]&&ls(p[w],p,c,r)}if(l[qt]!==null){let d=l[qt];for(let p=0;p<d.length;p++){let f=d[p];ls(f[w],f,c,r)}}}}}return r}function Da(e,t){return e[Fe].queries[t].queryList}function Rd(e,t,n){let r=new Hi((n&4)===4);return Nh(e,t,r,r.destroy),(t[Fe]??=new as).queries.push(new ss(r))-1}function Xy(e,t,n){let r=L();return r.firstCreatePass&&(Fd(r,new eo(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Rd(r,D(),t)}function Od(e,t,n,r){let o=L();if(o.firstCreatePass){let i=Q();Fd(o,new eo(t,n,r),i.index),tv(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Rd(o,D(),n)}function ev(e){return e.split(",").map(t=>t.trim())}function Fd(e,t,n){e.queries===null&&(e.queries=new us),e.queries.track(new cs(t,n))}function tv(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Ea(e,t){return e.queries.getByIndex(t)}function kd(e,t){let n=e[w],r=Ea(n,t);return r.crossesNgTemplate?ls(n,e,t,[]):Ad(n,e,r,t)}function Pd(e,t,n){let r,o=qo(()=>{r._dirtyCounter();let i=iv(r,e);if(t&&i===void 0)throw new _(-951,!1);return i});return r=o[G],r._dirtyCounter=Eg(0),r._flatValue=void 0,o}function nv(e){return Pd(!0,!1,e)}function rv(e){return Pd(!0,!0,e)}function ov(e,t){let n=e[G];n._lView=D(),n._queryIndex=t,n._queryList=Da(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function iv(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[y]&4)return t?void 0:J;let o=Da(n,r),i=kd(n,r);return o.reset(i,Al),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function oc(e,t){return nv(t)}function sv(e,t){return rv(t)}var ES=(oc.required=sv,oc);var Ke=class{},ds=class{};var fs=class extends Ke{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Xr(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=kc(t);this._bootstrapComponents=Jl(i.bootstrap),this._r3Injector=_l(t,n,[{provide:Ke,useValue:this},{provide:Kt,useValue:this.componentFactoryResolver},...r],X(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ps=class extends ds{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new fs(this.moduleType,t,[])}};var to=class extends Ke{injector;componentFactoryResolver=new Xr(this);instance=null;constructor(t){super();let n=new Dn([...t.providers,{provide:Ke,useValue:this},{provide:Kt,useValue:this.componentFactoryResolver}],t.parent||go(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function av(e,t,n=null){return new to({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var uv=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Lc(!1,n.type),o=r.length>0?av([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=F({token:e,providedIn:"environment",factory:()=>new e(q(Ze))})}return e})();function IS(e){return Sn(()=>{let t=Vd(e),n=re(ne({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Fl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(uv).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||bn.Emulated,styles:e.styles||J,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Ie("NgStandalone"),jd(n);let r=e.dependencies;return n.directiveDefs=sc(r,!1),n.pipeDefs=sc(r,!0),n.id=dv(n),n})}function cv(e){return Re(e)||Ps(e)}function lv(e){return e!==null}function Ld(e){return Sn(()=>({type:e.type,bootstrap:e.bootstrap||J,declarations:e.declarations||J,imports:e.imports||J,exports:e.exports||J,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ic(e,t){if(e==null)return Ae;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ye.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ye.None?[r,a]:r,t[i]=s):n[i]=r}return n}function Bn(e){return Sn(()=>{let t=Vd(e);return jd(t),t})}function Ia(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Vd(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Ae,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||J,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ic(e.inputs,t),outputs:ic(e.outputs),debugInfo:null}}function jd(e){e.features?.forEach(t=>t(e))}function sc(e,t){if(!e)return null;let n=t?Pc:cv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(lv)}function dv(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function fv(e){return Object.getPrototypeOf(e.prototype).constructor}function pv(e){let t=fv(e.type),n=!0,r=[e];for(;t;){let o;if(ke(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=br(e.inputs),s.inputTransforms=br(e.inputTransforms),s.declaredInputs=br(e.declaredInputs),s.outputs=br(e.outputs);let a=o.hostBindings;a&&vv(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&mv(e,u),c&&yv(e,c),hv(e,o),kp(e.outputs,o.outputs),ke(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===pv&&(n=!1)}}t=Object.getPrototypeOf(t)}gv(r)}function hv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function gv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Zt(o.hostAttrs,n=Zt(n,o.hostAttrs))}}function br(e){return e===Ae?{}:e===J?[]:e}function mv(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function yv(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function vv(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function wS(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=Bd,n.hostDirectives=r?e.map(hs):[e]):r?n.hostDirectives.unshift(...e.map(hs)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function Bd(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)ac(hs(i),t,n)}else ac(r,t,n)}function ac(e,t,n){let r=Ps(e.directive);Dv(r.declaredInputs,e.inputs),Bd(r,t,n),n.set(r,e),t.push(r)}function hs(e){return typeof e=="function"?{directive:U(e),inputs:Ae,outputs:Ae}:{directive:U(e.directive),inputs:uc(e.inputs),outputs:uc(e.outputs)}}function uc(e){if(e===void 0||e.length===0)return Ae;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function Dv(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function Ev(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}function Hd(e){return wv(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Iv(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function wv(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function wa(e,t,n){return e[t]=n}function Cv(e,t){return e[t]}function pe(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function bv(e,t,n,r){let o=pe(e,t,n);return pe(e,t+1,r)||o}function _v(e,t,n,r,o,i,s,a,u){let c=t.consts,l=jn(t,e,4,s||null,a||null);Eo()&&va(t,n,l,Qe(c,u),ua),l.mergedAttrs=Zt(l.mergedAttrs,l.attrs),Zs(t,l);let d=l.tView=sa(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function no(e,t,n,r,o,i,s,a,u,c){let l=n+$,d=t.firstCreatePass?_v(l,t,e,r,o,i,s,a,u):t.data[l];_t(d,!1);let p=Tv(t,e,d,n);Co()&&So(t,e,p,d),St(p,e);let f=cd(p,e,p,d);return e[l]=f,ca(e,f),qy(f,d,e),vo(d)&&oa(t,e,d),u!=null&&ia(e,d,c),d}function Mv(e,t,n,r,o,i,s,a){let u=D(),c=L(),l=Qe(c.consts,i);return no(u,c,e,t,n,r,o,l,s,a),Mv}var Tv=Sv;function Sv(e,t,n,r){return bo(!0),t[P].createComment("")}var CS=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Nv=new S("");function Ca(e){return!!e&&typeof e.then=="function"}function $d(e){return!!e&&typeof e.subscribe=="function"}var xv=new S("");var Ud=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=I(xv,{optional:!0})??[];injector=I(Ne);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=$c(this.injector,o);if(Ca(i))n.push(i);else if($d(i)){let s=new Promise((a,u)=>{i.subscribe({complete:a,error:u})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),zd=(()=>{class e{static \u0275prov=F({token:e,providedIn:"root",factory:()=>new ro})}return e})(),ro=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}},Av=new S("");function Rv(){Ya(()=>{throw new _(600,!1)})}function Ov(e){return e.isBoundToModule}var Fv=10;var Mn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=I(mg);afterRenderManager=I(Bl);zonelessEnabled=I(Ys);rootEffectScheduler=I(zd);dirtyFlags=0;deferredDirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new oe;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=I(Fn).hasPendingTasks.pipe($e(n=>!n));constructor(){I(_o,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=I(Ze);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Kr;if(!this._injector.get(Ud).done){let p=!o&&ah(n),f=!1;throw new _(405,f)}let s;o?s=n:s=this._injector.get(Kt).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=Ov(s)?void 0:this._injector.get(Ke),u=r||s.selector,c=s.create(Ne.NULL,[],u,a),l=c.location.nativeElement,d=c.injector.get(Nv,null);return d?.registerApplication(l),c.onDestroy(()=>{this.detachView(c.hostView),Ar(this.components,c),d?.unregisterApplication(l)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick=()=>{if(this.tracingSnapshot!==null){let r=this.tracingSnapshot;this.tracingSnapshot=null,r.run(Xs.CHANGE_DETECTION,this._tick),r.dispose();return}if(this._runningTick)throw new _(101,!1);let n=b(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,b(n),this.afterTick.next()}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Jr,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let n=0;for(;this.dirtyFlags!==0&&n++<Fv;)this.synchronizeOnce()}synchronizeOnce(){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)kv(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Do(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Ar(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Av,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ar(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ar(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function kv(e,t,n,r){if(!n&&!Do(e))return;wd(e,t,n&&!r?0:1)}function Pv(e,t,n,r){let o=D(),i=Mt();if(pe(o,i,t)){let s=L(),a=wo();Bm(a,o,e,t,n,r)}return Pv}function Gd(e,t,n,r){return pe(e,Mt(),n)?t+fo(n)+r:we}function _r(e,t){return e<<17|t<<2}function Ct(e){return e>>17&32767}function Lv(e){return(e&2)==2}function Vv(e,t){return e&131071|t<<17}function gs(e){return e|2}function Jt(e){return(e&131068)>>2}function yi(e,t){return e&-131069|t<<2}function jv(e){return(e&1)===1}function ms(e){return e|1}function Bv(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Ct(s),u=Jt(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Nn(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let p=Ct(e[a+1]);e[r+1]=_r(p,a),p!==0&&(e[p+1]=yi(e[p+1],r)),e[a+1]=Vv(e[a+1],r)}else e[r+1]=_r(a,0),a!==0&&(e[a+1]=yi(e[a+1],r)),a=r;else e[r+1]=_r(u,0),a===0?a=r:e[u+1]=yi(e[u+1],r),u=r;c&&(e[r+1]=gs(e[r+1])),cc(e,l,r,!0),cc(e,l,r,!1),Hv(t,l,e,r,i),s=_r(a,u),i?t.classBindings=s:t.styleBindings=s}function Hv(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Nn(i,t)>=0&&(n[r+1]=ms(n[r+1]))}function cc(e,t,n,r){let o=e[n+1],i=t===null,s=r?Ct(o):Jt(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];$v(u,t)&&(a=!0,e[s+1]=r?ms(c):gs(c)),s=r?Ct(c):Jt(c)}a&&(e[n+1]=r?gs(o):ms(o))}function $v(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Nn(e,t)>=0:!1}var ve={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Uv(e){return e.substring(ve.key,ve.keyEnd)}function zv(e){return Gv(e),Wd(e,qd(e,0,ve.textEnd))}function Wd(e,t){let n=ve.textEnd;return n===t?-1:(t=ve.keyEnd=Wv(e,ve.key=t,n),qd(e,t,n))}function Gv(e){ve.key=0,ve.keyEnd=0,ve.value=0,ve.valueEnd=0,ve.textEnd=e.length}function qd(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Wv(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function qv(e,t,n){let r=D(),o=Mt();if(pe(r,o,t)){let i=L(),s=wo();aa(i,s,r,e,t,r[P],n,!1)}return qv}function ys(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";la(e,n,i[s],s,r)}function Zd(e,t,n){return Yd(e,t,n,!1),Zd}function Zv(e,t){return Yd(e,t,null,!0),Zv}function bS(e){Kd(eD,Qd,e,!0)}function Qd(e,t){for(let n=zv(t);n>=0;n=Wd(t,n))ho(e,Uv(t),!0)}function Yd(e,t,n,r){let o=D(),i=L(),s=sl(2);if(i.firstUpdatePass&&Xd(i,e,s,r),t!==we&&pe(o,s,t)){let a=i.data[Je()];ef(i,a,o,o[P],e,o[s+1]=nD(t,n),r,s)}}function Kd(e,t,n,r){let o=L(),i=sl(2);o.firstUpdatePass&&Xd(o,null,i,r);let s=D();if(n!==we&&pe(s,i,n)){let a=o.data[Je()];if(tf(a,r)&&!Jd(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=Ci(u,n||"")),ys(o,a,s,n,r)}else tD(o,a,s,s[P],s[i+1],s[i+1]=Xv(e,t,n),r,i)}}function Jd(e,t){return t>=e.expandoStartIndex}function Xd(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Je()],s=Jd(e,n);tf(i,r)&&t===null&&!s&&(t=!1),t=Qv(o,i,t,r),Bv(o,i,t,n,s,r)}}function Qv(e,t,n,r){let o=Hh(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=vi(null,e,t,n,r),n=Tn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=vi(o,e,t,n,r),i===null){let u=Yv(e,t,r);u!==void 0&&Array.isArray(u)&&(u=vi(null,e,t,u[1],r),u=Tn(u,t.attrs,r),Kv(e,t,r,u))}else i=Jv(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function Yv(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Jt(r)!==0)return e[Ct(r)]}function Kv(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Ct(o)]=r}function Jv(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Tn(r,s,n)}return Tn(r,t.attrs,n)}function vi(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Tn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Tn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ho(e,s,n?!0:t[++i]))}return e===void 0?null:e}function Xv(e,t,n){if(n==null||n==="")return J;let r=[],o=kn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function eD(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ho(e,r,n)}function tD(e,t,n,r,o,i,s,a){o===we&&(o=J);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=u<o.length?o[u+1]:void 0,f=c<i.length?i[c+1]:void 0,h=null,g;l===d?(u+=2,c+=2,p!==f&&(h=d,g=f)):d===null||l!==null&&l<d?(u+=2,h=l):(c+=2,h=d,g=f),h!==null&&ef(e,t,n,r,h,g,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function ef(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=jv(c)?lc(u,t,n,o,Jt(c),s):void 0;if(!oo(l)){oo(i)||Lv(c)&&(i=lc(u,null,n,o,a,s));let d=Jc(Je(),n);oy(r,s,d,o,i)}}function lc(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,p=n[o+1];p===we&&(p=d?J:void 0);let f=d?li(p,r):l===r?p:void 0;if(c&&!oo(f)&&(f=li(u,r)),oo(f)&&(a=f,s))return a;let h=e[o+1];o=s?Ct(h):Jt(h)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=li(u,r))}return a}function oo(e){return e!==void 0}function nD(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=X(kn(e)))),e}function tf(e,t){return(e.flags&(t?8:16))!==0}function _S(e,t,n){let r=D(),o=Gd(r,e,t,n);Kd(ho,Qd,o,!0)}var vs=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Di(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function rD(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let u=t.length-1;for(;i<=s&&i<=u;){let c=e.at(i),l=t[i],d=Di(i,c,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let p=e.at(s),f=t[u],h=Di(s,p,u,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,u--;continue}let g=n(i,c),O=n(s,p),N=n(i,l);if(Object.is(N,O)){let te=n(u,f);Object.is(te,g)?(e.swap(i,s),e.updateValue(s,f),u--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new io,o??=fc(e,i,s,n),Ds(e,r,i,N))e.updateValue(i,l),i++,s++;else if(o.has(N))r.set(g,e.detach(i)),s--;else{let te=e.create(i,t[i]);e.attach(i,te),i++,s++}}for(;i<=u;)dc(e,r,n,i,t[i]),i++}else if(t!=null){let u=t[Symbol.iterator](),c=u.next();for(;!c.done&&i<=s;){let l=e.at(i),d=c.value,p=Di(i,l,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,c=u.next();else{r??=new io,o??=fc(e,i,s,n);let f=n(i,d);if(Ds(e,r,i,f))e.updateValue(i,d),i++,s++,c=u.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,c=u.next();else{let h=n(i,l);r.set(h,e.detach(i)),s--}}}for(;!c.done;)dc(e,r,n,e.length,c.value),c=u.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(u=>{e.destroy(u)})}function Ds(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function dc(e,t,n,r,o){if(Ds(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function fc(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var io=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function MS(e,t){Ie("NgControlFlow");let n=D(),r=Mt(),o=n[r]!==we?n[r]:-1,i=o!==-1?so(n,$+o):void 0,s=0;if(pe(n,r,e)){let a=b(null);try{if(i!==void 0&&vd(i,s),e!==-1){let u=$+e,c=so(n,u),l=Cs(n[w],u),d=Yt(c,l.tView.ssrId),p=Ln(n,l,t,{dehydratedView:d});Vn(c,p,s,Qt(l,d))}}finally{b(a)}}else if(i!==void 0){let a=yd(i,s);a!==void 0&&(a[z]=t)}}var Es=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-W}};function TS(e,t){return t}var Is=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function SS(e,t,n,r,o,i,s,a,u,c,l,d,p){Ie("NgControlFlow");let f=D(),h=L(),g=u!==void 0,O=D(),N=a?s.bind(O[le][z]):s,te=new Is(g,N);O[$+e]=te,no(f,h,e+1,t,n,r,o,Qe(h.consts,i)),g&&no(f,h,e+2,u,c,l,d,Qe(h.consts,p))}var ws=class extends vs{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-W}at(t){return this.getLView(t)[z].$implicit}attach(t,n){let r=n[Wt];this.needsIndexUpdate||=t!==this.length,Vn(this.lContainer,n,t,Qt(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,oD(this.lContainer,t)}create(t,n){let r=Yt(this.lContainer,this.templateTNode.tView.ssrId),o=Ln(this.hostLView,this.templateTNode,new Es(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){To(t[w],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[z].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[z].$index=t}getLView(t){return iD(this.lContainer,t)}};function NS(e){let t=b(null),n=Je();try{let r=D(),o=r[w],i=r[n],s=n+1,a=so(r,s);if(i.liveCollection===void 0){let c=Cs(o,s);i.liveCollection=new ws(a,r,c)}else i.liveCollection.reset();let u=i.liveCollection;if(rD(u,e,i.trackByFn),u.updateIndexes(),i.hasEmptyBlock){let c=Mt(),l=u.length===0;if(pe(r,c,l)){let d=n+2,p=so(r,d);if(l){let f=Cs(o,d),h=Yt(p,f.tView.ssrId),g=Ln(r,f,void 0,{dehydratedView:h});Vn(p,g,0,Qt(f,h))}else vd(p,0)}}}finally{b(t)}}function so(e,t){return e[t]}function oD(e,t){return _n(e,t)}function iD(e,t){return yd(e,t)}function Cs(e,t){return js(e,t)}function nf(e,t,n,r){let o=D(),i=L(),s=$+e,a=o[P],u=i.firstCreatePass?Td(s,i,o,t,ua,Eo(),n,r):i.data[s],c=aD(i,o,u,a,t,e);o[s]=c;let l=vo(u);return _t(u,!0),rd(a,c,u),!pa(u)&&Co()&&So(i,o,c,u),xh()===0&&St(c,o),Ah(),l&&(oa(i,o,u),ea(i,u,o)),r!==null&&ia(o,u),nf}function rf(){let e=Q();Us()?zs():(e=e.parent,_t(e,!1));let t=e;Oh(t)&&Fh(),Rh();let n=L();return n.firstCreatePass&&Sd(n,t),t.classesWithoutHost!=null&&Qh(t)&&ys(n,t,D(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Yh(t)&&ys(n,t,D(),t.stylesWithoutHost,!1),rf}function sD(e,t,n,r){return nf(e,t,n,r),rf(),sD}var aD=(e,t,n,r,o,i)=>(bo(!0),td(r,o,Gh()));function uD(e,t,n,r,o){let i=t.consts,s=Qe(i,r),a=jn(t,e,8,"ng-container",s);s!==null&&os(a,s,!0);let u=Qe(i,o);return Eo()&&va(t,n,a,u,ua),a.mergedAttrs=Zt(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function of(e,t,n){let r=D(),o=L(),i=e+$,s=o.firstCreatePass?uD(i,o,r,t,n):o.data[i];_t(s,!0);let a=lD(o,r,s,e);return r[i]=a,Co()&&So(o,r,a,s),St(a,r),vo(s)&&(oa(o,r,s),ea(o,s,r)),n!=null&&ia(r,s),of}function sf(){let e=Q(),t=L();return Us()?zs():(e=e.parent,_t(e,!1)),t.firstCreatePass&&(Zs(t,e),Vs(e)&&t.queries.elementEnd(e)),sf}function cD(e,t,n){return of(e,t,n),sf(),cD}var lD=(e,t,n,r)=>(bo(!0),_m(t[P],""));function xS(){return D()}function dD(e,t,n){let r=D(),o=Mt();if(pe(r,o,t)){let i=L(),s=wo();aa(i,s,r,e,t,r[P],n,!0)}return dD}var ct=void 0;function fD(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var pD=["en",[["a","p"],["AM","PM"],ct],[["AM","PM"],ct,ct],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],ct,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],ct,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",ct,"{1} 'at' {0}",ct],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",fD],Ei={};function he(e){let t=hD(e),n=pc(t);if(n)return n;let r=t.split("-")[0];if(n=pc(r),n)return n;if(r==="en")return pD;throw new _(701,!1)}function pc(e){return e in Ei||(Ei[e]=ze.ng&&ze.ng.common&&ze.ng.common.locales&&ze.ng.common.locales[e]),Ei[e]}var V=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(V||{});function hD(e){return e.toLowerCase().replace(/_/g,"-")}var ao="en-US";var gD=ao;function mD(e){typeof e=="string"&&(gD=e.toLowerCase().replace(/_/g,"-"))}var yD=(e,t,n)=>{};function vD(e,t,n,r){let o=D(),i=L(),s=Q();return af(i,o,o[P],s,e,t,r),vD}function DD(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Vr],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function af(e,t,n,r,o,i,s){let a=vo(r),c=e.firstCreatePass&&nl(e),l=t[z],d=tl(t),p=!0;if(r.type&3||s){let g=Ee(r,t),O=s?s(g):g,N=d.length,te=s?At=>s(Te(At[r.index])):r.index,xt=null;if(!s&&a&&(xt=DD(e,t,o,r.index)),xt!==null){let At=xt.__ngLastListenerFn__||xt;At.__ngNextListenerFn__=i,xt.__ngLastListenerFn__=i,p=!1}else{i=gc(r,t,l,i),yD(g,o,i);let At=n.listen(O,o,i);d.push(i,At),c&&c.push(o,te,N,N+1)}}else i=gc(r,t,l,i);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let g=h.length;if(g)for(let O=0;O<g;O+=2){let N=h[O],te=h[O+1],Rf=t[N][te].subscribe(i),La=d.length;d.push(i,Rf),c&&c.push(o,r.index,La,-(La+1))}}}function hc(e,t,n,r){let o=b(null);try{return fe(6,t,n),n(r)!==!1}catch(i){return ld(e,i),!1}finally{fe(7,t,n),b(o)}}function gc(e,t,n,r){return function o(i){if(i===Function)return r;let s=bt(e)?Se(e.index,t):t;ma(s,5);let a=hc(t,n,r,i),u=o.__ngNextListenerFn__;for(;u;)a=hc(t,n,u,i)&&a,u=u.__ngNextListenerFn__;return a}}function AS(e=1){return Uh(e)}function ED(e,t){let n=null,r=mm(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?ed(e,i,!0):Dm(r,i))return o}return n}function RS(e){let t=D()[le][ae];if(!t.projection){let n=e?e.length:1,r=t.projection=oh(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?ED(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function OS(e,t=0,n,r,o,i){let s=D(),a=L(),u=r?e+1:null;u!==null&&no(s,a,u,r,o,i,null,n);let c=jn(a,$+e,16,null,n||null);c.projection===null&&(c.projection=t),zs();let d=!s[Wt]||rl();s[le][ae].projection[c.projection]===null&&u!==null?ID(s,a,u):d&&!pa(c)&&ny(a,s,c)}function ID(e,t,n){let r=$+n,o=t.data[r],i=e[r],s=Yt(i,o.tView.ssrId),a=Ln(e,o,void 0,{dehydratedView:s});Vn(i,a,0,Qt(o,s))}function FS(e,t,n,r){Od(e,t,n,r)}function kS(e,t,n){Xy(e,t,n)}function PS(e){let t=D(),n=L(),r=Gs();Io(r+1);let o=Ea(n,r);if(e.dirty&&_h(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=kd(t,r);e.reset(i,Al),e.notifyOnChanges()}return!0}return!1}function LS(){return Da(D(),Gs())}function VS(e,t,n,r,o){ov(t,Od(e,n,r,o))}function jS(e=1){Io(Gs()+e)}function wD(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function BS(e){let t=Ph();return Bs(t,$+e)}function HS(e,t=""){let n=D(),r=L(),o=e+$,i=r.firstCreatePass?jn(r,o,1,t,null):r.data[o],s=CD(r,n,i,t,e);n[o]=s,Co()&&So(r,n,s,i),_t(i,!1)}var CD=(e,t,n,r,o)=>(bo(!0),Cm(t[P],r));function bD(e){return uf("",e,""),bD}function uf(e,t,n){let r=D(),o=Gd(r,e,t,n);return o!==we&&_D(r,Je(),o),uf}function _D(e,t,n){let r=Jc(t,e);bm(e[P],r,n)}function MD(e,t,n){Rl(t)&&(t=t());let r=D(),o=Mt();if(pe(r,o,t)){let i=L(),s=wo();aa(i,s,r,e,t,r[P],n,!1)}return MD}function $S(e,t){let n=Rl(e);return n&&e.set(t),n}function TD(e,t){let n=D(),r=L(),o=Q();return af(r,n,n[P],o,e,t),TD}function SD(e,t,n){let r=L();if(r.firstCreatePass){let o=ke(e);bs(n,r.data,r.blueprint,o,!0),bs(t,r.data,r.blueprint,o,!1)}}function bs(e,t,n,r,o){if(e=U(e),Array.isArray(e))for(let i=0;i<e.length;i++)bs(e[i],t,n,r,o);else{let i=L(),s=D(),a=Q(),u=Gt(e)?e:U(e.provide),c=Hc(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Gt(e)||!e.multi){let f=new yt(c,o,Y),h=wi(u,t,o?l:l+p,d);h===-1?(Pi(Gr(a,s),i,u),Ii(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=wi(u,t,l+p,d),h=wi(u,t,l,l+p),g=f>=0&&n[f],O=h>=0&&n[h];if(o&&!O||!o&&!g){Pi(Gr(a,s),i,u);let N=AD(o?xD:ND,n.length,o,r,c);!o&&O&&(n[h].providerFactory=N),Ii(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(N),s.push(N)}else{let N=cf(n[o?h:f],c,!o&&r);Ii(i,e,f>-1?f:h,N)}!o&&r&&O&&n[h].componentProviders++}}}function Ii(e,t,n,r){let o=Gt(t),i=ph(t);if(o||i){let u=(i?U(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function cf(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function wi(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function ND(e,t,n,r){return _s(this.multi,[])}function xD(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=wn(n,n[w],this.providerFactory.index,r);i=a.slice(0,s),_s(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],_s(o,i);return i}function _s(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function AD(e,t,n,r,o){let i=new yt(e,n,Y);return i.multi=[],i.index=t,i.componentProviders=0,cf(i,o,r&&!n),i}function US(e,t=[]){return n=>{n.providersResolver=(r,o)=>SD(r,o?o(e):e,t)}}function zS(e,t,n){let r=Rn()+e,o=D();return o[r]===we?wa(o,r,n?t.call(n):t()):Cv(o,r)}function GS(e,t,n,r){return df(D(),Rn(),e,t,n,r)}function WS(e,t,n,r,o){return ff(D(),Rn(),e,t,n,r,o)}function lf(e,t){let n=e[t];return n===we?void 0:n}function df(e,t,n,r,o,i){let s=t+n;return pe(e,s,o)?wa(e,s+1,i?r.call(i,o):r(o)):lf(e,s+1)}function ff(e,t,n,r,o,i,s){let a=t+n;return bv(e,a,o,i)?wa(e,a+2,s?r.call(s,o,i):r(o,i)):lf(e,a+2)}function qS(e,t){let n=L(),r,o=e+$;n.firstCreatePass?(r=RD(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=dt(r.type,!0)),s,a=se(Y);try{let u=zr(!1),c=i();return zr(u),wD(n,D(),o,c),c}finally{se(a)}}function RD(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function ZS(e,t,n){let r=e+$,o=D(),i=Bs(o,r);return pf(o,r)?df(o,Rn(),t,i.transform,n,i):i.transform(n)}function QS(e,t,n,r){let o=e+$,i=D(),s=Bs(i,o);return pf(i,o)?ff(i,Rn(),t,s.transform,n,r,s):s.transform(n,r)}function pf(e,t){return e[w].data[t].pure}function YS(e,t){return xo(e,t)}var uo=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},KS=new uo("19.1.5"),Ms=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},JS=(()=>{class e{compileModuleSync(n){return new ps(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=kc(n),i=Jl(o.declarations).reduce((s,a)=>{let u=Re(a);return u&&s.push(new wt(u)),s},[]);return new Ms(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var OD=(()=>{class e{zone=I(ce);changeDetectionScheduler=I(vt);applicationRef=I(Mn);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),FD=new S("",{factory:()=>!1});function hf({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new ce(re(ne({},gf()),{scheduleInRootZone:n})),[{provide:ce,useFactory:e},{provide:Pr,multi:!0,useFactory:()=>{let r=I(OD,{optional:!0});return()=>r.initialize()}},{provide:Pr,multi:!0,useFactory:()=>{let r=I(kD);return()=>{r.initialize()}}},t===!0?{provide:Tl,useValue:!0}:[],{provide:Sl,useValue:n??Ml}]}function XS(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=hf({ngZoneFactory:()=>{let o=gf(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Ie("NgZone_CoalesceEvent"),new ce(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return uh([{provide:FD,useValue:!0},{provide:Ys,useValue:!1},r])}function gf(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var kD=(()=>{class e{subscription=new j;initialized=!1;zone=I(ce);pendingTasks=I(Fn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ce.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ce.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var PD=(()=>{class e{appRef=I(Mn);taskService=I(Fn);ngZone=I(ce);zonelessEnabled=I(Ys);tracing=I(_o,{optional:!0});disableScheduling=I(Tl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new j;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(qr):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(I(Sl,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Bi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 8:{this.appRef.deferredDirtyFlags|=8;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 13:{this.appRef.dirtyFlags|=16,r=!0;break}case 14:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{r=!0;break}case 10:case 9:case 7:case 11:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Bu:Nl;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(qr+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Bu(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function LD(){return typeof $localize<"u"&&$localize.locale||ao}var Ao=new S("",{providedIn:"root",factory:()=>I(Ao,M.Optional|M.SkipSelf)||LD()});var Ts=new S(""),VD=new S("");function hn(e){return!e.moduleRef}function jD(e){let t=hn(e)?e.r3Injector:e.moduleRef.injector,n=t.get(ce);return n.run(()=>{hn(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Dt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),hn(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Ts);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Ts);s.add(i),e.moduleRef.onDestroy(()=>{Ar(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return HD(r,n,()=>{let i=t.get(Ud);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Ao,ao);if(mD(s||ao),!t.get(VD,!0))return hn(e)?t.get(Mn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(hn(e)){let u=t.get(Mn);return e.rootComponent!==void 0&&u.bootstrap(e.rootComponent),u}else return BD(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function BD(e,t){let n=e.injector.get(Mn);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new _(-403,!1);t.push(e)}function HD(e,t,n){try{let r=n();return Ca(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Rr=null;function $D(e=[],t){return Ne.create({name:t,providers:[{provide:Bc,useValue:"platform"},{provide:Ts,useValue:new Set([()=>Rr=null])},...e]})}function UD(e=[]){if(Rr)return Rr;let t=$D(e);return Rr=t,Rv(),zD(t),t}function zD(e){let t=e.get(Sg,null);$c(e,()=>{t?.forEach(n=>n())})}var ba=(()=>{class e{static __NG_ELEMENT_ID__=GD}return e})();function GD(e){return WD(Q(),D(),(e&16)===16)}function WD(e,t,n){if(bt(e)&&!n){let r=Se(e.index,t);return new Et(r,r)}else if(e.type&175){let r=t[le];return new Et(r,t)}return null}var Ss=class{constructor(){}supports(t){return Hd(t)}create(t){return new Ns(t)}},qD=(e,t)=>t,Ns=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||qD}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<mc(r,o,i)?n:r,a=mc(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let p=0;p<c;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;l<=h&&h<c&&(i[p]=f+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Hd(t))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Iv(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new xs(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new co),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new co),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},xs=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},As=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},co=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new As,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function mc(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function yc(){return new _a([new Ss])}var _a=(()=>{class e{factories;static \u0275prov=F({token:e,providedIn:"root",factory:yc});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||yc()),deps:[[e,new th,new eh]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new _(901,!1)}}return e})();function e0(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=UD(r),i=[hf({}),{provide:vt,useExisting:PD},...n||[]],s=new to({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return jD({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}}function ZD(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function QD(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function t0(e,t){Ie("NgSignals");let n=qo(e);return t?.equal&&(n[G].equal=t.equal),n}function YD(e){let t=b(null);try{return e()}finally{b(t)}}var mf=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=KD}return e})();function KD(){return new mf(D(),Q())}var JD=!1,XD=(()=>{class e extends ro{pendingTasks=I(Fn);taskId=null;schedule(n){super.schedule(n),this.taskId===null&&(this.taskId=this.pendingTasks.add(),queueMicrotask(()=>this.flush()))}flush(){try{super.flush()}finally{this.taskId!==null&&(this.pendingTasks.remove(this.taskId),this.taskId=null)}}static \u0275prov=F({token:e,providedIn:"root",factory:()=>new e})}return e})(),Rs=class{scheduler;effectFn;zone;injector;unregisterOnDestroy;watcher;constructor(t,n,r,o,i,s){this.scheduler=t,this.effectFn=n,this.zone=r,this.injector=i,this.watcher=Xa(a=>this.runEffect(a),()=>this.schedule(),s),this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}runEffect(t){try{this.effectFn(t)}catch(n){this.injector.get(Dt,null,{optional:!0})?.handleError(n)}}run(){this.watcher.run()}schedule(){this.scheduler.schedule(this)}destroy(){this.watcher.destroy(),this.unregisterOnDestroy?.()}};function eE(){}function tE(e,t){Ie("NgSignals"),!t?.injector&&mo(eE);let n=t?.injector??I(Ne),r=t?.manualCleanup!==!0?n.get(On):null,o=new Rs(n.get(XD),e,typeof Zone>"u"?null:Zone.current,r,n,t?.allowSignalWrites??!1),i=n.get(ba,null,{optional:!0});return!i||!(i._lView[y]&8)?o.watcher.notify():(i._lView[Tr]??=[]).push(o.watcher.notify),o}var nE=JD;var Os=class{[G];constructor(t){this[G]=t}destroy(){this[G].destroy()}};function rE(e,t){if(nE)return tE(e,t);Ie("NgSignals"),!t?.injector&&mo(rE);let n=t?.injector??I(Ne),r=t?.manualCleanup!==!0?n.get(On):null,o,i=n.get(mf,null,{optional:!0}),s=n.get(vt);return i!==null&&!t?.forceRoot?(o=sE(i.view,s,e),r instanceof Wr&&r._lView===i.view&&(r=null)):o=aE(e,n.get(zd),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Os(o)}var yf=re(ne({},et),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Cn,run(){if(this.dirty=!1,this.hasRun&&!on(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Rt(this),n=Hr(!1);try{this.maybeCleanup(),this.fn(e)}finally{Hr(n),rn(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),oE=re(ne({},yf),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(13)},destroy(){Ot(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),iE=re(ne({},yf),{consumerMarkedDirty(){this.view[y]|=8192,An(this.view),this.notifier.notify(14)},destroy(){Ot(this),this.onDestroyFn(),this.maybeCleanup(),this.view[pt]?.delete(this)}});function sE(e,t,n){let r=Object.create(iE);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[pt]??=new Set,e[pt].add(r),r.consumerMarkedDirty(r),r}function aE(e,t,n){let r=Object.create(oE);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(13),r}function n0(e,t){let n=Re(e),r=t.elementInjector||go();return new wt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function r0(e){let t=Re(e);if(!t)return null;let n=new wt(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var Mf=null;function Ma(){return Mf}function T0(e){Mf??=e}var vf=class{};var Fa=new S(""),ka=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:()=>I(cE),providedIn:"platform"})}return e})(),S0=new S(""),cE=(()=>{class e extends ka{_location;_history;_doc=I(Fa);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ma().getBaseHref(this._doc)}onPopState(n){let r=Ma().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Ma().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Pa(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Df(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function He(e){return e&&e[0]!=="?"?"?"+e:e}var Ho=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=F({token:e,factory:()=>I(lE),providedIn:"root"})}return e})(),Tf=new S(""),lE=(()=>{class e extends Ho{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??I(Fa).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Pa(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+He(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+He(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+He(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(q(ka),q(Tf,8))};static \u0275prov=F({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),N0=(()=>{class e extends Ho{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Pa(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+He(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+He(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(q(ka),q(Tf,8))};static \u0275prov=F({token:e,factory:e.\u0275fac})}return e})(),dE=(()=>{class e{_subject=new oe;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=hE(Df(Ef(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+He(r))}normalize(n){return e.stripTrailingSlash(pE(this._basePath,Ef(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+He(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+He(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=He;static joinWithSlash=Pa;static stripTrailingSlash=Df;static \u0275fac=function(r){return new(r||e)(q(Ho))};static \u0275prov=F({token:e,factory:()=>fE(),providedIn:"root"})}return e})();function fE(){return new dE(q(Ho))}function pE(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Ef(e){return e.replace(/\/index.html$/,"")}function hE(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var ee=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(ee||{}),R=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(R||{}),de=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(de||{}),Xe={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function gE(e){return he(e)[V.LocaleId]}function mE(e,t,n){let r=he(e),o=[r[V.DayPeriodsFormat],r[V.DayPeriodsStandalone]],i=ge(o,t);return ge(i,n)}function yE(e,t,n){let r=he(e),o=[r[V.DaysFormat],r[V.DaysStandalone]],i=ge(o,t);return ge(i,n)}function vE(e,t,n){let r=he(e),o=[r[V.MonthsFormat],r[V.MonthsStandalone]],i=ge(o,t);return ge(i,n)}function DE(e,t){let r=he(e)[V.Eras];return ge(r,t)}function Ro(e,t){let n=he(e);return ge(n[V.DateFormat],t)}function Oo(e,t){let n=he(e);return ge(n[V.TimeFormat],t)}function Fo(e,t){let r=he(e)[V.DateTimeFormat];return ge(r,t)}function $o(e,t){let n=he(e),r=n[V.NumberSymbols][t];if(typeof r>"u"){if(t===Xe.CurrencyDecimal)return n[V.NumberSymbols][Xe.Decimal];if(t===Xe.CurrencyGroup)return n[V.NumberSymbols][Xe.Group]}return r}function Sf(e){if(!e[V.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[V.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function EE(e){let t=he(e);return Sf(t),(t[V.ExtraData][2]||[]).map(r=>typeof r=="string"?Ta(r):[Ta(r[0]),Ta(r[1])])}function IE(e,t,n){let r=he(e);Sf(r);let o=[r[V.ExtraData][0],r[V.ExtraData][1]],i=ge(o,t)||[];return ge(i,n)||[]}function ge(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Ta(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var wE=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ko={},CE=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function bE(e,t,n,r){let o=OE(e);t=Be(n,t)||t;let s=[],a;for(;t;)if(a=CE.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();r&&(u=xf(r,u),o=RE(o,r,!0));let c="";return s.forEach(l=>{let d=xE(l);c+=d?d(o,n,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Bo(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Be(e,t){let n=gE(e);if(ko[n]??={},ko[n][t])return ko[n][t];let r="";switch(t){case"shortDate":r=Ro(e,de.Short);break;case"mediumDate":r=Ro(e,de.Medium);break;case"longDate":r=Ro(e,de.Long);break;case"fullDate":r=Ro(e,de.Full);break;case"shortTime":r=Oo(e,de.Short);break;case"mediumTime":r=Oo(e,de.Medium);break;case"longTime":r=Oo(e,de.Long);break;case"fullTime":r=Oo(e,de.Full);break;case"short":let o=Be(e,"shortTime"),i=Be(e,"shortDate");r=Po(Fo(e,de.Short),[o,i]);break;case"medium":let s=Be(e,"mediumTime"),a=Be(e,"mediumDate");r=Po(Fo(e,de.Medium),[s,a]);break;case"long":let u=Be(e,"longTime"),c=Be(e,"longDate");r=Po(Fo(e,de.Long),[u,c]);break;case"full":let l=Be(e,"fullTime"),d=Be(e,"fullDate");r=Po(Fo(e,de.Full),[l,d]);break}return r&&(ko[n][t]=r),r}function Po(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Ce(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function _E(e,t){return Ce(e,3).substring(0,t)}function B(e,t,n=0,r=!1,o=!1){return function(i,s){let a=ME(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return _E(a,t);let u=$o(s,Xe.MinusSign);return Ce(a,t,u,r,o)}}function ME(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function k(e,t,n=ee.Format,r=!1){return function(o,i){return TE(o,i,e,t,n,r)}}function TE(e,t,n,r,o,i){switch(n){case 2:return vE(t,o,r)[e.getMonth()];case 1:return yE(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let c=EE(t),l=IE(t,o,r),d=c.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,g=s>=f.hours&&a>=f.minutes,O=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(g&&O)return!0}else if(g||O)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return mE(t,o,r)[s<12?0:1];case 3:return DE(t,r)[e.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function Lo(e){return function(t,n,r){let o=-1*r,i=$o(n,Xe.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+Ce(s,2,i)+Ce(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+Ce(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+Ce(s,2,i)+":"+Ce(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+Ce(s,2,i)+":"+Ce(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var SE=0,jo=4;function NE(e){let t=Bo(e,SE,1).getDay();return Bo(e,0,1+(t<=jo?jo:jo+7)-t)}function Nf(e){let t=e.getDay(),n=t===0?-3:jo-t;return Bo(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Sa(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Nf(n),s=NE(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Ce(o,e,$o(r,Xe.MinusSign))}}function Vo(e,t=!1){return function(n,r){let i=Nf(n).getFullYear();return Ce(i,e,$o(r,Xe.MinusSign),t)}}var Na={};function xE(e){if(Na[e])return Na[e];let t;switch(e){case"G":case"GG":case"GGG":t=k(3,R.Abbreviated);break;case"GGGG":t=k(3,R.Wide);break;case"GGGGG":t=k(3,R.Narrow);break;case"y":t=B(0,1,0,!1,!0);break;case"yy":t=B(0,2,0,!0,!0);break;case"yyy":t=B(0,3,0,!1,!0);break;case"yyyy":t=B(0,4,0,!1,!0);break;case"Y":t=Vo(1);break;case"YY":t=Vo(2,!0);break;case"YYY":t=Vo(3);break;case"YYYY":t=Vo(4);break;case"M":case"L":t=B(1,1,1);break;case"MM":case"LL":t=B(1,2,1);break;case"MMM":t=k(2,R.Abbreviated);break;case"MMMM":t=k(2,R.Wide);break;case"MMMMM":t=k(2,R.Narrow);break;case"LLL":t=k(2,R.Abbreviated,ee.Standalone);break;case"LLLL":t=k(2,R.Wide,ee.Standalone);break;case"LLLLL":t=k(2,R.Narrow,ee.Standalone);break;case"w":t=Sa(1);break;case"ww":t=Sa(2);break;case"W":t=Sa(1,!0);break;case"d":t=B(2,1);break;case"dd":t=B(2,2);break;case"c":case"cc":t=B(7,1);break;case"ccc":t=k(1,R.Abbreviated,ee.Standalone);break;case"cccc":t=k(1,R.Wide,ee.Standalone);break;case"ccccc":t=k(1,R.Narrow,ee.Standalone);break;case"cccccc":t=k(1,R.Short,ee.Standalone);break;case"E":case"EE":case"EEE":t=k(1,R.Abbreviated);break;case"EEEE":t=k(1,R.Wide);break;case"EEEEE":t=k(1,R.Narrow);break;case"EEEEEE":t=k(1,R.Short);break;case"a":case"aa":case"aaa":t=k(0,R.Abbreviated);break;case"aaaa":t=k(0,R.Wide);break;case"aaaaa":t=k(0,R.Narrow);break;case"b":case"bb":case"bbb":t=k(0,R.Abbreviated,ee.Standalone,!0);break;case"bbbb":t=k(0,R.Wide,ee.Standalone,!0);break;case"bbbbb":t=k(0,R.Narrow,ee.Standalone,!0);break;case"B":case"BB":case"BBB":t=k(0,R.Abbreviated,ee.Format,!0);break;case"BBBB":t=k(0,R.Wide,ee.Format,!0);break;case"BBBBB":t=k(0,R.Narrow,ee.Format,!0);break;case"h":t=B(3,1,-12);break;case"hh":t=B(3,2,-12);break;case"H":t=B(3,1);break;case"HH":t=B(3,2);break;case"m":t=B(4,1);break;case"mm":t=B(4,2);break;case"s":t=B(5,1);break;case"ss":t=B(5,2);break;case"S":t=B(6,1);break;case"SS":t=B(6,2);break;case"SSS":t=B(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Lo(0);break;case"ZZZZZ":t=Lo(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Lo(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Lo(2);break;default:return null}return Na[e]=t,t}function xf(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function AE(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function RE(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=xf(t,o);return AE(e,r*(i-o))}function OE(e){if(If(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Bo(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(wE))return FE(r)}let t=new Date(e);if(!If(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function FE(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function If(e){return e instanceof Date&&!isNaN(e.valueOf())}function x0(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var xa=/\s+/,wf=[],A0=(()=>{class e{_ngEl;_renderer;initialClasses=wf;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(xa):wf}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(xa):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(xa).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(Y(Tt),Y(ya))};static \u0275dir=Bn({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Aa=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},R0=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Aa(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Cf(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Cf(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Y(Nt),Y(It),Y(_a))};static \u0275dir=Bn({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Cf(e,t){e.context.$implicit=t.item}var O0=(()=>{class e{_viewContainer;_context=new Ra;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){bf("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){bf("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Y(Nt),Y(It))};static \u0275dir=Bn({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ra=class{$implicit=null;ngIf=null};function bf(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${X(t)}'.`)}var F0=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(Y(Nt))};static \u0275dir=Bn({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[qc]})}return e})();function Af(e,t){return new _(2100,!1)}var kE=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,k0=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw Af(e,n);return n.replace(kE,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=Ia({name:"titlecase",type:e,pure:!0})}return e})();var PE="mediumDate",LE=new S(""),VE=new S(""),P0=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??PE,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return bE(n,s,i||this.locale,a)}catch(s){throw Af(e,s.message)}}static \u0275fac=function(r){return new(r||e)(Y(Ao,16),Y(LE,24),Y(VE,24))};static \u0275pipe=Ia({name:"date",type:e,pure:!0})}return e})();var L0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ld({type:e});static \u0275inj=_c({})}return e})(),jE="browser",BE="server";function V0(e){return e===jE}function j0(e){return e===BE}var B0=(()=>{class e{static \u0275prov=F({token:e,providedIn:"root",factory:()=>new Oa(I(Fa),window)})}return e})(),Oa=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=HE(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function HE(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var _f=class{};export{j as a,Uf as b,T as c,ri as d,oi as e,oe as f,an as g,cn as h,ot as i,_e as j,Xf as k,ep as l,tp as m,st as n,$e as o,cp as p,at as q,fn as r,yr as s,dp as t,fp as u,si as v,pp as w,ut as x,hp as y,_u as z,gp as A,mp as B,pn as C,vr as D,yp as E,Ep as F,Ip as G,ai as H,wp as I,Cp as J,bp as K,ci as L,_p as M,Mp as N,Tp as O,Sp as P,Np as Q,xp as R,Ap as S,_ as T,Cc as U,F as V,_c as W,KT as X,S as Y,M as Z,q as _,I as $,eh as aa,th as ba,uh as ca,Bc as da,Ze as ea,$c as fa,qc as ga,JT as ha,XT as ia,eS as ja,tS as ka,nS as la,bl as ma,Ne as na,ju as oa,On as pa,Fn as qa,Ge as ra,ce as sa,Dt as ta,rS as ua,Tt as va,Ie as wa,Dg as xa,Eg as ya,Hi as za,oS as Aa,iS as Ba,Sg as Ca,Ng as Da,sS as Ea,aS as Fa,_o as Ga,kg as Ha,Pg as Ia,bn as Ja,kn as Ka,zl as La,uS as Ma,cS as Na,lS as Oa,dS as Pa,fS as Qa,Gl as Ra,pS as Sa,ta as Ta,hS as Ua,gS as Va,Qr as Wa,It as Xa,Jr as Ya,ya as Za,Y as _a,vS as $a,Nt as ab,ES as bb,Ke as cb,ds as db,av as eb,IS as fb,Ld as gb,Bn as hb,pv as ib,wS as jb,Ev as kb,Mv as lb,CS as mb,Ca as nb,xv as ob,Av as pb,Mn as qb,Pv as rb,qv as sb,Zd as tb,Zv as ub,bS as vb,_S as wb,MS as xb,TS as yb,SS as zb,NS as Ab,nf as Bb,rf as Cb,sD as Db,of as Eb,sf as Fb,cD as Gb,xS as Hb,dD as Ib,vD as Jb,AS as Kb,RS as Lb,OS as Mb,FS as Nb,kS as Ob,PS as Pb,LS as Qb,VS as Rb,jS as Sb,BS as Tb,HS as Ub,bD as Vb,uf as Wb,MD as Xb,$S as Yb,TD as Zb,US as _b,zS as $b,GS as ac,WS as bc,qS as cc,ZS as dc,QS as ec,YS as fc,KS as gc,JS as hc,XS as ic,ba as jc,_a as kc,e0 as lc,ZD as mc,QD as nc,t0 as oc,YD as pc,rE as qc,n0 as rc,r0 as sc,Ma as tc,T0 as uc,vf as vc,Fa as wc,S0 as xc,Ho as yc,lE as zc,N0 as Ac,dE as Bc,x0 as Cc,A0 as Dc,R0 as Ec,O0 as Fc,F0 as Gc,k0 as Hc,P0 as Ic,L0 as Jc,jE as Kc,V0 as Lc,j0 as Mc,B0 as Nc,_f as Oc};
