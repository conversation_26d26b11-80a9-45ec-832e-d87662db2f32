import{a as X,b as Y,c as Z,d as tt,e as et}from"./chunk-5KM6POKN.js";import{a as J,b as K}from"./chunk-7BGM6SBA.js";import"./chunk-ORNC4OUU.js";import"./chunk-Q6MA6IAZ.js";import{a as G,b as H,c as Q,d as W}from"./chunk-UPFDAAUE.js";import{b as U}from"./chunk-OJZDOK3T.js";import{b as q}from"./chunk-ESTBHZNU.js";import"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import{a as B,c as F,d as z,e as $}from"./chunk-QB7XPJNY.js";import{i as E,k as L,l as O,n as A}from"./chunk-KGIFXD27.js";import{$ as N,_ as k,aa as T,ba as R,da as V,ea as j}from"./chunk-HE4KASLF.js";import{Bb as i,Cb as e,Db as g,Dc as I,Ec as b,Fc as w,Hb as M,Ic as h,Jb as x,Jc as S,Kb as s,Ub as n,Va as a,Vb as p,Wb as C,_a as m,ac as y,cc as _,dc as P,fb as D,ha as u,ia as f,lb as v,sb as l}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var nt=o=>["/lines",o];function ot(o,r){if(o&1&&(i(0,"mat-card-subtitle"),n(1),e()),o&2){let t=s();a(),p(t.point.name)}}function at(o,r){if(o&1&&g(0,"app-alert",5),o&2){let t=s();l("message",t.error)}}function rt(o,r){o&1&&(i(0,"div",6),g(1,"app-loading-spinner"),e())}function dt(o,r){if(o&1&&(i(0,"div",26)(1,"mat-chip",27),n(2),e()()),o&2){let t=r.$implicit;a(),l("routerLink",y(2,nt,t)),a(),C(" Line ID: ",t," ")}}function lt(o,r){if(o&1&&(i(0,"div",23)(1,"h3"),n(2,"Connected Lines"),e(),i(3,"div",24),v(4,dt,3,4,"div",25),e()()),o&2){let t=s(2);a(4),l("ngForOf",t.point.connectedLines)}}function pt(o,r){o&1&&(i(0,"div",28)(1,"p"),n(2,"No connected lines found for this connection point."),e()())}function st(o,r){if(o&1){let t=M();i(0,"div",7)(1,"div",8)(2,"button",9),x("click",function(){u(t);let c=s();return f(c.onEdit())}),i(3,"mat-icon"),n(4,"edit"),e(),n(5," Edit "),e(),i(6,"button",10),x("click",function(){u(t);let c=s();return f(c.onDelete())}),i(7,"mat-icon"),n(8,"delete"),e(),n(9," Delete "),e(),i(10,"button",11),x("click",function(){u(t);let c=s();return f(c.onViewMap())}),i(11,"mat-icon"),n(12,"map"),e(),n(13," View on Map "),e()(),g(14,"mat-divider",12),i(15,"mat-tab-group")(16,"mat-tab",13)(17,"div",14)(18,"div",15)(19,"div",16)(20,"div",17),n(21,"ID"),e(),i(22,"div",18),n(23),e()(),i(24,"div",16)(25,"div",17),n(26,"Name"),e(),i(27,"div",18),n(28),e()(),i(29,"div",16)(30,"div",17),n(31,"Provider"),e(),i(32,"div",18),n(33),e()(),i(34,"div",16)(35,"div",17),n(36,"Type"),e(),i(37,"div",18),n(38),e()(),i(39,"div",16)(40,"div",17),n(41,"Status"),e(),i(42,"div",18)(43,"span",19),n(44),e()()(),i(45,"div",16)(46,"div",17),n(47,"Capacity"),e(),i(48,"div",18),n(49),e()(),i(50,"div",16)(51,"div",17),n(52,"Address"),e(),i(53,"div",18),n(54),e()(),i(55,"div",16)(56,"div",17),n(57,"Installation Date"),e(),i(58,"div",18),n(59),_(60,"date"),e()(),i(61,"div",16)(62,"div",17),n(63,"Last Modified"),e(),i(64,"div",18),n(65),_(66,"date"),e()()()()(),i(67,"mat-tab",20)(68,"div",14),v(69,lt,5,1,"div",21)(70,pt,3,0,"div",22),e()()()()}if(o&2){let t=s();a(23),p(t.point.id),a(5),p(t.point.name),a(5),p(t.point.providerName),a(5),p(t.point.type),a(5),l("ngClass","status-"+t.point.status),a(),C(" ",t.point.status," "),a(5),p(t.point.capacity),a(5),p(t.point.address),a(5),p(P(60,12,t.point.installationDate)),a(6),p(P(66,14,t.point.lastModified)),a(4),l("ngIf",t.point.connectedLines&&t.point.connectedLines.length>0),a(),l("ngIf",!t.point.connectedLines||t.point.connectedLines.length===0)}}var it=class o{constructor(r,t,d,c){this.route=r;this.router=t;this.pointService=d;this.dialog=c}pointId="";point;loading=!0;error="";ngOnInit(){this.route.params.subscribe(r=>{this.pointId=r.id,this.loadPoint()})}loadPoint(){this.loading=!0,this.error="",setTimeout(()=>{this.point=this.getMockPoint(this.pointId),this.loading=!1,this.point||(this.error=`Connection point with ID ${this.pointId} not found.`)},1e3)}onEdit(){console.log("Edit connection point:",this.point)}onDelete(){if(!this.point)return;this.dialog.open(W,{width:"350px",data:{title:"Confirm Delete",message:`Are you sure you want to delete the connection point "${this.point.name}"?`,confirmText:"Delete",cancelText:"Cancel"}}).afterClosed().subscribe(t=>{t&&(console.log("Delete connection point:",this.point),this.router.navigate(["/points"]))})}onViewMap(){this.router.navigate(["/map"],{queryParams:{highlight:this.pointId}})}getMockPoint(r){return[{id:"point1",name:"Point 1",providerId:"provider1",providerName:"Provider A",type:"junction",capacity:300,address:"Address 1, City 1",status:"active",installationDate:new Date("2022-01-10"),lastModified:new Date("2023-05-15"),location:null,connectedLines:["1","2"],properties:{}},{id:"point2",name:"Point 2",providerId:"provider1",providerName:"Provider A",type:"endpoint",capacity:100,address:"Address 2, City 1",status:"active",installationDate:new Date("2022-01-12"),lastModified:new Date("2023-05-18"),location:null,connectedLines:["1","3"],properties:{}},{id:"point3",name:"Point 3",providerId:"provider2",providerName:"Provider B",type:"distribution",capacity:200,address:"Address 3, City 2",status:"active",installationDate:new Date("2021-11-05"),lastModified:new Date("2023-06-10"),location:null,connectedLines:["2"],properties:{}},{id:"point4",name:"Point 4",providerId:"provider2",providerName:"Provider B",type:"junction",capacity:250,address:"Address 4, City 2",status:"maintenance",installationDate:new Date("2021-11-08"),lastModified:new Date("2023-06-12"),location:null,connectedLines:["2"],properties:{}},{id:"point5",name:"Point 5",providerId:"provider1",providerName:"Provider A",type:"endpoint",capacity:150,address:"Address 5, City 3",status:"planned",installationDate:new Date("2023-11-20"),lastModified:new Date("2023-07-01"),location:null,connectedLines:["3"],properties:{}},{id:"point6",name:"Point 6",providerId:"provider1",providerName:"Provider A",type:"distribution",capacity:180,address:"Address 6, City 3",status:"planned",installationDate:new Date("2023-11-25"),lastModified:new Date("2023-07-03"),location:null,connectedLines:["3"],properties:{}},{id:"point7",name:"Point 7",providerId:"provider3",providerName:"Provider C",type:"junction",capacity:280,address:"Address 7, City 4",status:"active",installationDate:new Date("2022-03-15"),lastModified:new Date("2023-08-05"),location:null,connectedLines:["4"],properties:{}},{id:"point8",name:"Point 8",providerId:"provider3",providerName:"Provider C",type:"endpoint",capacity:320,address:"Address 8, City 4",status:"maintenance",installationDate:new Date("2022-03-18"),lastModified:new Date("2023-08-08"),location:null,connectedLines:["4"],properties:{}},{id:"point9",name:"Point 9",providerId:"provider2",providerName:"Provider B",type:"distribution",capacity:220,address:"Address 9, City 5",status:"inactive",installationDate:new Date("2022-05-01"),lastModified:new Date("2023-09-10"),location:null,connectedLines:["5"],properties:{}},{id:"point10",name:"Point 10",providerId:"provider2",providerName:"Provider B",type:"junction",capacity:270,address:"Address 10, City 5",status:"inactive",installationDate:new Date("2022-05-03"),lastModified:new Date("2023-09-12"),location:null,connectedLines:["5"],properties:{}},{id:"point11",name:"Point 11",providerId:"provider3",providerName:"Provider C",type:"endpoint",capacity:160,address:"Address 11, City 6",status:"active",installationDate:new Date("2022-06-05"),lastModified:new Date("2023-10-01"),location:null,connectedLines:["6"],properties:{}},{id:"point12",name:"Point 12",providerId:"provider3",providerName:"Provider C",type:"distribution",capacity:190,address:"Address 12, City 6",status:"active",installationDate:new Date("2022-06-08"),lastModified:new Date("2023-10-03"),location:null,connectedLines:["6"],properties:{}}].find(d=>d.id===r)}static \u0275fac=function(t){return new(t||o)(m(E),m(L),m(q),m(G))};static \u0275cmp=D({type:o,selectors:[["app-point-detail"]],decls:10,vars:4,consts:[[1,"point-detail-container"],[4,"ngIf"],["type","error",3,"message",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","point-details",4,"ngIf"],["type","error",3,"message"],[1,"loading-container"],[1,"point-details"],[1,"actions-bar"],["mat-raised-button","","color","primary",3,"click"],["mat-raised-button","","color","warn",3,"click"],["mat-raised-button","","color","accent",3,"click"],[1,"divider"],["label","Basic Information"],[1,"tab-content"],[1,"info-grid"],[1,"info-item"],[1,"info-label"],[1,"info-value"],[1,"status-chip",3,"ngClass"],["label","Connected Lines"],["class","connected-lines",4,"ngIf"],["class","no-lines",4,"ngIf"],[1,"connected-lines"],[1,"lines-list"],["class","line-item",4,"ngFor","ngForOf"],[1,"line-item"],["color","primary",3,"routerLink"],[1,"no-lines"]],template:function(t,d){t&1&&(i(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),n(4,"Connection Point Details"),e(),v(5,ot,2,1,"mat-card-subtitle",1),e(),i(6,"mat-card-content"),v(7,at,1,1,"app-alert",2)(8,rt,2,0,"div",3)(9,st,71,16,"div",4),e()()()),t&2&&(a(5),l("ngIf",d.point),a(2),l("ngIf",d.error),a(),l("ngIf",d.loading),a(),l("ngIf",d.point&&!d.loading))},dependencies:[S,I,b,w,h,j,k,T,V,R,N,F,B,$,z,Z,X,Y,et,tt,K,J,A,O,H,Q,U],styles:[".point-detail-container[_ngcontent-%COMP%]{padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px;min-height:300px;align-items:center}.actions-bar[_ngcontent-%COMP%]{display:flex;gap:10px;margin-bottom:20px}.divider[_ngcontent-%COMP%]{margin-bottom:20px}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column}.info-label[_ngcontent-%COMP%]{font-weight:500;color:#0000008a;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{font-size:16px}.connected-lines[_ngcontent-%COMP%]{margin-top:10px}.lines-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px;margin-top:10px}.line-item[_ngcontent-%COMP%]{cursor:pointer}.no-lines[_ngcontent-%COMP%]{color:#0000008a;font-style:italic}.status-chip[_ngcontent-%COMP%]{padding:4px 8px;border-radius:16px;font-size:12px;font-weight:500;text-transform:capitalize}.status-active[_ngcontent-%COMP%]{background-color:#e6f4ea;color:#137333}.status-planned[_ngcontent-%COMP%]{background-color:#e8f0fe;color:#1a73e8}.status-maintenance[_ngcontent-%COMP%]{background-color:#fef7e0;color:#b06000}.status-inactive[_ngcontent-%COMP%]{background-color:#fce8e6;color:#c5221f}"]})};export{it as PointDetailComponent};
