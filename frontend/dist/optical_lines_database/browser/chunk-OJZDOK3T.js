import{d as S,e as j}from"./chunk-QB7XPJNY.js";import{_ as I,aa as E,ea as z}from"./chunk-HE4KASLF.js";import{$ as g,Bb as o,Cb as a,Dc as O,Fc as x,Jc as w,M as b,Q as u,Ub as c,V as _,Va as i,Vb as C,ac as M,c as d,f as m,fb as h,lb as y,sa as v,sb as s,x as f}from"./chunk-BS5MTC5G.js";var D=e=>["alert-card",e];function k(e,r){e&1&&(o(0,"mat-icon"),c(1,"check_circle"),a())}function R(e,r){e&1&&(o(0,"mat-icon"),c(1,"error"),a())}function T(e,r){e&1&&(o(0,"mat-icon"),c(1,"warning"),a())}function F(e,r){e&1&&(o(0,"mat-icon"),c(1,"info"),a())}var P=class e{message="";type="info";static \u0275fac=function(t){return new(t||e)};static \u0275cmp=h({type:e,selectors:[["app-alert"]],inputs:{message:"message",type:"type"},decls:9,vars:8,consts:[[3,"ngClass"],[1,"alert-content"],[4,"ngIf"]],template:function(t,n){t&1&&(o(0,"mat-card",0)(1,"mat-card-content")(2,"div",1),y(3,k,2,0,"mat-icon",2)(4,R,2,0,"mat-icon",2)(5,T,2,0,"mat-icon",2)(6,F,2,0,"mat-icon",2),o(7,"span"),c(8),a()()()()),t&2&&(s("ngClass",M(6,D,n.type)),i(3),s("ngIf",n.type==="success"),i(),s("ngIf",n.type==="error"),i(),s("ngIf",n.type==="warning"),i(),s("ngIf",n.type==="info"),i(2),C(n.message))},dependencies:[z,I,E,j,S,w,O,x],styles:[".alert-card[_ngcontent-%COMP%]{margin:10px 0;border-radius:4px}.alert-content[_ngcontent-%COMP%]{display:flex;align-items:center}.alert-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.success[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.error[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.warning[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404}.info[_ngcontent-%COMP%]{background-color:#d1ecf1;color:#0c5460}"]})};var p=class{_box;_destroyed=new m;_resizeSubject=new m;_resizeObserver;_elementObservables=new Map;constructor(r){this._box=r,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(t=>this._resizeSubject.next(t)))}observe(r){return this._elementObservables.has(r)||this._elementObservables.set(r,new d(t=>{let n=this._resizeSubject.subscribe(t);return this._resizeObserver?.observe(r,{box:this._box}),()=>{this._resizeObserver?.unobserve(r),n.unsubscribe(),this._elementObservables.delete(r)}}).pipe(f(t=>t.some(n=>n.target===r)),b({bufferSize:1,refCount:!0}),u(this._destroyed))),this._elementObservables.get(r)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}},X=(()=>{class e{_cleanupErrorListener;_observers=new Map;_ngZone=g(v);constructor(){typeof ResizeObserver<"u"}ngOnDestroy(){for(let[,t]of this._observers)t.destroy();this._observers.clear(),this._cleanupErrorListener?.()}observe(t,n){let l=n?.box||"content-box";return this._observers.has(l)||this._observers.set(l,new p(l)),this._observers.get(l).observe(t)}static \u0275fac=function(n){return new(n||e)};static \u0275prov=_({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();export{X as a,P as b};
