import{$ as i,_ as n,aa as s,da as c,ea as p}from"./chunk-HE4KASLF.js";import{Bb as t,Cb as a,Ub as o,fb as r}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var m=class d{static \u0275fac=function(e){return new(e||d)};static \u0275cmp=r({type:d,selectors:[["app-dashboard"]],decls:9,vars:0,consts:[[1,"dashboard-container"],[1,"dashboard-placeholder"]],template:function(e,f){e&1&&(t(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),o(4,"Provider Dashboard"),a()(),t(5,"mat-card-content")(6,"div",1)(7,"p"),o(8,"Provider dashboard will be implemented in Phase 5"),a()()()()())},dependencies:[p,n,s,c,i],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px}.dashboard-placeholder[_ngcontent-%COMP%]{height:300px;display:flex;justify-content:center;align-items:center;background-color:#f5f5f5;border:1px solid #ddd}"]})};export{m as DashboardComponent};
