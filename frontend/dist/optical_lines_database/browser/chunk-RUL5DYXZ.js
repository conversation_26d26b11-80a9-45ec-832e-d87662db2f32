import{a as No,b as Ro}from"./chunk-QG3LXPLB.js";import{a as Fo,c as Ho,m as Vo,n as Wo,p as jo}from"./chunk-Q6MA6IAZ.js";import{a as Go,b as Uo}from"./chunk-ESTBHZNU.js";import{a as Zo,b as Ao}from"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import{b as <PERSON>,c as <PERSON>,d as <PERSON>,e as <PERSON>}from"./chunk-QB7XPJNY.js";import"./chunk-KGIFXD27.js";import{$ as Ne,F as Co,I as Io,L as Bi,O as zo,Q as Oo,Z as Eo,_ as Gt,aa as Ut,da as Re,ea as Jt,k as Mo}from"./chunk-HE4KASLF.js";import{$ as yt,Bb as g,Cb as S,Db as V,Ea as co,Ec as ko,Fc as To,Hb as Ae,Ib as fo,Jb as rt,Jc as jt,Kb as po,Lb as _o,Mb as vo,Ob as go,Pb as yo,Qb as wo,Tb as bo,U as eo,Ub as D,V as Ht,Va as G,W as io,Wb as Lo,Y as no,_a as Vt,_b as Po,fb as xt,g as de,ga as oo,gb as ho,ha as ue,ia as me,ja as ao,jc as xo,kb as lo,lb as Wt,mc as zt,nc as So,oa as so,ra as wt,rb as Ze,sb as J,ub as Di,va as ro,vb as uo,xb as mo}from"./chunk-BS5MTC5G.js";import{d as Ss,e as to}from"./chunk-C6Q5SG76.js";var Zi=Ss((Ue,Xo)=>{"use strict";(function(h,u){typeof Ue=="object"&&typeof Xo<"u"?u(Ue):typeof define=="function"&&define.amd?define(["exports"],u):(h=typeof globalThis<"u"?globalThis:h||self,u(h.leaflet={}))})(Ue,function(h){"use strict";var u="1.9.4";function l(t){var e,i,n,o;for(i=1,n=arguments.length;i<n;i++){o=arguments[i];for(e in o)t[e]=o[e]}return t}var f=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function m(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var O=0;function k(t){return"_leaflet_id"in t||(t._leaflet_id=++O),t._leaflet_id}function pe(t,e,i){var n,o,a,s;return s=function(){n=!1,o&&(a.apply(i,o),o=!1)},a=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(s,e),n=!0)},a}function Kt(t,e,i){var n=e[1],o=e[0],a=n-o;return t===n&&i?t:((t-o)%a+a)%a+o}function A(){return!1}function ct(t,e){if(e===!1)return t;var i=Math.pow(10,e===void 0?6:e);return Math.round(t*i)/i}function Xe(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function St(t){return Xe(t).split(/\s+/)}function E(t,e){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?f(t.options):{});for(var i in e)t.options[i]=e[i];return t.options}function Ai(t,e,i){var n=[];for(var o in t)n.push(encodeURIComponent(i?o.toUpperCase():o)+"="+encodeURIComponent(t[o]));return(!e||e.indexOf("?")===-1?"?":"&")+n.join("&")}var $o=/\{ *([\w_ -]+) *\}/g;function Ni(t,e){return t.replace($o,function(i,n){var o=e[n];if(o===void 0)throw new Error("No value provided for variable "+i);return typeof o=="function"&&(o=o(e)),o})}var ot=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function Ye(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return-1}var _e="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function $e(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var Ri=0;function Fi(t){var e=+new Date,i=Math.max(0,16-(e-Ri));return Ri=e+i,window.setTimeout(t,i)}var Qe=window.requestAnimationFrame||$e("RequestAnimationFrame")||Fi,Hi=window.cancelAnimationFrame||$e("CancelAnimationFrame")||$e("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function K(t,e,i){if(i&&Qe===Fi)t.call(e);else return Qe.call(window,m(t,e))}function tt(t){t&&Hi.call(window,t)}var Qo={__proto__:null,extend:l,create:f,bind:m,get lastId(){return O},stamp:k,throttle:pe,wrapNum:Kt,falseFn:A,formatNum:ct,trim:Xe,splitWords:St,setOptions:E,getParamString:Ai,template:Ni,isArray:ot,indexOf:Ye,emptyImageUrl:_e,requestFn:Qe,cancelFn:Hi,requestAnimFrame:K,cancelAnimFrame:tt};function mt(){}mt.extend=function(t){var e=function(){E(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},i=e.__super__=this.prototype,n=f(i);n.constructor=e,e.prototype=n;for(var o in this)Object.prototype.hasOwnProperty.call(this,o)&&o!=="prototype"&&o!=="__super__"&&(e[o]=this[o]);return t.statics&&l(e,t.statics),t.includes&&(ta(t.includes),l.apply(null,[n].concat(t.includes))),l(n,t),delete n.statics,delete n.includes,n.options&&(n.options=i.options?f(i.options):{},l(n.options,t.options)),n._initHooks=[],n.callInitHooks=function(){if(!this._initHooksCalled){i.callInitHooks&&i.callInitHooks.call(this),this._initHooksCalled=!0;for(var a=0,s=n._initHooks.length;a<s;a++)n._initHooks[a].call(this)}},e},mt.include=function(t){var e=this.prototype.options;return l(this.prototype,t),t.options&&(this.prototype.options=e,this.mergeOptions(t.options)),this},mt.mergeOptions=function(t){return l(this.prototype.options,t),this},mt.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i=typeof t=="function"?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};function ta(t){if(!(typeof L>"u"||!L||!L.Mixin)){t=ot(t)?t:[t];for(var e=0;e<t.length;e++)t[e]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var Q={on:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e);else{t=St(t);for(var o=0,a=t.length;o<a;o++)this._on(t[o],e,i)}return this},off:function(t,e,i){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var n in t)this._off(n,t[n],e);else{t=St(t);for(var o=arguments.length===1,a=0,s=t.length;a<s;a++)o?this._off(t[a]):this._off(t[a],e,i)}return this},_on:function(t,e,i,n){if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}if(this._listens(t,e,i)===!1){i===this&&(i=void 0);var o={fn:e,ctx:i};n&&(o.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(o)}},_off:function(t,e,i){var n,o,a;if(this._events&&(n=this._events[t],!!n)){if(arguments.length===1){if(this._firingCount)for(o=0,a=n.length;o<a;o++)n[o].fn=A;delete this._events[t];return}if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}var s=this._listens(t,e,i);if(s!==!1){var r=n[s];this._firingCount&&(r.fn=A,this._events[t]=n=n.slice()),n.splice(s,1)}}},fire:function(t,e,i){if(!this.listens(t,i))return this;var n=l({},e,{type:t,target:this,sourceTarget:e&&e.sourceTarget||this});if(this._events){var o=this._events[t];if(o){this._firingCount=this._firingCount+1||1;for(var a=0,s=o.length;a<s;a++){var r=o[a],c=r.fn;r.once&&this.off(t,c,r.ctx),c.call(r.ctx||this,n)}this._firingCount--}}return i&&this._propagateEvent(n),this},listens:function(t,e,i,n){typeof t!="string"&&console.warn('"string" type argument expected');var o=e;typeof e!="function"&&(n=!!e,o=void 0,i=void 0);var a=this._events&&this._events[t];if(a&&a.length&&this._listens(t,o,i)!==!1)return!0;if(n){for(var s in this._eventParents)if(this._eventParents[s].listens(t,e,i,n))return!0}return!1},_listens:function(t,e,i){if(!this._events)return!1;var n=this._events[t]||[];if(!e)return!!n.length;i===this&&(i=void 0);for(var o=0,a=n.length;o<a;o++)if(n[o].fn===e&&n[o].ctx===i)return o;return!1},once:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e,!0);else{t=St(t);for(var o=0,a=t.length;o<a;o++)this._on(t[o],e,i,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[k(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[k(t)],this},_propagateEvent:function(t){for(var e in this._eventParents)this._eventParents[e].fire(t.type,l({layer:t.target,propagatedFrom:t.target},t),!0)}};Q.addEventListener=Q.on,Q.removeEventListener=Q.clearAllEventListeners=Q.off,Q.addOneTimeEventListener=Q.once,Q.fireEvent=Q.fire,Q.hasEventListeners=Q.listens;var Xt=mt.extend(Q);function w(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var Vi=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};w.prototype={clone:function(){return new w(this.x,this.y)},add:function(t){return this.clone()._add(y(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(y(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new w(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new w(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Vi(this.x),this.y=Vi(this.y),this},distanceTo:function(t){t=y(t);var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return t=y(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=y(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+ct(this.x)+", "+ct(this.y)+")"}};function y(t,e,i){return t instanceof w?t:ot(t)?new w(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new w(t.x,t.y):new w(t,e,i)}function B(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}B.prototype={extend:function(t){var e,i;if(!t)return this;if(t instanceof w||typeof t[0]=="number"||"x"in t)e=i=y(t);else if(t=X(t),e=t.min,i=t.max,!e||!i)return this;return!this.min&&!this.max?(this.min=e.clone(),this.max=i.clone()):(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)),this},getCenter:function(t){return y((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return y(this.min.x,this.max.y)},getTopRight:function(){return y(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return typeof t[0]=="number"||t instanceof w?t=y(t):t=X(t),t instanceof B?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=X(t);var e=this.min,i=this.max,n=t.min,o=t.max,a=o.x>=e.x&&n.x<=i.x,s=o.y>=e.y&&n.y<=i.y;return a&&s},overlaps:function(t){t=X(t);var e=this.min,i=this.max,n=t.min,o=t.max,a=o.x>e.x&&n.x<i.x,s=o.y>e.y&&n.y<i.y;return a&&s},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var e=this.min,i=this.max,n=Math.abs(e.x-i.x)*t,o=Math.abs(e.y-i.y)*t;return X(y(e.x-n,e.y-o),y(i.x+n,i.y+o))},equals:function(t){return t?(t=X(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function X(t,e){return!t||t instanceof B?t:new B(t,e)}function Y(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}Y.prototype={extend:function(t){var e=this._southWest,i=this._northEast,n,o;if(t instanceof I)n=t,o=t;else if(t instanceof Y){if(n=t._southWest,o=t._northEast,!n||!o)return this}else return t?this.extend(T(t)||R(t)):this;return!e&&!i?(this._southWest=new I(n.lat,n.lng),this._northEast=new I(o.lat,o.lng)):(e.lat=Math.min(n.lat,e.lat),e.lng=Math.min(n.lng,e.lng),i.lat=Math.max(o.lat,i.lat),i.lng=Math.max(o.lng,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,o=Math.abs(e.lng-i.lng)*t;return new Y(new I(e.lat-n,e.lng-o),new I(i.lat+n,i.lng+o))},getCenter:function(){return new I((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new I(this.getNorth(),this.getWest())},getSouthEast:function(){return new I(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof I||"lat"in t?t=T(t):t=R(t);var e=this._southWest,i=this._northEast,n,o;return t instanceof Y?(n=t.getSouthWest(),o=t.getNorthEast()):n=o=t,n.lat>=e.lat&&o.lat<=i.lat&&n.lng>=e.lng&&o.lng<=i.lng},intersects:function(t){t=R(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),a=o.lat>=e.lat&&n.lat<=i.lat,s=o.lng>=e.lng&&n.lng<=i.lng;return a&&s},overlaps:function(t){t=R(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),a=o.lat>e.lat&&n.lat<i.lat,s=o.lng>e.lng&&n.lng<i.lng;return a&&s},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return t?(t=R(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function R(t,e){return t instanceof Y?t:new Y(t,e)}function I(t,e,i){if(isNaN(t)||isNaN(e))throw new Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,i!==void 0&&(this.alt=+i)}I.prototype={equals:function(t,e){if(!t)return!1;t=T(t);var i=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return i<=(e===void 0?1e-9:e)},toString:function(t){return"LatLng("+ct(this.lat,t)+", "+ct(this.lng,t)+")"},distanceTo:function(t){return bt.distance(this,T(t))},wrap:function(){return bt.wrapLatLng(this)},toBounds:function(t){var e=180*t/40075017,i=e/Math.cos(Math.PI/180*this.lat);return R([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new I(this.lat,this.lng,this.alt)}};function T(t,e,i){return t instanceof I?t:ot(t)&&typeof t[0]!="object"?t.length===3?new I(t[0],t[1],t[2]):t.length===2?new I(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new I(t.lat,"lng"in t?t.lng:t.lon,t.alt):e===void 0?null:new I(t,e,i)}var ft={latLngToPoint:function(t,e){var i=this.projection.project(t),n=this.scale(e);return this.transformation._transform(i,n)},pointToLatLng:function(t,e){var i=this.scale(e),n=this.transformation.untransform(t,i);return this.projection.unproject(n)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t),n=this.transformation.transform(e.min,i),o=this.transformation.transform(e.max,i);return new B(n,o)},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?Kt(t.lng,this.wrapLng,!0):t.lng,i=this.wrapLat?Kt(t.lat,this.wrapLat,!0):t.lat,n=t.alt;return new I(i,e,n)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,o=e.lng-i.lng;if(n===0&&o===0)return t;var a=t.getSouthWest(),s=t.getNorthEast(),r=new I(a.lat-n,a.lng-o),c=new I(s.lat-n,s.lng-o);return new Y(r,c)}},bt=l({},ft,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,a=Math.sin((e.lat-t.lat)*i/2),s=Math.sin((e.lng-t.lng)*i/2),r=a*a+Math.cos(n)*Math.cos(o)*s*s,c=2*Math.atan2(Math.sqrt(r),Math.sqrt(1-r));return this.R*c}}),Wi=6378137,ti={R:Wi,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,n=Math.max(Math.min(i,t.lat),-i),o=Math.sin(n*e);return new w(this.R*t.lng*e,this.R*Math.log((1+o)/(1-o))/2)},unproject:function(t){var e=180/Math.PI;return new I((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:function(){var t=Wi*Math.PI;return new B([-t,-t],[t,t])}()};function ei(t,e,i,n){if(ot(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=e,this._c=i,this._d=n}ei.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return e=e||1,t.x=e*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new w((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};function Yt(t,e,i,n){return new ei(t,e,i,n)}var ii=l({},bt,{code:"EPSG:3857",projection:ti,transformation:function(){var t=.5/(Math.PI*ti.R);return Yt(t,.5,-t,.5)}()}),ea=l({},ii,{code:"EPSG:900913"});function ji(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Gi(t,e){var i="",n,o,a,s,r,c;for(n=0,a=t.length;n<a;n++){for(r=t[n],o=0,s=r.length;o<s;o++)c=r[o],i+=(o?"L":"M")+c.x+" "+c.y;i+=e?_.svg?"z":"x":""}return i||"M0 0"}var ni=document.documentElement.style,ve="ActiveXObject"in window,ia=ve&&!document.addEventListener,Ui="msLaunchUri"in navigator&&!("documentMode"in document),oi=ht("webkit"),Ji=ht("android"),qi=ht("android 2")||ht("android 3"),na=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),oa=Ji&&ht("Google")&&na<537&&!("AudioNode"in window),ai=!!window.opera,Ki=!Ui&&ht("chrome"),Xi=ht("gecko")&&!oi&&!ai&&!ve,aa=!Ki&&ht("safari"),Yi=ht("phantom"),$i="OTransition"in ni,sa=navigator.platform.indexOf("Win")===0,Qi=ve&&"transition"in ni,si="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!qi,tn="MozPerspective"in ni,ra=!window.L_DISABLE_3D&&(Qi||si||tn)&&!$i&&!Yi,$t=typeof orientation<"u"||ht("mobile"),ca=$t&&oi,ha=$t&&si,en=!window.PointerEvent&&window.MSPointerEvent,nn=!!(window.PointerEvent||en),on="ontouchstart"in window||!!window.TouchEvent,la=!window.L_NO_TOUCH&&(on||nn),da=$t&&ai,ua=$t&&Xi,ma=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,fa=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",A,e),window.removeEventListener("testPassiveEventSupport",A,e)}catch{}return t}(),pa=function(){return!!document.createElement("canvas").getContext}(),ri=!!(document.createElementNS&&ji("svg").createSVGRect),_a=!!ri&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),va=!ri&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&typeof e.adj=="object"}catch{return!1}}(),ga=navigator.platform.indexOf("Mac")===0,ya=navigator.platform.indexOf("Linux")===0;function ht(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var _={ie:ve,ielt9:ia,edge:Ui,webkit:oi,android:Ji,android23:qi,androidStock:oa,opera:ai,chrome:Ki,gecko:Xi,safari:aa,phantom:Yi,opera12:$i,win:sa,ie3d:Qi,webkit3d:si,gecko3d:tn,any3d:ra,mobile:$t,mobileWebkit:ca,mobileWebkit3d:ha,msPointer:en,pointer:nn,touch:la,touchNative:on,mobileOpera:da,mobileGecko:ua,retina:ma,passiveEvents:fa,canvas:pa,svg:ri,vml:va,inlineSvg:_a,mac:ga,linux:ya},an=_.msPointer?"MSPointerDown":"pointerdown",sn=_.msPointer?"MSPointerMove":"pointermove",rn=_.msPointer?"MSPointerUp":"pointerup",cn=_.msPointer?"MSPointerCancel":"pointercancel",ci={touchstart:an,touchmove:sn,touchend:rn,touchcancel:cn},hn={touchstart:Sa,touchmove:ge,touchend:ge,touchcancel:ge},Ot={},ln=!1;function wa(t,e,i){return e==="touchstart"&&xa(),hn[e]?(i=hn[e].bind(this,i),t.addEventListener(ci[e],i,!1),i):(console.warn("wrong event specified:",e),A)}function ba(t,e,i){if(!ci[e]){console.warn("wrong event specified:",e);return}t.removeEventListener(ci[e],i,!1)}function La(t){Ot[t.pointerId]=t}function Pa(t){Ot[t.pointerId]&&(Ot[t.pointerId]=t)}function dn(t){delete Ot[t.pointerId]}function xa(){ln||(document.addEventListener(an,La,!0),document.addEventListener(sn,Pa,!0),document.addEventListener(rn,dn,!0),document.addEventListener(cn,dn,!0),ln=!0)}function ge(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){e.touches=[];for(var i in Ot)e.touches.push(Ot[i]);e.changedTouches=[e],t(e)}}function Sa(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&j(e),ge(t,e)}function ka(t){var e={},i,n;for(n in t)i=t[n],e[n]=i&&i.bind?i.bind(t):i;return t=e,e.type="dblclick",e.detail=2,e.isTrusted=!1,e._simulated=!0,e}var Ta=200;function Ma(t,e){t.addEventListener("dblclick",e);var i=0,n;function o(a){if(a.detail!==1){n=a.detail;return}if(!(a.pointerType==="mouse"||a.sourceCapabilities&&!a.sourceCapabilities.firesTouchEvents)){var s=_n(a);if(!(s.some(function(c){return c instanceof HTMLLabelElement&&c.attributes.for})&&!s.some(function(c){return c instanceof HTMLInputElement||c instanceof HTMLSelectElement}))){var r=Date.now();r-i<=Ta?(n++,n===2&&e(ka(a))):n=1,i=r}}}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}function Ca(t,e){t.removeEventListener("dblclick",e.dblclick),t.removeEventListener("click",e.simDblclick)}var hi=be(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Qt=be(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),un=Qt==="webkitTransition"||Qt==="OTransition"?Qt+"End":"transitionend";function mn(t){return typeof t=="string"?document.getElementById(t):t}function te(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||i==="auto")&&document.defaultView){var n=document.defaultView.getComputedStyle(t,null);i=n?n[e]:null}return i==="auto"?null:i}function C(t,e,i){var n=document.createElement(t);return n.className=e||"",i&&i.appendChild(n),n}function Z(t){var e=t.parentNode;e&&e.removeChild(t)}function ye(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Et(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function Dt(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function li(t,e){if(t.classList!==void 0)return t.classList.contains(e);var i=we(t);return i.length>0&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function P(t,e){if(t.classList!==void 0)for(var i=St(e),n=0,o=i.length;n<o;n++)t.classList.add(i[n]);else if(!li(t,e)){var a=we(t);di(t,(a?a+" ":"")+e)}}function N(t,e){t.classList!==void 0?t.classList.remove(e):di(t,Xe((" "+we(t)+" ").replace(" "+e+" "," ")))}function di(t,e){t.className.baseVal===void 0?t.className=e:t.className.baseVal=e}function we(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function et(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&Ia(t,e)}function Ia(t,e){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch{if(e===1)return}e=Math.round(e*100),i?(i.Enabled=e!==100,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}function be(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function kt(t,e,i){var n=e||new w(0,0);t.style[hi]=(_.ie3d?"translate("+n.x+"px,"+n.y+"px)":"translate3d("+n.x+"px,"+n.y+"px,0)")+(i?" scale("+i+")":"")}function F(t,e){t._leaflet_pos=e,_.any3d?kt(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function Tt(t){return t._leaflet_pos||new w(0,0)}var ee,ie,ui;if("onselectstart"in document)ee=function(){b(window,"selectstart",j)},ie=function(){z(window,"selectstart",j)};else{var ne=be(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);ee=function(){if(ne){var t=document.documentElement.style;ui=t[ne],t[ne]="none"}},ie=function(){ne&&(document.documentElement.style[ne]=ui,ui=void 0)}}function mi(){b(window,"dragstart",j)}function fi(){z(window,"dragstart",j)}var Le,pi;function _i(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(Pe(),Le=t,pi=t.style.outlineStyle,t.style.outlineStyle="none",b(window,"keydown",Pe))}function Pe(){Le&&(Le.style.outlineStyle=pi,Le=void 0,pi=void 0,z(window,"keydown",Pe))}function fn(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function vi(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}var za={__proto__:null,TRANSFORM:hi,TRANSITION:Qt,TRANSITION_END:un,get:mn,getStyle:te,create:C,remove:Z,empty:ye,toFront:Et,toBack:Dt,hasClass:li,addClass:P,removeClass:N,setClass:di,getClass:we,setOpacity:et,testProp:be,setTransform:kt,setPosition:F,getPosition:Tt,get disableTextSelection(){return ee},get enableTextSelection(){return ie},disableImageDrag:mi,enableImageDrag:fi,preventOutline:_i,restoreOutline:Pe,getSizedParentNode:fn,getScale:vi};function b(t,e,i,n){if(e&&typeof e=="object")for(var o in e)yi(t,o,e[o],i);else{e=St(e);for(var a=0,s=e.length;a<s;a++)yi(t,e[a],i,n)}return this}var lt="_leaflet_events";function z(t,e,i,n){if(arguments.length===1)pn(t),delete t[lt];else if(e&&typeof e=="object")for(var o in e)wi(t,o,e[o],i);else if(e=St(e),arguments.length===2)pn(t,function(r){return Ye(e,r)!==-1});else for(var a=0,s=e.length;a<s;a++)wi(t,e[a],i,n);return this}function pn(t,e){for(var i in t[lt]){var n=i.split(/\d/)[0];(!e||e(n))&&wi(t,n,null,null,i)}}var gi={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function yi(t,e,i,n){var o=e+k(i)+(n?"_"+k(n):"");if(t[lt]&&t[lt][o])return this;var a=function(r){return i.call(n||t,r||window.event)},s=a;!_.touchNative&&_.pointer&&e.indexOf("touch")===0?a=wa(t,e,a):_.touch&&e==="dblclick"?a=Ma(t,a):"addEventListener"in t?e==="touchstart"||e==="touchmove"||e==="wheel"||e==="mousewheel"?t.addEventListener(gi[e]||e,a,_.passiveEvents?{passive:!1}:!1):e==="mouseenter"||e==="mouseleave"?(a=function(r){r=r||window.event,Li(t,r)&&s(r)},t.addEventListener(gi[e],a,!1)):t.addEventListener(e,s,!1):t.attachEvent("on"+e,a),t[lt]=t[lt]||{},t[lt][o]=a}function wi(t,e,i,n,o){o=o||e+k(i)+(n?"_"+k(n):"");var a=t[lt]&&t[lt][o];if(!a)return this;!_.touchNative&&_.pointer&&e.indexOf("touch")===0?ba(t,e,a):_.touch&&e==="dblclick"?Ca(t,a):"removeEventListener"in t?t.removeEventListener(gi[e]||e,a,!1):t.detachEvent("on"+e,a),t[lt][o]=null}function Mt(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function bi(t){return yi(t,"wheel",Mt),this}function oe(t){return b(t,"mousedown touchstart dblclick contextmenu",Mt),t._leaflet_disable_click=!0,this}function j(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function Ct(t){return j(t),Mt(t),this}function _n(t){if(t.composedPath)return t.composedPath();for(var e=[],i=t.target;i;)e.push(i),i=i.parentNode;return e}function vn(t,e){if(!e)return new w(t.clientX,t.clientY);var i=vi(e),n=i.boundingClientRect;return new w((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var Oa=_.linux&&_.chrome?window.devicePixelRatio:_.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function gn(t){return _.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/Oa:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function Li(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch{return!1}return i!==t}var Ea={__proto__:null,on:b,off:z,stopPropagation:Mt,disableScrollPropagation:bi,disableClickPropagation:oe,preventDefault:j,stop:Ct,getPropagationPath:_n,getMousePosition:vn,getWheelDelta:gn,isExternalTarget:Li,addListener:b,removeListener:z},yn=Xt.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=Tt(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=K(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=this._duration*1e3;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),F(this._el,i),this.fire("step")},_complete:function(){tt(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),M=Xt.extend({options:{crs:ii,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=E(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=m(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),e.zoom!==void 0&&(this._zoom=this._limitZoom(e.zoom)),e.center&&e.zoom!==void 0&&this.setView(T(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Qt&&_.any3d&&!_.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),b(this._proxy,un,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,e,i){if(e=e===void 0?this._zoom:this._limitZoom(e),t=this._limitCenter(T(t),e,this.options.maxBounds),i=i||{},this._stop(),this._loaded&&!i.reset&&i!==!0){i.animate!==void 0&&(i.zoom=l({animate:i.animate},i.zoom),i.pan=l({animate:i.animate,duration:i.duration},i.pan));var n=this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,i.zoom):this._tryAnimatedPan(t,i.pan);if(n)return clearTimeout(this._sizeTimer),this}return this._resetView(t,e,i.pan&&i.pan.noMoveStart),this},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(_.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(_.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),a=t instanceof w?t:this.latLngToContainerPoint(t),s=a.subtract(o).multiplyBy(1-1/n),r=this.containerPointToLatLng(o.add(s));return this.setView(r,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():R(t);var i=y(e.paddingTopLeft||e.padding||[0,0]),n=y(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));if(o=typeof e.maxZoom=="number"?Math.min(e.maxZoom,o):o,o===1/0)return{center:t.getCenter(),zoom:o};var a=n.subtract(i).divideBy(2),s=this.project(t.getSouthWest(),o),r=this.project(t.getNorthEast(),o),c=this.unproject(s.add(r).divideBy(2).add(a),o);return{center:c,zoom:o}},fitBounds:function(t,e){if(t=R(t),!t.isValid())throw new Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(t=y(t).round(),e=e||{},!t.x&&!t.y)return this.fire("moveend");if(e.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new yn,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),e.animate!==!1){P(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(i=i||{},i.animate===!1||!_.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),a=this.getSize(),s=this._zoom;t=T(t),e=e===void 0?s:e;var r=Math.max(a.x,a.y),c=r*this.getZoomScale(s,e),d=o.distanceTo(n)||1,p=1.42,v=p*p;function x(H){var Be=H?-1:1,bs=H?c:r,Ls=c*c-r*r+Be*v*v*d*d,Ps=2*bs*v*d,Ei=Ls/Ps,Qn=Math.sqrt(Ei*Ei+1)-Ei,xs=Qn<1e-9?-18:Math.log(Qn);return xs}function U(H){return(Math.exp(H)-Math.exp(-H))/2}function W(H){return(Math.exp(H)+Math.exp(-H))/2}function nt(H){return U(H)/W(H)}var $=x(0);function Ft(H){return r*(W($)/W($+p*H))}function vs(H){return r*(W($)*nt($+p*H)-U($))/v}function gs(H){return 1-Math.pow(1-H,1.5)}var ys=Date.now(),Yn=(x(1)-$)/p,ws=i.duration?1e3*i.duration:1e3*Yn*.8;function $n(){var H=(Date.now()-ys)/ws,Be=gs(H)*Yn;H<=1?(this._flyToFrame=K($n,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy(vs(Be)/d)),s),this.getScaleZoom(r/Ft(Be),s),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}return this._moveStart(!0,i.noMoveStart),$n.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return t=R(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),n=this._limitCenter(i,this._zoom,R(t));return i.equals(n)||this.panTo(n,e),this._enforcingBounds=!1,this},panInside:function(t,e){e=e||{};var i=y(e.paddingTopLeft||e.padding||[0,0]),n=y(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),a=this.project(t),s=this.getPixelBounds(),r=X([s.min.add(i),s.max.subtract(n)]),c=r.getSize();if(!r.contains(a)){this._enforcingBounds=!0;var d=a.subtract(r.getCenter()),p=r.extend(a).getSize().subtract(c);o.x+=d.x<0?-p.x:p.x,o.y+=d.y<0?-p.y:p.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=l({animate:!1,pan:!0},t===!0?{animate:!0}:t);var e=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var i=this.getSize(),n=e.divideBy(2).round(),o=i.divideBy(2).round(),a=n.subtract(o);return!a.x&&!a.y?this:(t.animate&&t.pan?this.panBy(a):(t.pan&&this._rawPanBy(a),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(m(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:i}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=l({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var e=m(this._handleGeolocationResponse,this),i=m(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,i,t):navigator.geolocation.getCurrentPosition(e,i,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var e=t.code,i=t.message||(e===1?"permission denied":e===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e=t.coords.latitude,i=t.coords.longitude,n=new I(e,i),o=n.toBounds(t.coords.accuracy*2),a=this._locateOptions;if(a.setView){var s=this.getBoundsZoom(o);this.setView(n,a.maxZoom?Math.min(s,a.maxZoom):s)}var r={latlng:n,bounds:o,timestamp:t.timestamp};for(var c in t.coords)typeof t.coords[c]=="number"&&(r[c]=t.coords[c]);this.fire("locationfound",r)}},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Z(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(tt(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)Z(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),n=C("div",i,e||this._mapPane);return t&&(this._panes[t]=n),n},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),e=this.unproject(t.getBottomLeft()),i=this.unproject(t.getTopRight());return new Y(e,i)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=R(t),i=y(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),a=this.getMaxZoom(),s=t.getNorthWest(),r=t.getSouthEast(),c=this.getSize().subtract(i),d=X(this.project(r,n),this.project(s,n)).getSize(),p=_.any3d?this.options.zoomSnap:1,v=c.x/d.x,x=c.y/d.y,U=e?Math.max(v,x):Math.min(v,x);return n=this.getScaleZoom(U,n),p&&(n=Math.round(n/(p/100))*(p/100),n=e?Math.ceil(n/p)*p:Math.floor(n/p)*p),Math.max(o,Math.min(a,n))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new w(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new B(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=e===void 0?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=e===void 0?this._zoom:e;var n=i.zoom(t*i.scale(e));return isNaN(n)?1/0:n},project:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.latLngToPoint(T(t),e)},unproject:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.pointToLatLng(y(t),e)},layerPointToLatLng:function(t){var e=y(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){var e=this.project(T(t))._round();return e._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(T(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(R(t))},distance:function(t,e){return this.options.crs.distance(T(t),T(e))},containerPointToLayerPoint:function(t){return y(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return y(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(y(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(T(t)))},mouseEventToContainerPoint:function(t){return vn(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=mn(t);if(e){if(e._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");b(e,"scroll",this._onScroll,this),this._containerId=k(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&_.any3d,P(t,"leaflet-container"+(_.touch?" leaflet-touch":"")+(_.retina?" leaflet-retina":"")+(_.ielt9?" leaflet-oldie":"")+(_.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=te(t,"position");e!=="absolute"&&e!=="relative"&&e!=="fixed"&&e!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),F(this._mapPane,new w(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(P(t.markerPane,"leaflet-zoom-hide"),P(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e,i){F(this._mapPane,new w(0,0));var n=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,i)._move(t,e)._moveEnd(o),this.fire("viewreset"),n&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){e===void 0&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return tt(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){F(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[k(this._container)]=this;var e=t?z:b;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),_.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){tt(this._resizeRequest),this._resizeRequest=K(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i=[],n,o=e==="mouseout"||e==="mouseover",a=t.target||t.srcElement,s=!1;a;){if(n=this._targets[k(a)],n&&(e==="click"||e==="preclick")&&this._draggableMoved(n)){s=!0;break}if(n&&n.listens(e,!0)&&(o&&!Li(a,t)||(i.push(n),o))||a===this._container)break;a=a.parentNode}return!i.length&&!s&&!o&&this.listens(e,!0)&&(i=[this]),i},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e=t.target||t.srcElement;if(!(!this._loaded||e._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(e))){var i=t.type;i==="mousedown"&&_i(e),this._fireDOMEvent(t,i)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,e,i){if(t.type==="click"){var n=l({},t);n.type="preclick",this._fireDOMEvent(n,n.type,i)}var o=this._findEventTargets(t,e);if(i){for(var a=[],s=0;s<i.length;s++)i[s].listens(e,!0)&&a.push(i[s]);o=a.concat(o)}if(o.length){e==="contextmenu"&&j(t);var r=o[0],c={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var d=r.getLatLng&&(!r._radius||r._radius<=10);c.containerPoint=d?this.latLngToContainerPoint(r.getLatLng()):this.mouseEventToContainerPoint(t),c.layerPoint=this.containerPointToLayerPoint(c.containerPoint),c.latlng=d?r.getLatLng():this.layerPointToLatLng(c.layerPoint)}for(s=0;s<o.length;s++)if(o[s].fire(e,c,!0),c.originalEvent._stopped||o[s].options.bubblingMouseEvents===!1&&Ye(this._mouseEvents,e)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return Tt(this._mapPane)||new w(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){var i=t&&e!==void 0?this._getNewPixelOrigin(t,e):this.getPixelOrigin();return i.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(n)},_latLngBoundsToNewLayerBounds:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return X([this.project(t.getSouthWest(),e)._subtract(n),this.project(t.getNorthWest(),e)._subtract(n),this.project(t.getSouthEast(),e)._subtract(n),this.project(t.getNorthEast(),e)._subtract(n)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),a=new B(n.subtract(o),n.add(o)),s=this._getBoundsOffset(a,i,e);return Math.abs(s.x)<=1&&Math.abs(s.y)<=1?t:this.unproject(n.add(s),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),n=new B(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(n,e))},_getBoundsOffset:function(t,e,i){var n=X(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),o=n.min.subtract(t.min),a=n.max.subtract(t.max),s=this._rebound(o.x,-a.x),r=this._rebound(o.y,-a.y);return new w(s,r)},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=_.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){N(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return(e&&e.animate)!==!0&&!this.getSize().contains(i)?!1:(this.panBy(i,e),!0)},_createAnimProxy:function(){var t=this._proxy=C("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(e){var i=hi,n=this._proxy.style[i];kt(this._proxy,this.project(e.center,e.zoom),this.getZoomScale(e.zoom,1)),n===this._proxy.style[i]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Z(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();kt(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||i.animate===!1||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/n);return i.animate!==!0&&!this.getSize().contains(o)?!1:(K(function(){this._moveStart(!0,i.noMoveStart||!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,P(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(m(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&N(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Da(t,e){return new M(t,e)}var at=mt.extend({options:{position:"topright"},initialize:function(t){E(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),n=t._controlCorners[i];return P(e,"leaflet-control"),i.indexOf("bottom")!==-1?n.insertBefore(e,n.firstChild):n.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Z(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),ae=function(t){return new at(t)};M.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=C("div",e+"control-container",this._container);function n(o,a){var s=e+o+" "+e+a;t[o+a]=C("div",s,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)Z(this._controlCorners[t]);Z(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var wn=at.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:n<i?1:0}},initialize:function(t,e,i){E(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var n in t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return at.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(k(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){P(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(P(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):N(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return N(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=C("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),oe(e),bi(e);var n=this._section=C("section",t+"-list");i&&(this._map.on("click",this.collapse,this),b(e,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var o=this._layersLink=C("a",t+"-toggle",e);o.href="#",o.title="Layers",o.setAttribute("role","button"),b(o,{keydown:function(a){a.keyCode===13&&this._expandSafely()},click:function(a){j(a),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=C("div",t+"-base",n),this._separator=C("div",t+"-separator",n),this._overlaysList=C("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&k(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(m(function(n,o){return this.options.sortFunction(n.layer,o.layer,n.name,o.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;ye(this._baseLayersList),ye(this._overlaysList),this._layerControlInputs=[];var t,e,i,n,o=0;for(i=0;i<this._layers.length;i++)n=this._layers[i],this._addItem(n),e=e||n.overlay,t=t||!n.overlay,o+=n.overlay?0:1;return this.options.hideSingleBase&&(t=t&&o>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(k(t.target)),i=e.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",n=document.createElement("div");return n.innerHTML=i,n.firstChild},_addItem:function(t){var e=document.createElement("label"),i=this._map.hasLayer(t.layer),n;t.overlay?(n=document.createElement("input"),n.type="checkbox",n.className="leaflet-control-layers-selector",n.defaultChecked=i):n=this._createRadioElement("leaflet-base-layers_"+k(this),i),this._layerControlInputs.push(n),n.layerId=k(t.layer),b(n,"click",this._onInputClick,this);var o=document.createElement("span");o.innerHTML=" "+t.name;var a=document.createElement("span");e.appendChild(a),a.appendChild(n),a.appendChild(o);var s=t.overlay?this._overlaysList:this._baseLayersList;return s.appendChild(e),this._checkDisabledLayers(),e},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,e,i,n=[],o=[];this._handlingClick=!0;for(var a=t.length-1;a>=0;a--)e=t[a],i=this._getLayer(e.layerId).layer,e.checked?n.push(i):e.checked||o.push(i);for(a=0;a<o.length;a++)this._map.hasLayer(o[a])&&this._map.removeLayer(o[a]);for(a=0;a<n.length;a++)this._map.hasLayer(n[a])||this._map.addLayer(n[a]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,e,i,n=this._map.getZoom(),o=t.length-1;o>=0;o--)e=t[o],i=this._getLayer(e.layerId).layer,e.disabled=i.options.minZoom!==void 0&&n<i.options.minZoom||i.options.maxZoom!==void 0&&n>i.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,b(t,"click",j),this.expand();var e=this;setTimeout(function(){z(t,"click",j),e._preventClick=!1})}}),Ba=function(t,e,i){return new wn(t,e,i)},Pi=at.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=C("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){var a=C("a",i,n);return a.innerHTML=t,a.href="#",a.title=e,a.setAttribute("role","button"),a.setAttribute("aria-label",e),oe(a),b(a,"click",Ct),b(a,"click",o,this),b(a,"click",this._refocusOnMap,this),a},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";N(this._zoomInButton,e),N(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(P(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(P(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}});M.mergeOptions({zoomControl:!0}),M.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Pi,this.addControl(this.zoomControl))});var Za=function(t){return new Pi(t)},bn=at.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=C("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=C("div",e,i)),t.imperial&&(this._iScale=C("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t),i=e<1e3?e+" m":e/1e3+" km";this._updateScale(this._mScale,i,e/t)},_updateImperial:function(t){var e=t*3.2808399,i,n,o;e>5280?(i=e/5280,n=this._getRoundNum(i),this._updateScale(this._iScale,n+" mi",n/i)):(o=this._getRoundNum(e),this._updateScale(this._iScale,o+" ft",o/e))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1,e*i}}),Aa=function(t){return new bn(t)},Na='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',xi=at.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(_.inlineSvg?Na+" ":"")+"Leaflet</a>"},initialize:function(t){E(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=C("div","leaflet-control-attribution"),oe(this._container);for(var e in t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}});M.mergeOptions({attributionControl:!0}),M.addInitHook(function(){this.options.attributionControl&&new xi().addTo(this)});var Ra=function(t){return new xi(t)};at.Layers=wn,at.Zoom=Pi,at.Scale=bn,at.Attribution=xi,ae.layers=Ba,ae.zoom=Za,ae.scale=Aa,ae.attribution=Ra;var dt=mt.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});dt.addTo=function(t,e){return t.addHandler(e,this),this};var Fa={Events:Q},Ln=_.touch?"touchstart mousedown":"mousedown",Lt=Xt.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){E(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(b(this._dragStartTarget,Ln,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Lt._dragging===this&&this.finishDrag(!0),z(this._dragStartTarget,Ln,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!li(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){Lt._dragging===this&&this.finishDrag();return}if(!(Lt._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(Lt._dragging=this,this._preventOutline&&_i(this._element),mi(),ee(),!this._moving)){this.fire("down");var e=t.touches?t.touches[0]:t,i=fn(this._element);this._startPoint=new w(e.clientX,e.clientY),this._startPos=Tt(this._element),this._parentScale=vi(i);var n=t.type==="mousedown";b(document,n?"mousemove":"touchmove",this._onMove,this),b(document,n?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var e=t.touches&&t.touches.length===1?t.touches[0]:t,i=new w(e.clientX,e.clientY)._subtract(this._startPoint);!i.x&&!i.y||Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(i.x/=this._parentScale.x,i.y/=this._parentScale.y,j(t),this._moved||(this.fire("dragstart"),this._moved=!0,P(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),P(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),F(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){N(document.body,"leaflet-dragging"),this._lastTarget&&(N(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),z(document,"mousemove touchmove",this._onMove,this),z(document,"mouseup touchend touchcancel",this._onUp,this),fi(),ie();var e=this._moved&&this._moving;this._moving=!1,Lt._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function Pn(t,e,i){var n,o=[1,4,2,8],a,s,r,c,d,p,v,x;for(a=0,p=t.length;a<p;a++)t[a]._code=It(t[a],e);for(r=0;r<4;r++){for(v=o[r],n=[],a=0,p=t.length,s=p-1;a<p;s=a++)c=t[a],d=t[s],c._code&v?d._code&v||(x=xe(d,c,v,e,i),x._code=It(x,e),n.push(x)):(d._code&v&&(x=xe(d,c,v,e,i),x._code=It(x,e),n.push(x)),n.push(c));t=n}return t}function xn(t,e){var i,n,o,a,s,r,c,d,p;if(!t||t.length===0)throw new Error("latlngs not passed");it(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var v=T([0,0]),x=R(t),U=x.getNorthWest().distanceTo(x.getSouthWest())*x.getNorthEast().distanceTo(x.getNorthWest());U<1700&&(v=Si(t));var W=t.length,nt=[];for(i=0;i<W;i++){var $=T(t[i]);nt.push(e.project(T([$.lat-v.lat,$.lng-v.lng])))}for(r=c=d=0,i=0,n=W-1;i<W;n=i++)o=nt[i],a=nt[n],s=o.y*a.x-a.y*o.x,c+=(o.x+a.x)*s,d+=(o.y+a.y)*s,r+=s*3;r===0?p=nt[0]:p=[c/r,d/r];var Ft=e.unproject(y(p));return T([Ft.lat+v.lat,Ft.lng+v.lng])}function Si(t){for(var e=0,i=0,n=0,o=0;o<t.length;o++){var a=T(t[o]);e+=a.lat,i+=a.lng,n++}return T([e/n,i/n])}var Ha={__proto__:null,clipPolygon:Pn,polygonCenter:xn,centroid:Si};function Sn(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=ja(t,i),t=Wa(t,i),t}function kn(t,e,i){return Math.sqrt(se(t,e,i,!0))}function Va(t,e,i){return se(t,e,i)}function Wa(t,e){var i=t.length,n=typeof Uint8Array<"u"?Uint8Array:Array,o=new n(i);o[0]=o[i-1]=1,ki(t,o,e,0,i-1);var a,s=[];for(a=0;a<i;a++)o[a]&&s.push(t[a]);return s}function ki(t,e,i,n,o){var a=0,s,r,c;for(r=n+1;r<=o-1;r++)c=se(t[r],t[n],t[o],!0),c>a&&(s=r,a=c);a>i&&(e[s]=1,ki(t,e,i,n,s),ki(t,e,i,s,o))}function ja(t,e){for(var i=[t[0]],n=1,o=0,a=t.length;n<a;n++)Ga(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<a-1&&i.push(t[a-1]),i}var Tn;function Mn(t,e,i,n,o){var a=n?Tn:It(t,i),s=It(e,i),r,c,d;for(Tn=s;;){if(!(a|s))return[t,e];if(a&s)return!1;r=a||s,c=xe(t,e,r,i,o),d=It(c,i),r===a?(t=c,a=d):(e=c,s=d)}}function xe(t,e,i,n,o){var a=e.x-t.x,s=e.y-t.y,r=n.min,c=n.max,d,p;return i&8?(d=t.x+a*(c.y-t.y)/s,p=c.y):i&4?(d=t.x+a*(r.y-t.y)/s,p=r.y):i&2?(d=c.x,p=t.y+s*(c.x-t.x)/a):i&1&&(d=r.x,p=t.y+s*(r.x-t.x)/a),new w(d,p,o)}function It(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function Ga(t,e){var i=e.x-t.x,n=e.y-t.y;return i*i+n*n}function se(t,e,i,n){var o=e.x,a=e.y,s=i.x-o,r=i.y-a,c=s*s+r*r,d;return c>0&&(d=((t.x-o)*s+(t.y-a)*r)/c,d>1?(o=i.x,a=i.y):d>0&&(o+=s*d,a+=r*d)),s=t.x-o,r=t.y-a,n?s*s+r*r:new w(o,a)}function it(t){return!ot(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]<"u"}function Cn(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),it(t)}function In(t,e){var i,n,o,a,s,r,c,d;if(!t||t.length===0)throw new Error("latlngs not passed");it(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var p=T([0,0]),v=R(t),x=v.getNorthWest().distanceTo(v.getSouthWest())*v.getNorthEast().distanceTo(v.getNorthWest());x<1700&&(p=Si(t));var U=t.length,W=[];for(i=0;i<U;i++){var nt=T(t[i]);W.push(e.project(T([nt.lat-p.lat,nt.lng-p.lng])))}for(i=0,n=0;i<U-1;i++)n+=W[i].distanceTo(W[i+1])/2;if(n===0)d=W[0];else for(i=0,a=0;i<U-1;i++)if(s=W[i],r=W[i+1],o=s.distanceTo(r),a+=o,a>n){c=(a-n)/o,d=[r.x-c*(r.x-s.x),r.y-c*(r.y-s.y)];break}var $=e.unproject(y(d));return T([$.lat+p.lat,$.lng+p.lng])}var Ua={__proto__:null,simplify:Sn,pointToSegmentDistance:kn,closestPointOnSegment:Va,clipSegment:Mn,_getEdgeIntersection:xe,_getBitCode:It,_sqClosestPointOnSegment:se,isFlat:it,_flat:Cn,polylineCenter:In},Ti={project:function(t){return new w(t.lng,t.lat)},unproject:function(t){return new I(t.y,t.x)},bounds:new B([-180,-90],[180,90])},Mi={R:6378137,R_MINOR:6356752314245179e-9,bounds:new B([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,a=Math.sqrt(1-o*o),s=a*Math.sin(n),r=Math.tan(Math.PI/4-n/2)/Math.pow((1-s)/(1+s),a/2);return n=-i*Math.log(Math.max(r,1e-10)),new w(t.lng*e*i,n)},unproject:function(t){for(var e=180/Math.PI,i=this.R,n=this.R_MINOR/i,o=Math.sqrt(1-n*n),a=Math.exp(-t.y/i),s=Math.PI/2-2*Math.atan(a),r=0,c=.1,d;r<15&&Math.abs(c)>1e-7;r++)d=o*Math.sin(s),d=Math.pow((1-d)/(1+d),o/2),c=Math.PI/2-2*Math.atan(a*d)-s,s+=c;return new I(s*e,t.x*e/i)}},Ja={__proto__:null,LonLat:Ti,Mercator:Mi,SphericalMercator:ti},qa=l({},bt,{code:"EPSG:3395",projection:Mi,transformation:function(){var t=.5/(Math.PI*Mi.R);return Yt(t,.5,-t,.5)}()}),zn=l({},bt,{code:"EPSG:4326",projection:Ti,transformation:Yt(1/180,1,-1/180,.5)}),Ka=l({},ft,{projection:Ti,transformation:Yt(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,n=e.lat-t.lat;return Math.sqrt(i*i+n*n)},infinite:!0});ft.Earth=bt,ft.EPSG3395=qa,ft.EPSG3857=ii,ft.EPSG900913=ea,ft.EPSG4326=zn,ft.Simple=Ka;var st=Xt.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[k(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[k(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",function(){e.off(i,this)},this)}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}});M.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var e=k(t);return this._layers[e]?this:(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var e=k(t);return this._layers[e]?(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return k(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){t=t?ot(t)?t:[t]:[];for(var e=0,i=t.length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[k(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=k(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var n in this._zoomBoundLayers){var o=this._zoomBoundLayers[n].options;t=o.minZoom===void 0?t:Math.min(t,o.minZoom),e=o.maxZoom===void 0?e:Math.max(e,o.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var Bt=st.extend({initialize:function(t,e){E(this,e),this._layers={};var i,n;if(t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){var e=typeof t=="number"?t:this.getLayerId(t);return e in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e=Array.prototype.slice.call(arguments,1),i,n;for(i in this._layers)n=this._layers[i],n[t]&&n[t].apply(n,e);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return k(t)}}),Xa=function(t,e){return new Bt(t,e)},pt=Bt.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),Bt.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),Bt.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new Y;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),Ya=function(t,e){return new pt(t,e)},Zt=mt.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){E(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var n=this._createImg(i,e&&e.tagName==="IMG"?e:null);return this._setIconStyles(n,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),n},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"];typeof n=="number"&&(n=[n,n]);var o=y(n),a=y(e==="shadow"&&i.shadowAnchor||i.iconAnchor||o&&o.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),a&&(t.style.marginLeft=-a.x+"px",t.style.marginTop=-a.y+"px"),o&&(t.style.width=o.x+"px",t.style.height=o.y+"px")},_createImg:function(t,e){return e=e||document.createElement("img"),e.src=t,e},_getIconUrl:function(t){return _.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function $a(t){return new Zt(t)}var re=Zt.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof re.imagePath!="string"&&(re.imagePath=this._detectIconPath()),(this.options.imagePath||re.imagePath)+Zt.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var e=function(i,n,o){var a=n.exec(i);return a&&a[o]};return t=e(t,/^url\((['"])?(.+)\1\)$/,2),t&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=C("div","leaflet-default-icon-path",document.body),e=te(t,"background-image")||te(t,"backgroundImage");if(document.body.removeChild(t),e=this._stripUrl(e),e)return e;var i=document.querySelector('link[href$="leaflet.css"]');return i?i.href.substring(0,i.href.length-11-1):""}}),On=dt.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new Lt(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),P(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&N(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,a=Tt(e._icon),s=i.getPixelBounds(),r=i.getPixelOrigin(),c=X(s.min._subtract(r).add(o),s.max._subtract(r).subtract(o));if(!c.contains(a)){var d=y((Math.max(c.max.x,a.x)-c.max.x)/(s.max.x-c.max.x)-(Math.min(c.min.x,a.x)-c.min.x)/(s.min.x-c.min.x),(Math.max(c.max.y,a.y)-c.max.y)/(s.max.y-c.max.y)-(Math.min(c.min.y,a.y)-c.min.y)/(s.min.y-c.min.y)).multiplyBy(n);i.panBy(d,{animate:!1}),this._draggable._newPos._add(d),this._draggable._startPos._add(d),F(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=K(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(tt(this._panRequest),this._panRequest=K(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=Tt(e._icon),o=e._map.layerPointToLatLng(n);i&&F(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){tt(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),Se=st.extend({options:{icon:new re,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){E(this,e),this._latlng=T(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=T(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1;i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),i.tagName==="IMG"&&(i.alt=t.alt||"")),P(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&b(i,"focus",this._panOnFocus,this);var o=t.icon.createShadow(this._shadow),a=!1;o!==this._shadow&&(this._removeShadow(),a=!0),o&&(P(o,e),o.alt=""),this._shadow=o,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),o&&a&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&z(this._icon,"focus",this._panOnFocus,this),Z(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Z(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&F(this._icon,t),this._shadow&&F(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(P(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),On)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new On(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&et(this._icon,t),this._shadow&&et(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var e=this.options.icon.options,i=e.iconSize?y(e.iconSize):y(0,0),n=e.iconAnchor?y(e.iconAnchor):y(0,0);t.panInside(this._latlng,{paddingTopLeft:n,paddingBottomRight:i.subtract(n)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Qa(t,e){return new Se(t,e)}var Pt=st.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return E(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),ke=Pt.extend({options:{fill:!0,radius:10},initialize:function(t,e){E(this,e),this._latlng=T(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=T(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return Pt.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),n=[t+i,e+i];this._pxBounds=new B(this._point.subtract(n),this._point.add(n))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function ts(t,e){return new ke(t,e)}var Ci=ke.extend({initialize:function(t,e,i){if(typeof e=="number"&&(e=l({},i,{radius:e})),E(this,e),this._latlng=T(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new Y(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:Pt.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,n=i.options.crs;if(n.distance===bt.distance){var o=Math.PI/180,a=this._mRadius/bt.R/o,s=i.project([e+a,t]),r=i.project([e-a,t]),c=s.add(r).divideBy(2),d=i.unproject(c).lat,p=Math.acos((Math.cos(a*o)-Math.sin(e*o)*Math.sin(d*o))/(Math.cos(e*o)*Math.cos(d*o)))/o;(isNaN(p)||p===0)&&(p=a/Math.cos(Math.PI/180*e)),this._point=c.subtract(i.getPixelOrigin()),this._radius=isNaN(p)?0:c.x-i.project([d,t-p]).x,this._radiusY=c.y-s.y}else{var v=n.unproject(n.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(v).x}this._updateBounds()}});function es(t,e,i){return new Ci(t,e,i)}var _t=Pt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){E(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e=1/0,i=null,n=se,o,a,s=0,r=this._parts.length;s<r;s++)for(var c=this._parts[s],d=1,p=c.length;d<p;d++){o=c[d-1],a=c[d];var v=n(t,o,a,!0);v<e&&(e=v,i=n(t,o,a))}return i&&(i.distance=Math.sqrt(e)),i},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return In(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=T(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new Y,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return it(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=it(t),n=0,o=t.length;n<o;n++)i?(e[n]=T(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new B;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),e=new w(t,t);this._rawPxBounds&&(this._pxBounds=new B([this._rawPxBounds.min.subtract(e),this._rawPxBounds.max.add(e)]))},_projectLatlngs:function(t,e,i){var n=t[0]instanceof I,o=t.length,a,s;if(n){for(s=[],a=0;a<o;a++)s[a]=this._map.latLngToLayerPoint(t[a]),i.extend(s[a]);e.push(s)}else for(a=0;a<o;a++)this._projectLatlngs(t[a],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var e=this._parts,i,n,o,a,s,r,c;for(i=0,o=0,a=this._rings.length;i<a;i++)for(c=this._rings[i],n=0,s=c.length;n<s-1;n++)r=Mn(c[n],c[n+1],t,n,!0),r&&(e[o]=e[o]||[],e[o].push(r[0]),(r[1]!==c[n+1]||n===s-2)&&(e[o].push(r[1]),o++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=Sn(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,a,s,r,c=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,a=this._parts.length;i<a;i++)for(r=this._parts[i],n=0,s=r.length,o=s-1;n<s;o=n++)if(!(!e&&n===0)&&kn(t,r[o],r[n])<=c)return!0;return!1}});function is(t,e){return new _t(t,e)}_t._flat=Cn;var At=_t.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return xn(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var e=_t.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof I&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){_t.prototype._setLatLngs.call(this,t),it(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return it(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new w(e,e);if(t=new B(t.min.subtract(i),t.max.add(i)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var n=0,o=this._rings.length,a;n<o;n++)a=Pn(this._rings[n],t,!0),a.length&&this._parts.push(a)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e=!1,i,n,o,a,s,r,c,d;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(a=0,c=this._parts.length;a<c;a++)for(i=this._parts[a],s=0,d=i.length,r=d-1;s<d;r=s++)n=i[s],o=i[r],n.y>t.y!=o.y>t.y&&t.x<(o.x-n.x)*(t.y-n.y)/(o.y-n.y)+n.x&&(e=!e);return e||_t.prototype._containsPoint.call(this,t,!0)}});function ns(t,e){return new At(t,e)}var vt=pt.extend({initialize:function(t,e){E(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e=ot(t)?t:t.features,i,n,o;if(e){for(i=0,n=e.length;i<n;i++)o=e[i],(o.geometries||o.geometry||o.features||o.coordinates)&&this.addData(o);return this}var a=this.options;if(a.filter&&!a.filter(t))return this;var s=Te(t,a);return s?(s.feature=Ie(t),s.defaultOptions=s.options,this.resetStyle(s),a.onEachFeature&&a.onEachFeature(t,s),this.addLayer(s)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=l({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&(typeof e=="function"&&(e=e(t.feature)),t.setStyle(e))}});function Te(t,e){var i=t.type==="Feature"?t.geometry:t,n=i?i.coordinates:null,o=[],a=e&&e.pointToLayer,s=e&&e.coordsToLatLng||Ii,r,c,d,p;if(!n&&!i)return null;switch(i.type){case"Point":return r=s(n),En(a,t,r,e);case"MultiPoint":for(d=0,p=n.length;d<p;d++)r=s(n[d]),o.push(En(a,t,r,e));return new pt(o);case"LineString":case"MultiLineString":return c=Me(n,i.type==="LineString"?0:1,s),new _t(c,e);case"Polygon":case"MultiPolygon":return c=Me(n,i.type==="Polygon"?1:2,s),new At(c,e);case"GeometryCollection":for(d=0,p=i.geometries.length;d<p;d++){var v=Te({geometry:i.geometries[d],type:"Feature",properties:t.properties},e);v&&o.push(v)}return new pt(o);case"FeatureCollection":for(d=0,p=i.features.length;d<p;d++){var x=Te(i.features[d],e);x&&o.push(x)}return new pt(o);default:throw new Error("Invalid GeoJSON object.")}}function En(t,e,i,n){return t?t(e,i):new Se(i,n&&n.markersInheritOptions&&n)}function Ii(t){return new I(t[1],t[0],t[2])}function Me(t,e,i){for(var n=[],o=0,a=t.length,s;o<a;o++)s=e?Me(t[o],e-1,i):(i||Ii)(t[o]),n.push(s);return n}function zi(t,e){return t=T(t),t.alt!==void 0?[ct(t.lng,e),ct(t.lat,e),ct(t.alt,e)]:[ct(t.lng,e),ct(t.lat,e)]}function Ce(t,e,i,n){for(var o=[],a=0,s=t.length;a<s;a++)o.push(e?Ce(t[a],it(t[a])?0:e-1,i,n):zi(t[a],n));return!e&&i&&o.length>0&&o.push(o[0].slice()),o}function Nt(t,e){return t.feature?l({},t.feature,{geometry:e}):Ie(e)}function Ie(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var Oi={toGeoJSON:function(t){return Nt(this,{type:"Point",coordinates:zi(this.getLatLng(),t)})}};Se.include(Oi),Ci.include(Oi),ke.include(Oi),_t.include({toGeoJSON:function(t){var e=!it(this._latlngs),i=Ce(this._latlngs,e?1:0,!1,t);return Nt(this,{type:(e?"Multi":"")+"LineString",coordinates:i})}}),At.include({toGeoJSON:function(t){var e=!it(this._latlngs),i=e&&!it(this._latlngs[0]),n=Ce(this._latlngs,i?2:e?1:0,!0,t);return e||(n=[n]),Nt(this,{type:(i?"Multi":"")+"Polygon",coordinates:n})}}),Bt.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),Nt(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(e==="MultiPoint")return this.toMultiPoint(t);var i=e==="GeometryCollection",n=[];return this.eachLayer(function(o){if(o.toGeoJSON){var a=o.toGeoJSON(t);if(i)n.push(a.geometry);else{var s=Ie(a);s.type==="FeatureCollection"?n.push.apply(n,s.features):n.push(s)}}}),i?Nt(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});function Dn(t,e){return new vt(t,e)}var os=Dn,ze=st.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=R(e),E(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(P(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Z(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Et(this._image),this},bringToBack:function(){return this._map&&Dt(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=R(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",e=this._image=t?this._url:C("img");if(P(e,"leaflet-image-layer"),this._zoomAnimated&&P(e,"leaflet-zoom-animated"),this.options.className&&P(e,this.options.className),e.onselectstart=A,e.onmousemove=A,e.onload=m(this.fire,this,"load"),e.onerror=m(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(e.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=e.src;return}e.src=this._url,e.alt=this.options.alt},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;kt(this._image,i,e)},_reset:function(){var t=this._image,e=new B(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();F(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){et(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),as=function(t,e,i){return new ze(t,e,i)},Bn=ze.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",e=this._image=t?this._url:C("video");if(P(e,"leaflet-image-layer"),this._zoomAnimated&&P(e,"leaflet-zoom-animated"),this.options.className&&P(e,this.options.className),e.onselectstart=A,e.onmousemove=A,e.onloadeddata=m(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],o=0;o<i.length;o++)n.push(i[o].src);this._url=i.length>0?n:[e.src];return}ot(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var a=0;a<this._url.length;a++){var s=C("source");s.src=this._url[a],e.appendChild(s)}}});function ss(t,e,i){return new Bn(t,e,i)}var Zn=ze.extend({_initImage:function(){var t=this._image=this._url;P(t,"leaflet-image-layer"),this._zoomAnimated&&P(t,"leaflet-zoom-animated"),this.options.className&&P(t,this.options.className),t.onselectstart=A,t.onmousemove=A}});function rs(t,e,i){return new Zn(t,e,i)}var ut=st.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,e){t&&(t instanceof I||ot(t))?(this._latlng=T(t),E(this,e)):(E(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&et(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&et(this._container,1),this.bringToFront(),this.options.interactive&&(P(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(et(this._container,0),this._removeTimeout=setTimeout(m(Z,void 0,this._container),200)):Z(this._container),this.options.interactive&&(N(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=T(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Et(this._container),this},bringToBack:function(){return this._map&&Dt(this._container),this},_prepareOpen:function(t){var e=this._source;if(!e._map)return!1;if(e instanceof pt){e=null;var i=this._source._layers;for(var n in i)if(i[n]._map){e=i[n];break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else if(e.getBounds)t=e.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof e=="string")t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=y(this.options.offset),i=this._getAnchor();this._zoomAnimated?F(this._container,t.add(i)):e=e.add(t).add(i);var n=this._containerBottom=-e.y,o=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=n+"px",this._container.style.left=o+"px"}},_getAnchor:function(){return[0,0]}});M.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),st.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(E(o,n),o._source=this):(o=e&&!n?e:new t(n,this),o.setContent(i)),o}});var Oe=ut.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,ut.prototype.openOn.call(this,t)},onAdd:function(t){ut.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Pt||this._source.on("preclick",Mt))},onRemove:function(t){ut.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Pt||this._source.off("preclick",Mt))},getEvents:function(){var t=ut.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=C("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=C("div",t+"-content-wrapper",e);if(this._contentNode=C("div",t+"-content",i),oe(e),bi(this._contentNode),b(e,"contextmenu",Mt),this._tipContainer=C("div",t+"-tip-container",e),this._tip=C("div",t+"-tip",this._tipContainer),this.options.closeButton){var n=this._closeButton=C("a",t+"-close-button",e);n.setAttribute("role","button"),n.setAttribute("aria-label","Close popup"),n.href="#close",n.innerHTML='<span aria-hidden="true">&#215;</span>',b(n,"click",function(o){j(o),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;i=Math.min(i,this.options.maxWidth),i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="";var n=t.offsetHeight,o=this.options.maxHeight,a="leaflet-popup-scrolled";o&&n>o?(e.height=o+"px",P(t,a)):N(t,a),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();F(this._container,e.add(i))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,e=parseInt(te(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,n=this._containerWidth,o=new w(this._containerLeft,-i-this._containerBottom);o._add(Tt(this._container));var a=t.layerPointToContainerPoint(o),s=y(this.options.autoPanPadding),r=y(this.options.autoPanPaddingTopLeft||s),c=y(this.options.autoPanPaddingBottomRight||s),d=t.getSize(),p=0,v=0;a.x+n+c.x>d.x&&(p=a.x+n-d.x+c.x),a.x-p-r.x<0&&(p=a.x-r.x),a.y+i+c.y>d.y&&(v=a.y+i-d.y+c.y),a.y-v-r.y<0&&(v=a.y-r.y),(p||v)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([p,v]))}},_getAnchor:function(){return y(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),cs=function(t,e){return new Oe(t,e)};M.mergeOptions({closePopupOnClick:!0}),M.include({openPopup:function(t,e,i){return this._initOverlay(Oe,t,e,i).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),st.include({bindPopup:function(t,e){return this._popup=this._initOverlay(Oe,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof pt||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){Ct(t);var e=t.layer||t.target;if(this._popup._source===e&&!(e instanceof Pt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=e,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var Ee=ut.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){ut.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){ut.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=ut.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",e=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=C("div",e),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+k(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i,n=this._map,o=this._container,a=n.latLngToContainerPoint(n.getCenter()),s=n.layerPointToContainerPoint(t),r=this.options.direction,c=o.offsetWidth,d=o.offsetHeight,p=y(this.options.offset),v=this._getAnchor();r==="top"?(e=c/2,i=d):r==="bottom"?(e=c/2,i=0):r==="center"?(e=c/2,i=d/2):r==="right"?(e=0,i=d/2):r==="left"?(e=c,i=d/2):s.x<a.x?(r="right",e=0,i=d/2):(r="left",e=c+(p.x+v.x)*2,i=d/2),t=t.subtract(y(e,i,!0)).add(p).add(v),N(o,"leaflet-tooltip-right"),N(o,"leaflet-tooltip-left"),N(o,"leaflet-tooltip-top"),N(o,"leaflet-tooltip-bottom"),P(o,"leaflet-tooltip-"+r),F(o,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&et(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return y(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),hs=function(t,e){return new Ee(t,e)};M.include({openTooltip:function(t,e,i){return this._initOverlay(Ee,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),st.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Ee,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?i.add=this._openTooltip:(i.mouseover=this._openTooltip,i.mouseout=this.closeTooltip,i.click=this._openTooltip,this._map?this._addFocusListeners():i.add=this._addFocusListeners),this._tooltip.options.sticky&&(i.mousemove=this._moveTooltip),this[e](i),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof pt||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&(b(e,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),b(e,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&e.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var e=this;this._map.once("moveend",function(){e._openOnceFlag=!1,e._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var e=t.latlng,i,n;this._tooltip.options.sticky&&t.originalEvent&&(i=this._map.mouseEventToContainerPoint(t.originalEvent),n=this._map.containerPointToLayerPoint(i),e=this._map.layerPointToLatLng(n)),this._tooltip.setLatLng(e)}});var An=Zt.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&t.tagName==="DIV"?t:document.createElement("div"),i=this.options;if(i.html instanceof Element?(ye(e),e.appendChild(i.html)):e.innerHTML=i.html!==!1?i.html:"",i.bgPos){var n=y(i.bgPos);e.style.backgroundPosition=-n.x+"px "+-n.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});function ls(t){return new An(t)}Zt.Default=re;var ce=st.extend({options:{tileSize:256,opacity:1,updateWhenIdle:_.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){E(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),Z(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Et(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Dt(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=pe(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof w?t:new w(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e=this.getPane().children,i=-t(-1/0,1/0),n=0,o=e.length,a;n<o;n++)a=e[n].style.zIndex,e[n]!==this._container&&a&&(i=t(i,+a));isFinite(i)&&(this.options.zIndex=i+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!_.ielt9){et(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var n in this._tiles){var o=this._tiles[n];if(!(!o.current||!o.loaded)){var a=Math.min(1,(t-o.loaded)/200);et(o.el,a),a<1?e=!0:(o.active?i=!0:this._onOpaqueTile(o),o.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(tt(this._fadeFrame),this._fadeFrame=K(this._updateOpacity,this))}},_onOpaqueTile:A,_initContainer:function(){this._container||(this._container=C("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(t!==void 0){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(Z(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||(n=this._levels[t]={},n.el=C("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),A(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n,n}},_onUpdateLevel:A,_onRemoveLevel:A,_onCreateLevel:A,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)e=this._tiles[t],e.retain=e.current;for(t in this._tiles)if(e=this._tiles[t],e.current&&!e.active){var n=e.coords;this._retainParent(n.x,n.y,n.z,n.z-5)||this._retainChildren(n.x,n.y,n.z,n.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)Z(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var o=Math.floor(t/2),a=Math.floor(e/2),s=i-1,r=new w(+o,+a);r.z=+s;var c=this._tileCoordsToKey(r),d=this._tiles[c];return d&&d.active?(d.retain=!0,!0):(d&&d.loaded&&(d.retain=!0),s>n?this._retainParent(o,a,s,n):!1)},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var a=2*e;a<2*e+2;a++){var s=new w(o,a);s.z=i+1;var r=this._tileCoordsToKey(s),c=this._tiles[r];if(c&&c.active){c.retain=!0;continue}else c&&c.loaded&&(c.retain=!0);i+1<n&&this._retainChildren(o,a,i+1,n)}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return e.minNativeZoom!==void 0&&t<e.minNativeZoom?e.minNativeZoom:e.maxNativeZoom!==void 0&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e);this.options.maxZoom!==void 0&&o>this.options.maxZoom||this.options.minZoom!==void 0&&o<this.options.minZoom?o=void 0:o=this._clampZoom(o);var a=this.options.updateWhenZooming&&o!==this._tileZoom;(!n||a)&&(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),o!==void 0&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),o=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();_.any3d?kt(t.el,o,n):F(t.el,o)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),n=e.getZoomScale(i,this._tileZoom),o=e.project(t,this._tileZoom).floor(),a=e.getSize().divideBy(n*2);return new B(o.subtract(a),o.add(a))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(t===void 0&&(t=e.getCenter()),this._tileZoom!==void 0){var n=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(n),a=o.getCenter(),s=[],r=this.options.keepBuffer,c=new B(o.getBottomLeft().subtract([r,-r]),o.getTopRight().add([r,-r]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var d in this._tiles){var p=this._tiles[d].coords;(p.z!==this._tileZoom||!c.contains(new w(p.x,p.y)))&&(this._tiles[d].current=!1)}if(Math.abs(i-this._tileZoom)>1){this._setView(t,i);return}for(var v=o.min.y;v<=o.max.y;v++)for(var x=o.min.x;x<=o.max.x;x++){var U=new w(x,v);if(U.z=this._tileZoom,!!this._isValidTile(U)){var W=this._tiles[this._tileCoordsToKey(U)];W?W.current=!0:s.push(U)}}if(s.sort(function($,Ft){return $.distanceTo(a)-Ft.distanceTo(a)}),s.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var nt=document.createDocumentFragment();for(x=0;x<s.length;x++)this._addTile(s[x],nt);this._level.el.appendChild(nt)}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var n=this._tileCoordsToBounds(t);return R(this.options.bounds).overlaps(n)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),o=n.add(i),a=e.unproject(n,t.z),s=e.unproject(o,t.z);return[a,s]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new Y(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new w(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(Z(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){P(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=A,t.onmousemove=A,_.ielt9&&this.options.opacity<1&&et(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),o=this.createTile(this._wrapCoords(t),m(this._tileReady,this,t));this._initTile(o),this.createTile.length<2&&K(m(this._tileReady,this,t,null,o)),F(o,i),this._tiles[n]={el:o,coords:t,current:!0},e.appendChild(o),this.fire("tileloadstart",{tile:o,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var n=this._tileCoordsToKey(t);i=this._tiles[n],i&&(i.loaded=+new Date,this._map._fadeAnimated?(et(i.el,0),tt(this._fadeFrame),this._fadeFrame=K(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(P(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),_.ielt9||!this._map._fadeAnimated?K(this._pruneTiles,this):setTimeout(m(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new w(this._wrapX?Kt(t.x,this._wrapX):t.x,this._wrapY?Kt(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new B(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function ds(t){return new ce(t)}var Rt=ce.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,e=E(this,e),e.detectRetina&&_.retina&&e.maxZoom>0?(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom=Math.min(e.maxZoom,e.minZoom+1)):(e.zoomOffset++,e.maxZoom=Math.max(e.minZoom,e.maxZoom-1)),e.minZoom=Math.max(0,e.minZoom)):e.zoomReverse?e.minZoom=Math.min(e.maxZoom,e.minZoom):e.maxZoom=Math.max(e.minZoom,e.maxZoom),typeof e.subdomains=="string"&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&e===void 0&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return b(i,"load",m(this._tileOnLoad,this,e,i)),b(i,"error",m(this._tileOnError,this,e,i)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(i.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl:function(t){var e={r:_.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var i=this._globalTileRange.max.y-t.y;this.options.tms&&(e.y=i),e["-y"]=i}return Ni(this._url,l(e,this.options))},_tileOnLoad:function(t,e){_.ielt9?setTimeout(m(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom,i=this.options.zoomReverse,n=this.options.zoomOffset;return i&&(t=e-t),t+n},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(e=this._tiles[t].el,e.onload=A,e.onerror=A,!e.complete)){e.src=_e;var i=this._tiles[t].coords;Z(e),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})}},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",_e),ce.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(!(!this._map||i&&i.getAttribute("src")===_e))return ce.prototype._tileReady.call(this,t,e,i)}});function Nn(t,e){return new Rt(t,e)}var Rn=Rt.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,e){this._url=t;var i=l({},this.defaultWmsParams);for(var n in e)n in this.options||(i[n]=e[n]);e=E(this,e);var o=e.detectRetina&&_.retina?2:1,a=this.getTileSize();i.width=a.x*o,i.height=a.y*o,this.wmsParams=i},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,Rt.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,n=X(i.project(e[0]),i.project(e[1])),o=n.min,a=n.max,s=(this._wmsVersion>=1.3&&this._crs===zn?[o.y,o.x,a.y,a.x]:[o.x,o.y,a.x,a.y]).join(","),r=Rt.prototype.getTileUrl.call(this,t);return r+Ai(this.wmsParams,r,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+s},setParams:function(t,e){return l(this.wmsParams,t),e||this.redraw(),this}});function us(t,e){return new Rn(t,e)}Rt.WMS=Rn,Nn.wms=us;var gt=st.extend({options:{padding:.1},initialize:function(t){E(this,t),k(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),P(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),a=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));_.any3d?kt(this._container,a,i):F(this._container,a)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new B(i,i.add(e.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Fn=gt.extend({options:{tolerance:0},getEvents:function(){var t=gt.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){gt.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");b(t,"mousemove",this._onMouseMove,this),b(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),b(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){tt(this._redrawRequest),delete this._ctx,Z(this._container),z(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var e in this._layers)t=this._layers[e],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){gt.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),n=_.retina?2:1;F(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",_.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){gt.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[k(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,n=e.prev;i?i.prev=n:this._drawLast=n,n?n.next=i:this._drawFirst=i,delete t._order,delete this._layers[k(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var e=t.options.dashArray.split(/[, ]+/),i=[],n,o;for(o=0;o<e.length;o++){if(n=Number(e[o]),isNaN(n))return;i.push(n)}t.options._dashArray=i}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||K(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new B,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,a,s=t._parts,r=s.length,c=this._ctx;if(r){for(c.beginPath(),i=0;i<r;i++){for(n=0,o=s[i].length;n<o;n++)a=s[i][n],c[n?"lineTo":"moveTo"](a.x,a.y);e&&c.closePath()}this._fillStroke(c,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),o=(Math.max(Math.round(t._radiusY),1)||n)/n;o!==1&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,Math.PI*2,!1),o!==1&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&i.weight!==0&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e=this._map.mouseEventToLayerPoint(t),i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(i))&&(n=i);this._fireEvent(n?[n]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(N(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(P(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(m(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(i)i.prev=n;else return;n?n.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t)}},_bringToBack:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(n)n.next=i;else return;i?i.prev=n:n&&(this._drawLast=n),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t)}}});function Hn(t){return _.canvas?new Fn(t):null}var he=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch{}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),ms={_initContainer:function(){this._container=C("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(gt.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=he("shape");P(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=he("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[k(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;Z(e),t.removeInteractiveTarget(e),delete this._layers[k(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e||(e=t._stroke=he("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=ot(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i||(i=t._fill=he("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,"+65535*360)},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){Et(t._container)},_bringToBack:function(t){Dt(t._container)}},De=_.vml?he:ji,le=gt.extend({_initContainer:function(){this._container=De("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=De("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Z(this._container),z(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){gt.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;(!this._svgSize||!this._svgSize.equals(e))&&(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),F(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=De("path");t.options.className&&P(e,t.options.className),t.options.interactive&&P(e,"leaflet-interactive"),this._updateStyle(t),this._layers[k(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){Z(t._path),t.removeInteractiveTarget(t._path),delete this._layers[k(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,Gi(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n=Math.max(Math.round(t._radiusY),1)||i,o="a"+i+","+n+" 0 1,0 ",a=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+i*2+",0 "+o+-i*2+",0 ";this._setPath(t,a)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){Et(t._path)},_bringToBack:function(t){Dt(t._path)}});_.vml&&le.include(ms);function Vn(t){return _.svg||_.vml?new le(t):null}M.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var e=this._paneRenderers[t];return e===void 0&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&Hn(t)||Vn(t)}});var Wn=At.extend({initialize:function(t,e){At.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=R(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function fs(t,e){return new Wn(t,e)}le.create=De,le.pointsToPath=Gi,vt.geometryToLayer=Te,vt.coordsToLatLng=Ii,vt.coordsToLatLngs=Me,vt.latLngToCoords=zi,vt.latLngsToCoords=Ce,vt.getFeature=Nt,vt.asFeature=Ie,M.mergeOptions({boxZoom:!0});var jn=dt.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){b(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){z(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Z(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),ee(),mi(),this._startPoint=this._map.mouseEventToContainerPoint(t),b(document,{contextmenu:Ct,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=C("div","leaflet-zoom-box",this._container),P(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new B(this._point,this._startPoint),i=e.getSize();F(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(Z(this._box),N(this._container,"leaflet-crosshair")),ie(),fi(),z(document,{contextmenu:Ct,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(m(this._resetState,this),0);var e=new Y(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});M.addInitHook("addHandler","boxZoom",jn),M.mergeOptions({doubleClickZoom:!0});var Gn=dt.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,o=t.originalEvent.shiftKey?i-n:i+n;e.options.doubleClickZoom==="center"?e.setZoom(o):e.setZoomAround(t.containerPoint,o)}});M.addInitHook("addHandler","doubleClickZoom",Gn),M.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var Un=dt.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new Lt(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}P(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){N(this._map._container,"leaflet-grab"),N(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=R(this._map.options.maxBounds);this._offsetLimit=X(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,a=(n+e+i)%t-e-i,s=Math.abs(o+i)<Math.abs(a+i)?o:a;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=s},_onDragEnd:function(t){var e=this._map,i=e.options,n=!i.inertia||t.noInertia||this._times.length<2;if(e.fire("dragend",t),n)e.fire("moveend");else{this._prunePositions(+new Date);var o=this._lastPos.subtract(this._positions[0]),a=(this._lastTime-this._times[0])/1e3,s=i.easeLinearity,r=o.multiplyBy(s/a),c=r.distanceTo([0,0]),d=Math.min(i.inertiaMaxSpeed,c),p=r.multiplyBy(d/c),v=d/(i.inertiaDeceleration*s),x=p.multiplyBy(-v/2).round();!x.x&&!x.y?e.fire("moveend"):(x=e._limitOffset(x,e.options.maxBounds),K(function(){e.panBy(x,{duration:v,easeLinearity:s,noMoveStart:!0,animate:!0})}))}}});M.addInitHook("addHandler","dragging",Un),M.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Jn=dt.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),b(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),z(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,n=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(n,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e=this._panKeys={},i=this.keyCodes,n,o;for(n=0,o=i.left.length;n<o;n++)e[i.left[n]]=[-1*t,0];for(n=0,o=i.right.length;n<o;n++)e[i.right[n]]=[t,0];for(n=0,o=i.down.length;n<o;n++)e[i.down[n]]=[0,t];for(n=0,o=i.up.length;n<o;n++)e[i.up[n]]=[0,-1*t]},_setZoomDelta:function(t){var e=this._zoomKeys={},i=this.keyCodes,n,o;for(n=0,o=i.zoomIn.length;n<o;n++)e[i.zoomIn[n]]=t;for(n=0,o=i.zoomOut.length;n<o;n++)e[i.zoomOut[n]]=-t},_addHooks:function(){b(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){z(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e=t.keyCode,i=this._map,n;if(e in this._panKeys){if(!i._panAnim||!i._panAnim._inProgress)if(n=this._panKeys[e],t.shiftKey&&(n=y(n).multiplyBy(3)),i.options.maxBounds&&(n=i._limitOffset(y(n),i.options.maxBounds)),i.options.worldCopyJump){var o=i.wrapLatLng(i.unproject(i.project(i.getCenter()).add(n)));i.panTo(o)}else i.panBy(n)}else if(e in this._zoomKeys)i.setZoom(i.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[e]);else if(e===27&&i._popup&&i._popup.options.closeOnEscapeKey)i.closePopup();else return;Ct(t)}}});M.addInitHook("addHandler","keyboard",Jn),M.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var qn=dt.extend({addHooks:function(){b(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){z(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=gn(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var n=Math.max(i-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(m(this._performZoom,this),n),Ct(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var n=this._delta/(this._map.options.wheelPxPerZoomLevel*4),o=4*Math.log(2/(1+Math.exp(-Math.abs(n))))/Math.LN2,a=i?Math.ceil(o/i)*i:o,s=t._limitZoom(e+(this._delta>0?a:-a))-e;this._delta=0,this._startTime=null,s&&(t.options.scrollWheelZoom==="center"?t.setZoom(e+s):t.setZoomAround(this._lastMousePos,e+s))}});M.addInitHook("addHandler","scrollWheelZoom",qn);var ps=600;M.mergeOptions({tapHold:_.touchNative&&_.safari&&_.mobile,tapTolerance:15});var Kn=dt.extend({addHooks:function(){b(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){z(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var e=t.touches[0];this._startPos=this._newPos=new w(e.clientX,e.clientY),this._holdTimeout=setTimeout(m(function(){this._cancel(),this._isTapValid()&&(b(document,"touchend",j),b(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),ps),b(document,"touchend touchcancel contextmenu",this._cancel,this),b(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){z(document,"touchend",j),z(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),z(document,"touchend touchcancel contextmenu",this._cancel,this),z(document,"touchmove",this._onMove,this)},_onMove:function(t){var e=t.touches[0];this._newPos=new w(e.clientX,e.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});i._simulated=!0,e.target.dispatchEvent(i)}});M.addInitHook("addHandler","tapHold",Kn),M.mergeOptions({touchZoom:_.touch,bounceAtZoomLimits:!0});var Xn=dt.extend({addHooks:function(){P(this._map._container,"leaflet-touch-zoom"),b(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){N(this._map._container,"leaflet-touch-zoom"),z(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(!(!t.touches||t.touches.length!==2||e._animatingZoom||this._zooming)){var i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),e.options.touchZoom!=="center"&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(n)._divideBy(2))),this._startDist=i.distanceTo(n),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),b(document,"touchmove",this._onTouchMove,this),b(document,"touchend touchcancel",this._onTouchEnd,this),j(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),o=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(o,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&o<1||this._zoom>e.getMaxZoom()&&o>1)&&(this._zoom=e._limitZoom(this._zoom)),e.options.touchZoom==="center"){if(this._center=this._startLatLng,o===1)return}else{var a=i._add(n)._divideBy(2)._subtract(this._centerPoint);if(o===1&&a.x===0&&a.y===0)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(a),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),tt(this._animRequest);var s=m(e._move,e,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=K(s,this,!0),j(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,tt(this._animRequest),z(document,"touchmove",this._onTouchMove,this),z(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});M.addInitHook("addHandler","touchZoom",Xn),M.BoxZoom=jn,M.DoubleClickZoom=Gn,M.Drag=Un,M.Keyboard=Jn,M.ScrollWheelZoom=qn,M.TapHold=Kn,M.TouchZoom=Xn,h.Bounds=B,h.Browser=_,h.CRS=ft,h.Canvas=Fn,h.Circle=Ci,h.CircleMarker=ke,h.Class=mt,h.Control=at,h.DivIcon=An,h.DivOverlay=ut,h.DomEvent=Ea,h.DomUtil=za,h.Draggable=Lt,h.Evented=Xt,h.FeatureGroup=pt,h.GeoJSON=vt,h.GridLayer=ce,h.Handler=dt,h.Icon=Zt,h.ImageOverlay=ze,h.LatLng=I,h.LatLngBounds=Y,h.Layer=st,h.LayerGroup=Bt,h.LineUtil=Ua,h.Map=M,h.Marker=Se,h.Mixin=Fa,h.Path=Pt,h.Point=w,h.PolyUtil=Ha,h.Polygon=At,h.Polyline=_t,h.Popup=Oe,h.PosAnimation=yn,h.Projection=Ja,h.Rectangle=Wn,h.Renderer=gt,h.SVG=le,h.SVGOverlay=Zn,h.TileLayer=Rt,h.Tooltip=Ee,h.Transformation=ei,h.Util=Qo,h.VideoOverlay=Bn,h.bind=m,h.bounds=X,h.canvas=Hn,h.circle=es,h.circleMarker=ts,h.control=ae,h.divIcon=ls,h.extend=l,h.featureGroup=Ya,h.geoJSON=Dn,h.geoJson=os,h.gridLayer=ds,h.icon=$a,h.imageOverlay=as,h.latLng=T,h.latLngBounds=R,h.layerGroup=Xa,h.map=Da,h.marker=Qa,h.point=y,h.polygon=ns,h.polyline=is,h.popup=cs,h.rectangle=fs,h.setOptions=E,h.stamp=k,h.svg=Vn,h.svgOverlay=rs,h.tileLayer=Nn,h.tooltip=hs,h.transformation=Yt,h.version=u,h.videoOverlay=ss;var _s=window.L;h.noConflict=function(){return window.L=_s,this},window.L=h})});var ks=["switch"],Ts=["*"];function Ms(h,u){h&1&&(g(0,"span",10),ao(),g(1,"svg",12),V(2,"path",13),S(),g(3,"svg",14),V(4,"path",15),S()())}var Cs=new no("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),Is={provide:Fo,useExisting:eo(()=>We),multi:!0},Ve=class{source;checked;constructor(u,l){this.source=u,this.checked=l}},We=(()=>{class h{_elementRef=yt(ro);_focusMonitor=yt(Co);_changeDetectorRef=yt(xo);defaults=yt(Cs);_onChange=l=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(l){return new Ve(this,l)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(l){this._checked=l,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new wt;toggleChange=new wt;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){yt(Mo).load(zo);let l=yt(new so("tabindex"),{optional:!0}),f=this.defaults,m=yt(co,{optional:!0});this.tabIndex=l==null?0:parseInt(l)||0,this.color=f.color||"accent",this._noopAnimations=m==="NoopAnimations",this.id=this._uniqueId=yt(Io).getId("mat-mdc-slide-toggle-"),this.hideIcon=f.hideIcon??!1,this.disabledInteractive=f.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(l=>{l==="keyboard"||l==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):l||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(l){l.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(l){this.checked=!!l}registerOnChange(l){this._onChange=l}registerOnTouched(l){this._onTouched=l}validate(l){return this.required&&l.value!==!0?{required:!0}:null}registerOnValidatorChange(l){this._validatorOnChange=l}setDisabledState(l){this.disabled=l,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new Ve(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(f){return new(f||h)};static \u0275cmp=xt({type:h,selectors:[["mat-slide-toggle"]],viewQuery:function(f,m){if(f&1&&go(ks,5),f&2){let O;yo(O=wo())&&(m._switchElement=O.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(f,m){f&2&&(fo("id",m.id),Ze("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),uo(m.color?"mat-"+m.color:""),Di("mat-mdc-slide-toggle-focused",m._focused)("mat-mdc-slide-toggle-checked",m.checked)("_mat-animation-noopable",m._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",zt],color:"color",disabled:[2,"disabled","disabled",zt],disableRipple:[2,"disableRipple","disableRipple",zt],tabIndex:[2,"tabIndex","tabIndex",l=>l==null?0:So(l)],checked:[2,"checked","checked",zt],hideIcon:[2,"hideIcon","hideIcon",zt],disabledInteractive:[2,"disabledInteractive","disabledInteractive",zt]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[Po([Is,{provide:Ho,useExisting:h,multi:!0}]),lo,oo],ngContentSelectors:Ts,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(f,m){if(f&1){let O=Ae();_o(),g(0,"div",1)(1,"button",2,0),rt("click",function(){return ue(O),me(m._handleClick())}),V(3,"span",3),g(4,"span",4)(5,"span",5)(6,"span",6),V(7,"span",7),S(),g(8,"span",8),V(9,"span",9),S(),Wt(10,Ms,5,0,"span",10),S()()(),g(11,"label",11),rt("click",function(pe){return ue(O),me(pe.stopPropagation())}),vo(12),S()()}if(f&2){let O=bo(2);J("labelPosition",m.labelPosition),G(),Di("mdc-switch--selected",m.checked)("mdc-switch--unselected",!m.checked)("mdc-switch--checked",m.checked)("mdc-switch--disabled",m.disabled)("mat-mdc-slide-toggle-disabled-interactive",m.disabledInteractive),J("tabIndex",m.disabled&&!m.disabledInteractive?-1:m.tabIndex)("disabled",m.disabled&&!m.disabledInteractive),Ze("id",m.buttonId)("name",m.name)("aria-label",m.ariaLabel)("aria-labelledby",m._getAriaLabelledBy())("aria-describedby",m.ariaDescribedby)("aria-required",m.required||null)("aria-checked",m.checked)("aria-disabled",m.disabled&&m.disabledInteractive?"true":null),G(8),J("matRippleTrigger",O)("matRippleDisabled",m.disableRipple||m.disabled)("matRippleCentered",!0),G(),mo(m.hideIcon?-1:10),G(),J("for",m.buttonId),Ze("id",m._labelId)}},dependencies:[Oo,Eo],styles:['.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}'],encapsulation:2,changeDetection:0})}return h})();var Jo=(()=>{class h{static \u0275fac=function(f){return new(f||h)};static \u0275mod=ho({type:h});static \u0275inj=io({imports:[We,Bi,Bi]})}return h})();function Es(h,u){if(h&1){let l=Ae();g(0,"div",12)(1,"mat-slide-toggle",13),rt("change",function(){let m=ue(l).$implicit,O=po();return me(O.toggleLayer.emit(m.id))}),D(2),S()()}if(h&2){let l=u.$implicit;G(),J("checked",l.visible),G(),Lo(" ",l.name," ")}}var je=class h{layers=[];activeBaseLayer="osm";zoomIn=new wt;zoomOut=new wt;resetView=new wt;toggleLayer=new wt;changeBaseLayer=new wt;static \u0275fac=function(l){return new(l||h)};static \u0275cmp=xt({type:h,selectors:[["app-map-controls"]],inputs:{layers:"layers",activeBaseLayer:"activeBaseLayer"},outputs:{zoomIn:"zoomIn",zoomOut:"zoomOut",resetView:"resetView",toggleLayer:"toggleLayer",changeBaseLayer:"changeBaseLayer"},decls:30,vars:5,consts:[[1,"map-controls"],[1,"control-group"],["mat-icon-button","","matTooltip","Zoom In",3,"click"],["mat-icon-button","","matTooltip","Zoom Out",3,"click"],["mat-icon-button","","matTooltip","Reset View",3,"click"],["class","layer-control",4,"ngFor","ngForOf"],[1,"base-layer-control"],[3,"change"],["value","osm",3,"selected"],["value","light",3,"selected"],["value","dark",3,"selected"],["value","satellite",3,"selected"],[1,"layer-control"],[3,"change","checked"]],template:function(l,f){l&1&&(g(0,"div",0)(1,"mat-card")(2,"mat-card-content")(3,"div",1)(4,"button",2),rt("click",function(){return f.zoomIn.emit()}),g(5,"mat-icon"),D(6,"add"),S()(),g(7,"button",3),rt("click",function(){return f.zoomOut.emit()}),g(8,"mat-icon"),D(9,"remove"),S()(),g(10,"button",4),rt("click",function(){return f.resetView.emit()}),g(11,"mat-icon"),D(12,"home"),S()()(),g(13,"div",1)(14,"h3"),D(15,"Layers"),S(),Wt(16,Es,3,2,"div",5),S(),g(17,"div",1)(18,"h3"),D(19,"Base Map"),S(),g(20,"div",6)(21,"select",7),rt("change",function(O){return f.changeBaseLayer.emit(O.target.value)}),g(22,"option",8),D(23,"OpenStreetMap"),S(),g(24,"option",9),D(25,"Light"),S(),g(26,"option",10),D(27,"Dark"),S(),g(28,"option",11),D(29,"Satellite"),S()()()()()()()),l&2&&(G(16),J("ngForOf",f.layers),G(6),J("selected",f.activeBaseLayer==="osm"),G(2),J("selected",f.activeBaseLayer==="light"),G(2),J("selected",f.activeBaseLayer==="dark"),G(2),J("selected",f.activeBaseLayer==="satellite"))},dependencies:[jt,ko,Fe,Do,He,Bo,Ro,No,Jt,Gt,Ut,Jo,We,jo,Vo,Wo],styles:[".map-controls[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;z-index:1000;width:200px}.control-group[_ngcontent-%COMP%]{margin-bottom:15px}.control-group[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-size:14px}.layer-control[_ngcontent-%COMP%], .base-layer-control[_ngcontent-%COMP%]{margin-bottom:5px}select[_ngcontent-%COMP%]{width:100%;padding:5px}"]})};var Ge=class h{static \u0275fac=function(l){return new(l||h)};static \u0275cmp=xt({type:h,selectors:[["app-map-legend"]],decls:41,vars:0,consts:[[1,"map-legend"],[1,"legend-item"],[1,"legend-icon","line-icon"],[1,"legend-label"],[1,"legend-icon","junction-icon"],[1,"legend-icon","endpoint-icon"],[1,"legend-icon","distribution-icon"],[1,"status-section"],[1,"legend-icon","status-active"],[1,"legend-icon","status-planned"],[1,"legend-icon","status-maintenance"],[1,"legend-icon","status-inactive"]],template:function(l,f){l&1&&(g(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),D(4,"Legend"),S()(),g(5,"mat-card-content")(6,"div",1),V(7,"div",2),g(8,"div",3),D(9,"Optical Line"),S()(),g(10,"div",1),V(11,"div",4),g(12,"div",3),D(13,"Junction Point"),S()(),g(14,"div",1),V(15,"div",5),g(16,"div",3),D(17,"Endpoint"),S()(),g(18,"div",1),V(19,"div",6),g(20,"div",3),D(21,"Distribution Point"),S()(),g(22,"div",7)(23,"h4"),D(24,"Status Colors"),S(),g(25,"div",1),V(26,"div",8),g(27,"div",3),D(28,"Active"),S()(),g(29,"div",1),V(30,"div",9),g(31,"div",3),D(32,"Planned"),S()(),g(33,"div",1),V(34,"div",10),g(35,"div",3),D(36,"Maintenance"),S()(),g(37,"div",1),V(38,"div",11),g(39,"div",3),D(40,"Inactive"),S()()()()()())},dependencies:[jt,Jt,Gt,Ut,Re,Ne],styles:[".map-legend[_ngcontent-%COMP%]{position:absolute;bottom:10px;right:10px;z-index:1000;width:200px}mat-card-title[_ngcontent-%COMP%]{font-size:16px}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:8px}.legend-icon[_ngcontent-%COMP%]{width:20px;height:20px;margin-right:10px;border-radius:3px}.line-icon[_ngcontent-%COMP%]{height:3px;background-color:#38f}.junction-icon[_ngcontent-%COMP%]{background-color:#ff7800;border-radius:50%}.endpoint-icon[_ngcontent-%COMP%]{background-color:#32cd32;border-radius:50%}.distribution-icon[_ngcontent-%COMP%]{background-color:#9370db;border-radius:50%}.status-section[_ngcontent-%COMP%]{margin-top:15px}.status-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;font-size:14px}.status-active[_ngcontent-%COMP%]{background-color:#4caf50}.status-planned[_ngcontent-%COMP%]{background-color:#2196f3}.status-maintenance[_ngcontent-%COMP%]{background-color:#ff9800}.status-inactive[_ngcontent-%COMP%]{background-color:#f44336}"]})};var q=to(Zi());var Je=class h{map;linesLayer=q.layerGroup();pointsLayer=q.layerGroup();mapReadySubject=new de(!1);mapReady$=this.mapReadySubject.asObservable();constructor(){}initMap(u){this.map=q.map(u,{center:[48.7,19.7],zoom:8,layers:[q.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{maxZoom:19,attribution:"\xA9 OpenStreetMap contributors"})]}),this.linesLayer.addTo(this.map),this.pointsLayer.addTo(this.map),this.mapReadySubject.next(!0)}displayLines(u){this.map&&(this.linesLayer.clearLayers(),u.forEach(l=>{if(l.geometry){let f=q.geoJSON(l.geometry,{style:()=>({color:this.getProviderColor(l.providerId),weight:3,opacity:.7}),onEachFeature:(m,O)=>{O.bindPopup(this.createLinePopup(l))}});this.linesLayer.addLayer(f)}}))}displayPoints(u){this.map&&(this.pointsLayer.clearLayers(),u.forEach(l=>{if(l.location){let f=q.marker([l.location.coordinates[1],l.location.coordinates[0]],{icon:this.getPointIcon(l.type)});f.bindPopup(this.createPointPopup(l)),this.pointsLayer.addLayer(f)}}))}fitBounds(){if(!this.map)return;let u=q.featureGroup([this.linesLayer,this.pointsLayer]).getBounds();u.isValid()&&this.map.fitBounds(u,{padding:[50,50]})}getProviderColor(u){let l=Array.from(u).reduce((m,O)=>O.charCodeAt(0)+((m<<5)-m),0);return`hsl(${Math.abs(l)%360}, 70%, 50%)`}getPointIcon(u){let l="data:image/svg+xml;base64,"+btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#ff7800" stroke="#000000" stroke-width="1"/><circle cx="12" cy="12" r="4" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>'),f="data:image/svg+xml;base64,"+btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#32CD32" stroke="#000000" stroke-width="1"/><rect x="7" y="7" width="10" height="10" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>'),m="data:image/svg+xml;base64,"+btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#9370DB" stroke="#000000" stroke-width="1"/><polygon points="12,6 18,12 12,18 6,12" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');return q.icon({iconUrl:u==="junction"?l:u==="endpoint"?f:m,iconSize:[24,24],iconAnchor:[12,12],popupAnchor:[0,-12]})}createLinePopup(u){return`
      <div class="popup-content">
        <h3>${u.name}</h3>
        <p><strong>Provider:</strong> ${u.providerName}</p>
        <p><strong>Capacity:</strong> ${u.usedCapacity}/${u.capacity}</p>
        <p><strong>Length:</strong> ${u.length} km</p>
        <p><strong>Status:</strong> ${u.status}</p>
        <a href="/lines/${u.id}" target="_blank">View Details</a>
      </div>
    `}createPointPopup(u){return`
      <div class="popup-content">
        <h3>${u.name}</h3>
        <p><strong>Provider:</strong> ${u.providerName}</p>
        <p><strong>Type:</strong> ${u.type}</p>
        <p><strong>Address:</strong> ${u.address}</p>
        <p><strong>Status:</strong> ${u.status}</p>
        <a href="/points/${u.id}" target="_blank">View Details</a>
      </div>
    `}static \u0275fac=function(l){return new(l||h)};static \u0275prov=Ht({token:h,factory:h.\u0275fac,providedIn:"root"})};var qe=class h{constructor(){}linesToGeoJson(u){return{type:"FeatureCollection",features:u.map(f=>({type:"Feature",geometry:f.geometry,properties:{id:f.id,name:f.name,providerId:f.providerId,providerName:f.providerName,capacity:f.capacity,usedCapacity:f.usedCapacity,status:f.status}}))}}pointsToGeoJson(u){return{type:"FeatureCollection",features:u.map(f=>({type:"Feature",geometry:f.location,properties:{id:f.id,name:f.name,providerId:f.providerId,providerName:f.providerName,type:f.type,capacity:f.capacity,status:f.status}}))}}createLineString(u,l,f,m){return{type:"LineString",coordinates:[[u,l],[f,m]]}}createPoint(u,l){return{type:"Point",coordinates:[u,l]}}static \u0275fac=function(l){return new(l||h)};static \u0275prov=Ht({token:h,factory:h.\u0275fac,providedIn:"root"})};var fe=to(Zi());var Ke=class h{layers=new Map;baseLayers=new Map;activeBaseLayer="osm";layersSubject=new de([]);layers$=this.layersSubject.asObservable();baseLayersSubject=new de(this.baseLayers);baseLayers$=this.baseLayersSubject.asObservable();constructor(){this.initializeBaseLayers()}initializeBaseLayers(){this.baseLayers.set("osm",fe.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{maxZoom:19,attribution:"\xA9 OpenStreetMap contributors"})),this.baseLayers.set("light",fe.tileLayer("https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",{maxZoom:19,attribution:"\xA9 OpenStreetMap contributors, \xA9 CARTO"})),this.baseLayers.set("dark",fe.tileLayer("https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",{maxZoom:19,attribution:"\xA9 OpenStreetMap contributors, \xA9 CARTO"})),this.baseLayers.set("satellite",fe.tileLayer("https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",{maxZoom:19,attribution:"Tiles \xA9 Esri"})),this.baseLayersSubject.next(this.baseLayers)}getActiveBaseLayer(){return this.activeBaseLayer}setActiveBaseLayer(u){this.baseLayers.has(u)&&(this.activeBaseLayer=u)}addLayer(u,l,f,m=!0){this.layers.set(u,{id:u,name:l,layer:f,visible:m}),this.notifyLayersChanged()}removeLayer(u){this.layers.delete(u),this.notifyLayersChanged()}getLayer(u){return this.layers.get(u)}getLayers(){return Array.from(this.layers.values())}setLayerVisibility(u,l){let f=this.layers.get(u);f&&(f.visible=l,this.notifyLayersChanged())}toggleLayerVisibility(u){let l=this.layers.get(u);l&&(l.visible=!l.visible,this.notifyLayersChanged())}notifyLayersChanged(){this.layersSubject.next(Array.from(this.layers.values()))}static \u0275fac=function(l){return new(l||h)};static \u0275prov=Ht({token:h,factory:h.\u0275fac,providedIn:"root"})};function Bs(h,u){h&1&&(g(0,"div",5),V(1,"mat-spinner",6),S())}var Yo=class h{constructor(u,l,f,m,O){this.mapService=u;this.layerService=l;this.geoJsonService=f;this.lineService=m;this.pointService=O}loading=!0;layers=[];activeBaseLayer="osm";subscriptions=[];lines=[];points=[];ngOnInit(){this.subscriptions.push(this.layerService.layers$.subscribe(u=>{this.layers=u})),this.loadData()}ngAfterViewInit(){setTimeout(()=>{this.mapService.initMap("map")})}ngOnDestroy(){this.subscriptions.forEach(u=>u.unsubscribe())}loadData(){this.loading=!0,this.lines=this.getMockLines(),this.points=this.getMockPoints(),this.displayData(),this.loading=!1}displayData(){this.subscriptions.push(this.mapService.mapReady$.subscribe(u=>{u&&(this.mapService.displayLines(this.lines),this.mapService.displayPoints(this.points),this.mapService.fitBounds())}))}zoomIn(){let u=this.mapService.map;u&&u.zoomIn()}zoomOut(){let u=this.mapService.map;u&&u.zoomOut()}resetView(){this.mapService.fitBounds()}toggleLayer(u){this.layerService.toggleLayerVisibility(u)}changeBaseLayer(u){this.activeBaseLayer=u,this.layerService.setActiveBaseLayer(u)}getMockLines(){return[{id:"1",name:"Bratislava-Trnava",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point1",endPointId:"point2",capacity:1e3,usedCapacity:750,length:47.5,status:"active",installationDate:new Date("2020-01-15"),lastModified:new Date("2023-05-20"),geometry:this.geoJsonService.createLineString(17.107,48.148,17.583,48.377),properties:{}},{id:"2",name:"Trnava-Nitra",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point2",endPointId:"point3",capacity:1e3,usedCapacity:720,length:45.2,status:"active",installationDate:new Date("2020-02-10"),lastModified:new Date("2023-06-15"),geometry:this.geoJsonService.createLineString(17.583,48.377,18.084,48.309),properties:{}},{id:"3",name:"Nitra-Zvolen",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point3",endPointId:"point4",capacity:800,usedCapacity:630,length:88.7,status:"active",installationDate:new Date("2020-03-01"),lastModified:new Date("2023-07-05"),geometry:this.geoJsonService.createLineString(18.084,48.309,19.123,48.576),properties:{}},{id:"4",name:"Zvolen-Bansk\xE1 Bystrica",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point4",endPointId:"point5",capacity:800,usedCapacity:650,length:21.1,status:"active",installationDate:new Date("2020-03-20"),lastModified:new Date("2023-08-10"),geometry:this.geoJsonService.createLineString(19.123,48.576,19.153,48.734),properties:{}},{id:"5",name:"Bansk\xE1 Bystrica-Poprad",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point5",endPointId:"point6",capacity:800,usedCapacity:600,length:112.8,status:"active",installationDate:new Date("2020-04-05"),lastModified:new Date("2023-09-15"),geometry:this.geoJsonService.createLineString(19.153,48.734,20.297,49.056),properties:{}},{id:"6",name:"Poprad-Pre\u0161ov",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point6",endPointId:"point7",capacity:800,usedCapacity:620,length:75.3,status:"active",installationDate:new Date("2020-05-10"),lastModified:new Date("2023-10-05"),geometry:this.geoJsonService.createLineString(20.297,49.056,21.237,49),properties:{}},{id:"7",name:"Pre\u0161ov-Ko\u0161ice",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point7",endPointId:"point8",capacity:1e3,usedCapacity:820,length:36.5,status:"active",installationDate:new Date("2020-05-15"),lastModified:new Date("2023-10-10"),geometry:this.geoJsonService.createLineString(21.237,49,21.259,48.716),properties:{}},{id:"8",name:"Bratislava-Nitra",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point1",endPointId:"point3",capacity:800,usedCapacity:550,length:87.3,status:"active",installationDate:new Date("2020-06-10"),lastModified:new Date("2023-11-05"),geometry:this.geoJsonService.createLineString(17.107,48.148,18.084,48.309),properties:{}},{id:"9",name:"Bratislava-Tren\u010D\xEDn",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point1",endPointId:"point9",capacity:800,usedCapacity:520,length:120.5,status:"active",installationDate:new Date("2020-07-15"),lastModified:new Date("2023-11-20"),geometry:this.geoJsonService.createLineString(17.107,48.148,18.044,48.894),properties:{}},{id:"10",name:"Tren\u010D\xEDn-\u017Dilina",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point9",endPointId:"point10",capacity:800,usedCapacity:480,length:74.8,status:"active",installationDate:new Date("2020-08-01"),lastModified:new Date("2023-12-05"),geometry:this.geoJsonService.createLineString(18.044,48.894,18.739,49.223),properties:{}},{id:"11",name:"\u017Dilina-Poprad",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point10",endPointId:"point6",capacity:800,usedCapacity:450,length:145.2,status:"active",installationDate:new Date("2020-08-15"),lastModified:new Date("2023-12-10"),geometry:this.geoJsonService.createLineString(18.739,49.223,20.297,49.056),properties:{}},{id:"12",name:"\u017Dilina-Bansk\xE1 Bystrica",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point10",endPointId:"point5",capacity:600,usedCapacity:380,length:90.3,status:"active",installationDate:new Date("2020-09-10"),lastModified:new Date("2024-01-05"),geometry:this.geoJsonService.createLineString(18.739,49.223,19.153,48.734),properties:{}},{id:"13",name:"Ko\u0161ice-Michalovce",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point8",endPointId:"point11",capacity:500,usedCapacity:320,length:68.7,status:"active",installationDate:new Date("2021-01-15"),lastModified:new Date("2024-01-20"),geometry:this.geoJsonService.createLineString(21.259,48.716,21.919,48.754),properties:{}},{id:"14",name:"Pre\u0161ov-Bardejov",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point7",endPointId:"point12",capacity:400,usedCapacity:240,length:41.2,status:"active",installationDate:new Date("2021-02-10"),lastModified:new Date("2024-02-05"),geometry:this.geoJsonService.createLineString(21.237,49,21.277,49.292),properties:{}},{id:"15",name:"Poprad-Spi\u0161sk\xE1 Nov\xE1 Ves",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point6",endPointId:"point13",capacity:400,usedCapacity:260,length:26.8,status:"active",installationDate:new Date("2021-03-05"),lastModified:new Date("2024-02-15"),geometry:this.geoJsonService.createLineString(20.297,49.056,20.561,48.944),properties:{}},{id:"16",name:"Spi\u0161sk\xE1 Nov\xE1 Ves-Ko\u0161ice",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point13",endPointId:"point8",capacity:400,usedCapacity:280,length:75.3,status:"active",installationDate:new Date("2021-03-20"),lastModified:new Date("2024-02-25"),geometry:this.geoJsonService.createLineString(20.561,48.944,21.259,48.716),properties:{}},{id:"17",name:"Bansk\xE1 Bystrica-Lu\u010Denec",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point5",endPointId:"point14",capacity:400,usedCapacity:220,length:65.7,status:"active",installationDate:new Date("2021-04-10"),lastModified:new Date("2024-03-05"),geometry:this.geoJsonService.createLineString(19.153,48.734,19.671,48.332),properties:{}},{id:"18",name:"Zvolen-Levice",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point4",endPointId:"point15",capacity:400,usedCapacity:210,length:70.2,status:"active",installationDate:new Date("2021-05-05"),lastModified:new Date("2024-03-15"),geometry:this.geoJsonService.createLineString(19.123,48.576,18.607,48.216),properties:{}},{id:"19",name:"Levice-Nitra",providerId:"provider3",providerName:"UPC Slovakia",startPointId:"point15",endPointId:"point3",capacity:400,usedCapacity:230,length:48.5,status:"active",installationDate:new Date("2021-05-20"),lastModified:new Date("2024-03-25"),geometry:this.geoJsonService.createLineString(18.607,48.216,18.084,48.309),properties:{}},{id:"20",name:"Bratislava-Dunajsk\xE1 Streda",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point1",endPointId:"point16",capacity:600,usedCapacity:0,length:47.8,status:"planned",installationDate:new Date("2024-06-01"),lastModified:new Date("2024-01-15"),geometry:this.geoJsonService.createLineString(17.107,48.148,17.612,47.994),properties:{}},{id:"21",name:"Dunajsk\xE1 Streda-Kom\xE1rno",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point16",endPointId:"point17",capacity:600,usedCapacity:0,length:55.3,status:"planned",installationDate:new Date("2024-07-01"),lastModified:new Date("2024-01-20"),geometry:this.geoJsonService.createLineString(17.612,47.994,18.123,47.763),properties:{}},{id:"22",name:"Kom\xE1rno-Nov\xE9 Z\xE1mky",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point17",endPointId:"point18",capacity:600,usedCapacity:0,length:32.7,status:"planned",installationDate:new Date("2024-08-01"),lastModified:new Date("2024-01-25"),geometry:this.geoJsonService.createLineString(18.123,47.763,18.161,47.985),properties:{}},{id:"23",name:"Nov\xE9 Z\xE1mky-Levice",providerId:"provider1",providerName:"Slovak Telekom",startPointId:"point18",endPointId:"point15",capacity:600,usedCapacity:0,length:50.5,status:"planned",installationDate:new Date("2024-09-01"),lastModified:new Date("2024-01-30"),geometry:this.geoJsonService.createLineString(18.161,47.985,18.607,48.216),properties:{}},{id:"24",name:"Tren\u010D\xEDn-Prievidza",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point9",endPointId:"point19",capacity:500,usedCapacity:350,length:53.2,status:"maintenance",installationDate:new Date("2021-06-10"),lastModified:new Date("2024-04-05"),geometry:this.geoJsonService.createLineString(18.044,48.894,18.603,48.773),properties:{}},{id:"25",name:"Prievidza-\u017Diar nad Hronom",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point19",endPointId:"point20",capacity:500,usedCapacity:320,length:45.8,status:"maintenance",installationDate:new Date("2021-06-25"),lastModified:new Date("2024-04-10"),geometry:this.geoJsonService.createLineString(18.603,48.773,18.851,48.591),properties:{}},{id:"26",name:"\u017Diar nad Hronom-Zvolen",providerId:"provider2",providerName:"Orange Slovakia",startPointId:"point20",endPointId:"point4",capacity:500,usedCapacity:330,length:30.5,status:"maintenance",installationDate:new Date("2021-07-10"),lastModified:new Date("2024-04-15"),geometry:this.geoJsonService.createLineString(18.851,48.591,19.123,48.576),properties:{}}]}getMockPoints(){return[{id:"point1",name:"Bratislava",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:2e3,address:"N\xE1mestie SNP 1, 811 01 Bratislava",status:"active",installationDate:new Date("2020-01-05"),lastModified:new Date("2023-05-15"),location:this.geoJsonService.createPoint(17.107,48.148),connectedLines:["1","8","9","20"],properties:{}},{id:"point2",name:"Trnava",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:1e3,address:"Hlavn\xE1 1, 917 01 Trnava",status:"active",installationDate:new Date("2020-01-10"),lastModified:new Date("2023-05-18"),location:this.geoJsonService.createPoint(17.583,48.377),connectedLines:["1","2"],properties:{}},{id:"point3",name:"Nitra",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:1200,address:"\u0160tef\xE1nikova trieda 1, 949 01 Nitra",status:"active",installationDate:new Date("2020-02-05"),lastModified:new Date("2023-06-10"),location:this.geoJsonService.createPoint(18.084,48.309),connectedLines:["2","3","8","19"],properties:{}},{id:"point4",name:"Zvolen",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:900,address:"N\xE1mestie SNP 1, 960 01 Zvolen",status:"active",installationDate:new Date("2020-03-01"),lastModified:new Date("2023-06-12"),location:this.geoJsonService.createPoint(19.123,48.576),connectedLines:["3","4","18","26"],properties:{}},{id:"point5",name:"Bansk\xE1 Bystrica",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:1100,address:"N\xE1mestie SNP 1, 974 01 Bansk\xE1 Bystrica",status:"active",installationDate:new Date("2020-03-15"),lastModified:new Date("2023-07-01"),location:this.geoJsonService.createPoint(19.153,48.734),connectedLines:["4","5","12","17"],properties:{}},{id:"point6",name:"Poprad",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:800,address:"N\xE1mestie sv. Eg\xEDdia 1, 058 01 Poprad",status:"active",installationDate:new Date("2020-04-01"),lastModified:new Date("2023-07-03"),location:this.geoJsonService.createPoint(20.297,49.056),connectedLines:["5","6","11","15"],properties:{}},{id:"point7",name:"Pre\u0161ov",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:900,address:"Hlavn\xE1 1, 080 01 Pre\u0161ov",status:"active",installationDate:new Date("2020-05-01"),lastModified:new Date("2023-08-05"),location:this.geoJsonService.createPoint(21.237,49),connectedLines:["6","7","14"],properties:{}},{id:"point8",name:"Ko\u0161ice",providerId:"provider1",providerName:"Slovak Telekom",type:"junction",capacity:1500,address:"Hlavn\xE1 1, 040 01 Ko\u0161ice",status:"active",installationDate:new Date("2020-05-10"),lastModified:new Date("2023-08-08"),location:this.geoJsonService.createPoint(21.259,48.716),connectedLines:["7","13","16"],properties:{}},{id:"point9",name:"Tren\u010D\xEDn",providerId:"provider2",providerName:"Orange Slovakia",type:"junction",capacity:800,address:"Mierov\xE9 n\xE1mestie 1, 911 01 Tren\u010D\xEDn",status:"active",installationDate:new Date("2020-07-01"),lastModified:new Date("2023-09-10"),location:this.geoJsonService.createPoint(18.044,48.894),connectedLines:["9","10","24"],properties:{}},{id:"point10",name:"\u017Dilina",providerId:"provider2",providerName:"Orange Slovakia",type:"junction",capacity:1e3,address:"Mari\xE1nske n\xE1mestie 1, 010 01 \u017Dilina",status:"active",installationDate:new Date("2020-07-15"),lastModified:new Date("2023-09-12"),location:this.geoJsonService.createPoint(18.739,49.223),connectedLines:["10","11","12"],properties:{}},{id:"point11",name:"Michalovce",providerId:"provider3",providerName:"UPC Slovakia",type:"endpoint",capacity:600,address:"N\xE1mestie oslobodite\u013Eov 1, 071 01 Michalovce",status:"active",installationDate:new Date("2021-01-10"),lastModified:new Date("2024-01-01"),location:this.geoJsonService.createPoint(21.919,48.754),connectedLines:["13"],properties:{}},{id:"point12",name:"Bardejov",providerId:"provider3",providerName:"UPC Slovakia",type:"endpoint",capacity:500,address:"Radni\u010Dn\xE9 n\xE1mestie 1, 085 01 Bardejov",status:"active",installationDate:new Date("2021-02-05"),lastModified:new Date("2024-01-05"),location:this.geoJsonService.createPoint(21.277,49.292),connectedLines:["14"],properties:{}},{id:"point13",name:"Spi\u0161sk\xE1 Nov\xE1 Ves",providerId:"provider3",providerName:"UPC Slovakia",type:"distribution",capacity:600,address:"Radni\u010Dn\xE9 n\xE1mestie 1, 052 01 Spi\u0161sk\xE1 Nov\xE1 Ves",status:"active",installationDate:new Date("2021-03-01"),lastModified:new Date("2024-01-10"),location:this.geoJsonService.createPoint(20.561,48.944),connectedLines:["15","16"],properties:{}},{id:"point14",name:"Lu\u010Denec",providerId:"provider3",providerName:"UPC Slovakia",type:"endpoint",capacity:500,address:"N\xE1mestie republiky 1, 984 01 Lu\u010Denec",status:"active",installationDate:new Date("2021-04-05"),lastModified:new Date("2024-01-15"),location:this.geoJsonService.createPoint(19.671,48.332),connectedLines:["17"],properties:{}},{id:"point15",name:"Levice",providerId:"provider3",providerName:"UPC Slovakia",type:"junction",capacity:700,address:"N\xE1mestie hrdinov 1, 934 01 Levice",status:"active",installationDate:new Date("2021-05-01"),lastModified:new Date("2024-01-20"),location:this.geoJsonService.createPoint(18.607,48.216),connectedLines:["18","19","23"],properties:{}},{id:"point16",name:"Dunajsk\xE1 Streda",providerId:"provider1",providerName:"Slovak Telekom",type:"distribution",capacity:600,address:"Hlavn\xE1 1, 929 01 Dunajsk\xE1 Streda",status:"planned",installationDate:new Date("2024-06-01"),lastModified:new Date("2024-01-15"),location:this.geoJsonService.createPoint(17.612,47.994),connectedLines:["20","21"],properties:{}},{id:"point17",name:"Kom\xE1rno",providerId:"provider1",providerName:"Slovak Telekom",type:"distribution",capacity:600,address:"N\xE1mestie gener\xE1la Klapku 1, 945 01 Kom\xE1rno",status:"planned",installationDate:new Date("2024-07-01"),lastModified:new Date("2024-01-20"),location:this.geoJsonService.createPoint(18.123,47.763),connectedLines:["21","22"],properties:{}},{id:"point18",name:"Nov\xE9 Z\xE1mky",providerId:"provider1",providerName:"Slovak Telekom",type:"distribution",capacity:600,address:"Hlavn\xE9 n\xE1mestie 1, 940 01 Nov\xE9 Z\xE1mky",status:"planned",installationDate:new Date("2024-08-01"),lastModified:new Date("2024-01-25"),location:this.geoJsonService.createPoint(18.161,47.985),connectedLines:["22","23"],properties:{}},{id:"point19",name:"Prievidza",providerId:"provider2",providerName:"Orange Slovakia",type:"junction",capacity:600,address:"N\xE1mestie slobody 1, 971 01 Prievidza",status:"maintenance",installationDate:new Date("2021-06-05"),lastModified:new Date("2024-04-01"),location:this.geoJsonService.createPoint(18.603,48.773),connectedLines:["24","25"],properties:{}},{id:"point20",name:"\u017Diar nad Hronom",providerId:"provider2",providerName:"Orange Slovakia",type:"junction",capacity:500,address:"N\xE1mestie Matice slovenskej 1, 965 01 \u017Diar nad Hronom",status:"maintenance",installationDate:new Date("2021-06-20"),lastModified:new Date("2024-04-05"),location:this.geoJsonService.createPoint(18.851,48.591),connectedLines:["25","26"],properties:{}}]}static \u0275fac=function(l){return new(l||h)(Vt(Je),Vt(Ke),Vt(qe),Vt(Go),Vt(Uo))};static \u0275cmp=xt({type:h,selectors:[["app-map-view"]],decls:11,vars:3,consts:[[1,"map-container"],[1,"map-wrapper"],["id","map",1,"map"],["class","loading-overlay",4,"ngIf"],[3,"zoomIn","zoomOut","resetView","toggleLayer","changeBaseLayer","layers","activeBaseLayer"],[1,"loading-overlay"],["diameter","50"]],template:function(l,f){l&1&&(g(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),D(4,"Optical Network Map"),S()(),g(5,"mat-card-content")(6,"div",1),V(7,"div",2),Wt(8,Bs,2,0,"div",3),g(9,"app-map-controls",4),rt("zoomIn",function(){return f.zoomIn()})("zoomOut",function(){return f.zoomOut()})("resetView",function(){return f.resetView()})("toggleLayer",function(O){return f.toggleLayer(O)})("changeBaseLayer",function(O){return f.changeBaseLayer(O)}),S(),V(10,"app-map-legend"),S()()()()),l&2&&(G(8),J("ngIf",f.loading),G(),J("layers",f.layers)("activeBaseLayer",f.activeBaseLayer))},dependencies:[jt,To,Jt,Gt,Ut,Re,Ne,Fe,He,Ao,Zo,je,Ge],styles:[".map-container[_ngcontent-%COMP%]{padding:20px}.map-wrapper[_ngcontent-%COMP%]{position:relative;height:600px;border:1px solid #ddd}.map[_ngcontent-%COMP%]{height:100%;width:100%}.loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background-color:#ffffffb3;display:flex;justify-content:center;align-items:center;z-index:1001}"]})};export{Yo as MapViewComponent};
