import{a as K,b as Q,c as W,d as X,e as Y}from"./chunk-5KM6POKN.js";import{a as q,b as G,c as H,d as J}from"./chunk-UPFDAAUE.js";import{b as U}from"./chunk-OJZDOK3T.js";import{a as $}from"./chunk-ESTBHZNU.js";import"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import{a as B,c as j,d as z,e as F}from"./chunk-QB7XPJNY.js";import{i as S,k as L,l as w,n as O}from"./chunk-KGIFXD27.js";import{$ as T,_ as k,aa as N,ba as V,da as R,ea as A}from"./chunk-HE4KASLF.js";import{Bb as t,Cb as e,Db as g,Dc as y,Fc as P,Hb as b,Ic as h,Jb as f,Jc as E,Kb as m,Ub as i,Va as a,Vb as d,Wb as x,_a as s,ac as I,cc as _,dc as M,fb as D,ha as v,ia as u,lb as C,sb as p}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var Z=o=>["/points",o];function te(o,r){if(o&1&&(t(0,"mat-card-subtitle"),i(1),e()),o&2){let n=m();a(),d(n.line.name)}}function ie(o,r){if(o&1&&g(0,"app-alert",5),o&2){let n=m();p("message",n.error)}}function ne(o,r){o&1&&(t(0,"div",6),g(1,"app-loading-spinner"),e())}function ae(o,r){if(o&1){let n=b();t(0,"div",7)(1,"div",8)(2,"button",9),f("click",function(){v(n);let c=m();return u(c.onEdit())}),t(3,"mat-icon"),i(4,"edit"),e(),i(5," Edit "),e(),t(6,"button",10),f("click",function(){v(n);let c=m();return u(c.onDelete())}),t(7,"mat-icon"),i(8,"delete"),e(),i(9," Delete "),e(),t(10,"button",11),f("click",function(){v(n);let c=m();return u(c.onViewMap())}),t(11,"mat-icon"),i(12,"map"),e(),i(13," View on Map "),e()(),g(14,"mat-divider",12),t(15,"mat-tab-group")(16,"mat-tab",13)(17,"div",14)(18,"div",15)(19,"div",16)(20,"div",17),i(21,"ID"),e(),t(22,"div",18),i(23),e()(),t(24,"div",16)(25,"div",17),i(26,"Name"),e(),t(27,"div",18),i(28),e()(),t(29,"div",16)(30,"div",17),i(31,"Provider"),e(),t(32,"div",18),i(33),e()(),t(34,"div",16)(35,"div",17),i(36,"Status"),e(),t(37,"div",18)(38,"span",19),i(39),e()()(),t(40,"div",16)(41,"div",17),i(42,"Capacity"),e(),t(43,"div",18),i(44),e()(),t(45,"div",16)(46,"div",17),i(47,"Used Capacity"),e(),t(48,"div",18),i(49),e()(),t(50,"div",16)(51,"div",17),i(52,"Length (km)"),e(),t(53,"div",18),i(54),e()(),t(55,"div",16)(56,"div",17),i(57,"Installation Date"),e(),t(58,"div",18),i(59),_(60,"date"),e()(),t(61,"div",16)(62,"div",17),i(63,"Last Modified"),e(),t(64,"div",18),i(65),_(66,"date"),e()()()()(),t(67,"mat-tab",20)(68,"div",14)(69,"div",21)(70,"div",22)(71,"h3"),i(72,"Start Point"),e(),t(73,"p")(74,"strong"),i(75,"ID:"),e(),i(76),e(),t(77,"button",23),i(78," View Details "),e()(),t(79,"div",22)(80,"h3"),i(81,"End Point"),e(),t(82,"p")(83,"strong"),i(84,"ID:"),e(),i(85),e(),t(86,"button",23),i(87," View Details "),e()()()()()()()}if(o&2){let n=m();a(23),d(n.line.id),a(5),d(n.line.name),a(5),d(n.line.providerName),a(5),p("ngClass","status-"+n.line.status),a(),x(" ",n.line.status," "),a(5),d(n.line.capacity),a(5),d(n.line.usedCapacity),a(5),d(n.line.length),a(5),d(M(60,14,n.line.installationDate)),a(6),d(M(66,16,n.line.lastModified)),a(11),x(" ",n.line.startPointId,""),a(),p("routerLink",I(18,Z,n.line.startPointId)),a(8),x(" ",n.line.endPointId,""),a(),p("routerLink",I(20,Z,n.line.endPointId))}}var ee=class o{constructor(r,n,l,c){this.route=r;this.router=n;this.lineService=l;this.dialog=c}lineId="";line;loading=!0;error="";ngOnInit(){this.route.params.subscribe(r=>{this.lineId=r.id,this.loadLine()})}loadLine(){this.loading=!0,this.error="",setTimeout(()=>{this.line=this.getMockLine(this.lineId),this.loading=!1,this.line||(this.error=`Line with ID ${this.lineId} not found.`)},1e3)}onEdit(){console.log("Edit line:",this.line)}onDelete(){if(!this.line)return;this.dialog.open(J,{width:"350px",data:{title:"Confirm Delete",message:`Are you sure you want to delete the line "${this.line.name}"?`,confirmText:"Delete",cancelText:"Cancel"}}).afterClosed().subscribe(n=>{n&&(console.log("Delete line:",this.line),this.router.navigate(["/lines"]))})}onViewMap(){this.router.navigate(["/map"],{queryParams:{highlight:this.lineId}})}getMockLine(r){return[{id:"1",name:"Line 1",providerId:"provider1",providerName:"Provider A",startPointId:"point1",endPointId:"point2",capacity:100,usedCapacity:75,length:25.5,status:"active",installationDate:new Date("2022-01-15"),lastModified:new Date("2023-05-20"),geometry:null,properties:{}},{id:"2",name:"Line 2",providerId:"provider2",providerName:"Provider B",startPointId:"point3",endPointId:"point4",capacity:200,usedCapacity:120,length:15.2,status:"active",installationDate:new Date("2021-11-10"),lastModified:new Date("2023-06-15"),geometry:null,properties:{}},{id:"3",name:"Line 3",providerId:"provider1",providerName:"Provider A",startPointId:"point5",endPointId:"point6",capacity:150,usedCapacity:30,length:18.7,status:"planned",installationDate:new Date("2023-12-01"),lastModified:new Date("2023-07-05"),geometry:null,properties:{}},{id:"4",name:"Line 4",providerId:"provider3",providerName:"Provider C",startPointId:"point7",endPointId:"point8",capacity:300,usedCapacity:150,length:32.1,status:"maintenance",installationDate:new Date("2022-03-20"),lastModified:new Date("2023-08-10"),geometry:null,properties:{}},{id:"5",name:"Line 5",providerId:"provider2",providerName:"Provider B",startPointId:"point9",endPointId:"point10",capacity:250,usedCapacity:0,length:22.8,status:"inactive",installationDate:new Date("2022-05-05"),lastModified:new Date("2023-09-15"),geometry:null,properties:{}},{id:"6",name:"Line 6",providerId:"provider3",providerName:"Provider C",startPointId:"point11",endPointId:"point12",capacity:180,usedCapacity:120,length:15.3,status:"active",installationDate:new Date("2022-06-10"),lastModified:new Date("2023-10-05"),geometry:null,properties:{}}].find(l=>l.id===r)}static \u0275fac=function(n){return new(n||o)(s(S),s(L),s($),s(q))};static \u0275cmp=D({type:o,selectors:[["app-line-detail"]],decls:10,vars:4,consts:[[1,"line-detail-container"],[4,"ngIf"],["type","error",3,"message",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","line-details",4,"ngIf"],["type","error",3,"message"],[1,"loading-container"],[1,"line-details"],[1,"actions-bar"],["mat-raised-button","","color","primary",3,"click"],["mat-raised-button","","color","warn",3,"click"],["mat-raised-button","","color","accent",3,"click"],[1,"divider"],["label","Basic Information"],[1,"tab-content"],[1,"info-grid"],[1,"info-item"],[1,"info-label"],[1,"info-value"],[1,"status-chip",3,"ngClass"],["label","Connection Points"],[1,"connection-points"],[1,"connection-point"],["mat-button","","color","primary",3,"routerLink"]],template:function(n,l){n&1&&(t(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),i(4,"Optical Line Details"),e(),C(5,te,2,1,"mat-card-subtitle",1),e(),t(6,"mat-card-content"),C(7,ie,1,1,"app-alert",2)(8,ne,2,0,"div",3)(9,ae,88,22,"div",4),e()()()),n&2&&(a(5),p("ngIf",l.line),a(2),p("ngIf",l.error),a(),p("ngIf",l.loading),a(),p("ngIf",l.line&&!l.loading))},dependencies:[E,y,P,h,A,k,N,R,V,T,j,B,F,z,W,K,Q,Y,X,O,w,G,H,U],styles:[".line-detail-container[_ngcontent-%COMP%]{padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px;min-height:300px;align-items:center}.actions-bar[_ngcontent-%COMP%]{display:flex;gap:10px;margin-bottom:20px}.divider[_ngcontent-%COMP%]{margin-bottom:20px}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column}.info-label[_ngcontent-%COMP%]{font-weight:500;color:#0000008a;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{font-size:16px}.connection-points[_ngcontent-%COMP%]{display:flex;gap:30px;flex-wrap:wrap}.connection-point[_ngcontent-%COMP%]{flex:1;min-width:250px;padding:15px;border:1px solid #e0e0e0;border-radius:4px}.connection-point[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-top:0;margin-bottom:10px}.status-chip[_ngcontent-%COMP%]{padding:4px 8px;border-radius:16px;font-size:12px;font-weight:500;text-transform:capitalize}.status-active[_ngcontent-%COMP%]{background-color:#e6f4ea;color:#137333}.status-planned[_ngcontent-%COMP%]{background-color:#e8f0fe;color:#1a73e8}.status-maintenance[_ngcontent-%COMP%]{background-color:#fef7e0;color:#b06000}.status-inactive[_ngcontent-%COMP%]{background-color:#fce8e6;color:#c5221f}"]})};export{ee as LineDetailComponent};
