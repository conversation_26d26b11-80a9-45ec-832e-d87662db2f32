import{a as Mi,d as ct,g as Ci}from"./chunk-FMRXKCS7.js";import{j as wi,m as Ti}from"./chunk-3OB45MWO.js";import{a as at,c as lt,d as Si,e as Ei}from"./chunk-QB7XPJNY.js";import{c as Yn,d as Gn,e as Xn,f as Zn,g as Jn,j as ei,k as ti,l as ot,m as ni,p as ut}from"./chunk-KGIFXD27.js";import{$ as gi,C as li,D as ci,F as qt,I as ui,J as mi,L as Te,O as di,Q as hi,R as fi,_ as pi,a as ii,aa as yi,ca as _i,da as bi,ea as vi,j as si,k as ri,m as oi,z as ai}from"./chunk-HE4KASLF.js";import{$ as M,Bb as S,Cb as p,D as kn,Db as Y,Ea as ke,Eb as Fe,Fb as xe,Fc as $n,Hb as it,Ia as xn,Jb as Se,Jc as Wn,Kb as G,Lb as Le,Mb as ce,Nb as Ee,O as Ze,Ob as Vn,P as zt,Pb as ue,Q as Rn,Qb as me,T,Tb as st,Ub as y,V as be,Va as q,W as Pe,Wb as jn,Xa as Ln,Y as De,Ya as Bn,Za as zn,_ as oe,_a as Kn,_b as Un,a as Ye,ab as qn,f as Ge,fb as L,gb as Re,ha as ae,hb as tt,ia as le,ic as Hn,ja as Nn,jc as rt,k as Bt,kb as Kt,l as An,lb as Ne,mc as Be,na as In,ra as Je,rb as Ie,sa as et,sb as V,ub as Oe,va as ve,vb as nt,w as Xe,wa as On,wc as we,x as Pn,xb as Qn,z as Dn,za as Fn}from"./chunk-BS5MTC5G.js";import{a as re,b as Mn,c as Cn}from"./chunk-C6Q5SG76.js";var E=function(s){return s[s.State=0]="State",s[s.Transition=1]="Transition",s[s.Sequence=2]="Sequence",s[s.Group=3]="Group",s[s.Animate=4]="Animate",s[s.Keyframes=5]="Keyframes",s[s.Style=6]="Style",s[s.Trigger=7]="Trigger",s[s.Reference=8]="Reference",s[s.AnimateChild=9]="AnimateChild",s[s.AnimateRef=10]="AnimateRef",s[s.Query=11]="Query",s[s.Stagger=12]="Stagger",s}(E||{}),$="*";function Ai(s,t=null){return{type:E.Sequence,steps:s,options:t}}function Qt(s){return{type:E.Style,styles:s,offset:null}}var J=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,e=0){this.totalTime=t+e}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(n=>n()),e.length=0}},ze=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let e=0,n=0,i=0,r=this.players.length;r==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(o=>{o.onDone(()=>{++e==r&&this._onFinish()}),o.onDestroy(()=>{++n==r&&this._onDestroy()}),o.onStart(()=>{++i==r&&this._onStart()})}),this.totalTime=this.players.reduce((o,a)=>Math.max(o,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let e=t*this.totalTime;this.players.forEach(n=>{let i=n.totalTime?Math.min(1,e/n.totalTime):1;n.setPosition(i)})}getPosition(){let t=this.players.reduce((e,n)=>e===null||n.totalTime>e.totalTime?n:e,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(n=>n()),e.length=0}},mt="!";function Pi(s){return new T(3e3,!1)}function hs(){return new T(3100,!1)}function fs(){return new T(3101,!1)}function ps(s){return new T(3001,!1)}function gs(s){return new T(3003,!1)}function ys(s){return new T(3004,!1)}function _s(s,t){return new T(3005,!1)}function bs(){return new T(3006,!1)}function vs(){return new T(3007,!1)}function Ss(s,t){return new T(3008,!1)}function Es(s){return new T(3002,!1)}function ws(s,t,e,n,i){return new T(3010,!1)}function Ts(){return new T(3011,!1)}function Ms(){return new T(3012,!1)}function Cs(){return new T(3200,!1)}function As(){return new T(3202,!1)}function Ps(){return new T(3013,!1)}function Ds(s){return new T(3014,!1)}function ks(s){return new T(3015,!1)}function Rs(s){return new T(3016,!1)}function Ns(s,t){return new T(3404,!1)}function Is(s){return new T(3502,!1)}function Os(s){return new T(3503,!1)}function Fs(){return new T(3300,!1)}function xs(s){return new T(3504,!1)}function Ls(s){return new T(3301,!1)}function Bs(s,t){return new T(3302,!1)}function zs(s){return new T(3303,!1)}function Ks(s,t){return new T(3400,!1)}function qs(s){return new T(3401,!1)}function Qs(s){return new T(3402,!1)}function Vs(s,t){return new T(3505,!1)}function ee(s){switch(s.length){case 0:return new J;case 1:return s[0];default:return new ze(s)}}function Qi(s,t,e=new Map,n=new Map){let i=[],r=[],o=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),m=c==o,u=m&&a||new Map;l.forEach((v,_)=>{let h=_,b=v;if(_!=="offset")switch(h=s.normalizePropertyName(h,i),b){case mt:b=e.get(_);break;case $:b=n.get(_);break;default:b=s.normalizeStyleValue(_,h,b,i);break}u.set(h,b)}),m||r.push(u),a=u,o=c}),i.length)throw Is(i);return r}function mn(s,t,e,n){switch(t){case"start":s.onStart(()=>n(e&&Vt(e,"start",s)));break;case"done":s.onDone(()=>n(e&&Vt(e,"done",s)));break;case"destroy":s.onDestroy(()=>n(e&&Vt(e,"destroy",s)));break}}function Vt(s,t,e){let n=e.totalTime,i=!!e.disabled,r=dn(s.element,s.triggerName,s.fromState,s.toState,t||s.phaseName,n??s.totalTime,i),o=s._data;return o!=null&&(r._data=o),r}function dn(s,t,e,n,i="",r=0,o){return{element:s,triggerName:t,fromState:e,toState:n,phaseName:i,totalTime:r,disabled:!!o}}function z(s,t,e){let n=s.get(t);return n||s.set(t,n=e),n}function Di(s){let t=s.indexOf(":"),e=s.substring(1,t),n=s.slice(t+1);return[e,n]}var js=typeof document>"u"?null:document.documentElement;function hn(s){let t=s.parentNode||s.host||null;return t===js?null:t}function Us(s){return s.substring(1,6)=="ebkit"}var de=null,ki=!1;function Hs(s){de||(de=$s()||{},ki=de.style?"WebkitAppearance"in de.style:!1);let t=!0;return de.style&&!Us(s)&&(t=s in de.style,!t&&ki&&(t="Webkit"+s.charAt(0).toUpperCase()+s.slice(1)in de.style)),t}function $s(){return typeof document<"u"?document.body:null}function Vi(s,t){for(;t;){if(t===s)return!0;t=hn(t)}return!1}function ji(s,t,e){if(e)return Array.from(s.querySelectorAll(t));let n=s.querySelector(t);return n?[n]:[]}var fn=(()=>{class s{validateStyleProperty(e){return Hs(e)}containsElement(e,n){return Vi(e,n)}getParentElement(e){return hn(e)}query(e,n,i){return ji(e,n,i)}computeStyle(e,n,i){return i||""}animate(e,n,i,r,o,a=[],l){return new J(i,r)}static \u0275fac=function(n){return new(n||s)};static \u0275prov=be({token:s,factory:s.\u0275fac})}return s})(),pe=class{static NOOP=new fn},ge=class{};var Ws=1e3,Ui="{{",Ys="}}",Hi="ng-enter",Yt="ng-leave",dt="ng-trigger",yt=".ng-trigger",Ri="ng-animating",Gt=".ng-animating";function X(s){if(typeof s=="number")return s;let t=s.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:Xt(parseFloat(t[1]),t[2])}function Xt(s,t){switch(t){case"s":return s*Ws;default:return s}}function _t(s,t,e){return s.hasOwnProperty("duration")?s:Gs(s,t,e)}function Gs(s,t,e){let n=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,r=0,o="";if(typeof s=="string"){let a=s.match(n);if(a===null)return t.push(Pi(s)),{duration:0,delay:0,easing:""};i=Xt(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(r=Xt(parseFloat(l),a[4]));let c=a[5];c&&(o=c)}else i=s;if(!e){let a=!1,l=t.length;i<0&&(t.push(hs()),a=!0),r<0&&(t.push(fs()),a=!0),a&&t.splice(l,0,Pi(s))}return{duration:i,delay:r,easing:o}}function Xs(s){return s.length?s[0]instanceof Map?s:s.map(t=>new Map(Object.entries(t))):[]}function W(s,t,e){t.forEach((n,i)=>{let r=pn(i);e&&!e.has(i)&&e.set(i,s.style[r]),s.style[r]=n})}function fe(s,t){t.forEach((e,n)=>{let i=pn(n);s.style[i]=""})}function Ke(s){return Array.isArray(s)?s.length==1?s[0]:Ai(s):s}function Zs(s,t,e){let n=t.params||{},i=$i(s);i.length&&i.forEach(r=>{n.hasOwnProperty(r)||e.push(ps(r))})}var Zt=new RegExp(`${Ui}\\s*(.+?)\\s*${Ys}`,"g");function $i(s){let t=[];if(typeof s=="string"){let e;for(;e=Zt.exec(s);)t.push(e[1]);Zt.lastIndex=0}return t}function Qe(s,t,e){let n=`${s}`,i=n.replace(Zt,(r,o)=>{let a=t[o];return a==null&&(e.push(gs(o)),a=""),a.toString()});return i==n?s:i}var Js=/-+([a-z0-9])/g;function pn(s){return s.replace(Js,(...t)=>t[1].toUpperCase())}function er(s,t){return s===0||t===0}function tr(s,t,e){if(e.size&&t.length){let n=t[0],i=[];if(e.forEach((r,o)=>{n.has(o)||i.push(o),n.set(o,r)}),i.length)for(let r=1;r<t.length;r++){let o=t[r];i.forEach(a=>o.set(a,gn(s,a)))}}return t}function B(s,t,e){switch(t.type){case E.Trigger:return s.visitTrigger(t,e);case E.State:return s.visitState(t,e);case E.Transition:return s.visitTransition(t,e);case E.Sequence:return s.visitSequence(t,e);case E.Group:return s.visitGroup(t,e);case E.Animate:return s.visitAnimate(t,e);case E.Keyframes:return s.visitKeyframes(t,e);case E.Style:return s.visitStyle(t,e);case E.Reference:return s.visitReference(t,e);case E.AnimateChild:return s.visitAnimateChild(t,e);case E.AnimateRef:return s.visitAnimateRef(t,e);case E.Query:return s.visitQuery(t,e);case E.Stagger:return s.visitStagger(t,e);default:throw ys(t.type)}}function gn(s,t){return window.getComputedStyle(s)[t]}var nr=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),bt=class extends ge{normalizePropertyName(t,e){return pn(t)}normalizeStyleValue(t,e,n,i){let r="",o=n.toString().trim();if(nr.has(e)&&n!==0&&n!=="0")if(typeof n=="number")r="px";else{let a=n.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&i.push(_s(t,n))}return o+r}};var vt="*";function ir(s,t){let e=[];return typeof s=="string"?s.split(/\s*,\s*/).forEach(n=>sr(n,e,t)):e.push(s),e}function sr(s,t,e){if(s[0]==":"){let l=rr(s,e);if(typeof l=="function"){t.push(l);return}s=l}let n=s.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(n==null||n.length<4)return e.push(ks(s)),t;let i=n[1],r=n[2],o=n[3];t.push(Ni(i,o));let a=i==vt&&o==vt;r[0]=="<"&&!a&&t.push(Ni(o,i))}function rr(s,t){switch(s){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(e,n)=>parseFloat(n)>parseFloat(e);case":decrement":return(e,n)=>parseFloat(n)<parseFloat(e);default:return t.push(Rs(s)),"* => *"}}var ht=new Set(["true","1"]),ft=new Set(["false","0"]);function Ni(s,t){let e=ht.has(s)||ft.has(s),n=ht.has(t)||ft.has(t);return(i,r)=>{let o=s==vt||s==i,a=t==vt||t==r;return!o&&e&&typeof i=="boolean"&&(o=i?ht.has(s):ft.has(s)),!a&&n&&typeof r=="boolean"&&(a=r?ht.has(t):ft.has(t)),o&&a}}var Wi=":self",or=new RegExp(`s*${Wi}s*,?`,"g");function Yi(s,t,e,n){return new Jt(s).build(t,e,n)}var Ii="",Jt=class{_driver;constructor(t){this._driver=t}build(t,e,n){let i=new en(e);return this._resetContextStyleTimingState(i),B(this,Ke(t),i)}_resetContextStyleTimingState(t){t.currentQuerySelector=Ii,t.collectedStyles=new Map,t.collectedStyles.set(Ii,new Map),t.currentTime=0}visitTrigger(t,e){let n=e.queryCount=0,i=e.depCount=0,r=[],o=[];return t.name.charAt(0)=="@"&&e.errors.push(bs()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(e),a.type==E.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(m=>{l.name=m,r.push(this.visitState(l,e))}),l.name=c}else if(a.type==E.Transition){let l=this.visitTransition(a,e);n+=l.queryCount,i+=l.depCount,o.push(l)}else e.errors.push(vs())}),{type:E.Trigger,name:t.name,states:r,transitions:o,queryCount:n,depCount:i,options:null}}visitState(t,e){let n=this.visitStyle(t.styles,e),i=t.options&&t.options.params||null;if(n.containsDynamicStyles){let r=new Set,o=i||{};n.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{$i(l).forEach(c=>{o.hasOwnProperty(c)||r.add(c)})})}),r.size&&e.errors.push(Ss(t.name,[...r.values()]))}return{type:E.State,name:t.name,style:n,options:i?{params:i}:null}}visitTransition(t,e){e.queryCount=0,e.depCount=0;let n=B(this,Ke(t.animation),e),i=ir(t.expr,e.errors);return{type:E.Transition,matchers:i,animation:n,queryCount:e.queryCount,depCount:e.depCount,options:he(t.options)}}visitSequence(t,e){return{type:E.Sequence,steps:t.steps.map(n=>B(this,n,e)),options:he(t.options)}}visitGroup(t,e){let n=e.currentTime,i=0,r=t.steps.map(o=>{e.currentTime=n;let a=B(this,o,e);return i=Math.max(i,e.currentTime),a});return e.currentTime=i,{type:E.Group,steps:r,options:he(t.options)}}visitAnimate(t,e){let n=ur(t.timings,e.errors);e.currentAnimateTimings=n;let i,r=t.styles?t.styles:Qt({});if(r.type==E.Keyframes)i=this.visitKeyframes(r,e);else{let o=t.styles,a=!1;if(!o){a=!0;let c={};n.easing&&(c.easing=n.easing),o=Qt(c)}e.currentTime+=n.duration+n.delay;let l=this.visitStyle(o,e);l.isEmptyStep=a,i=l}return e.currentAnimateTimings=null,{type:E.Animate,timings:n,style:i,options:null}}visitStyle(t,e){let n=this._makeStyleAst(t,e);return this._validateStyleAst(n,e),n}_makeStyleAst(t,e){let n=[],i=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of i)typeof a=="string"?a===$?n.push(a):e.errors.push(Es(a)):n.push(new Map(Object.entries(a)));let r=!1,o=null;return n.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(o=a.get("easing"),a.delete("easing")),!r)){for(let l of a.values())if(l.toString().indexOf(Ui)>=0){r=!0;break}}}),{type:E.Style,styles:n,easing:o,offset:t.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(t,e){let n=e.currentAnimateTimings,i=e.currentTime,r=e.currentTime;n&&r>0&&(r-=n.duration+n.delay),t.styles.forEach(o=>{typeof o!="string"&&o.forEach((a,l)=>{let c=e.collectedStyles.get(e.currentQuerySelector),m=c.get(l),u=!0;m&&(r!=i&&r>=m.startTime&&i<=m.endTime&&(e.errors.push(ws(l,m.startTime,m.endTime,r,i)),u=!1),r=m.startTime),u&&c.set(l,{startTime:r,endTime:i}),e.options&&Zs(a,e.options,e.errors)})})}visitKeyframes(t,e){let n={type:E.Keyframes,styles:[],options:null};if(!e.currentAnimateTimings)return e.errors.push(Ts()),n;let i=1,r=0,o=[],a=!1,l=!1,c=0,m=t.steps.map(P=>{let D=this._makeStyleAst(P,e),I=D.offset!=null?D.offset:cr(D.styles),R=0;return I!=null&&(r++,R=D.offset=I),l=l||R<0||R>1,a=a||R<c,c=R,o.push(R),D});l&&e.errors.push(Ms()),a&&e.errors.push(Cs());let u=t.steps.length,v=0;r>0&&r<u?e.errors.push(As()):r==0&&(v=i/(u-1));let _=u-1,h=e.currentTime,b=e.currentAnimateTimings,C=b.duration;return m.forEach((P,D)=>{let I=v>0?D==_?1:v*D:o[D],R=I*C;e.currentTime=h+b.delay+R,b.duration=R,this._validateStyleAst(P,e),P.offset=I,n.styles.push(P)}),n}visitReference(t,e){return{type:E.Reference,animation:B(this,Ke(t.animation),e),options:he(t.options)}}visitAnimateChild(t,e){return e.depCount++,{type:E.AnimateChild,options:he(t.options)}}visitAnimateRef(t,e){return{type:E.AnimateRef,animation:this.visitReference(t.animation,e),options:he(t.options)}}visitQuery(t,e){let n=e.currentQuerySelector,i=t.options||{};e.queryCount++,e.currentQuery=t;let[r,o]=ar(t.selector);e.currentQuerySelector=n.length?n+" "+r:r,z(e.collectedStyles,e.currentQuerySelector,new Map);let a=B(this,Ke(t.animation),e);return e.currentQuery=null,e.currentQuerySelector=n,{type:E.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:o,animation:a,originalSelector:t.selector,options:he(t.options)}}visitStagger(t,e){e.currentQuery||e.errors.push(Ps());let n=t.timings==="full"?{duration:0,delay:0,easing:"full"}:_t(t.timings,e.errors,!0);return{type:E.Stagger,animation:B(this,Ke(t.animation),e),timings:n,options:null}}};function ar(s){let t=!!s.split(/\s*,\s*/).find(e=>e==Wi);return t&&(s=s.replace(or,"")),s=s.replace(/@\*/g,yt).replace(/@\w+/g,e=>yt+"-"+e.slice(1)).replace(/:animating/g,Gt),[s,t]}function lr(s){return s?re({},s):null}var en=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(t){this.errors=t}};function cr(s){if(typeof s=="string")return null;let t=null;if(Array.isArray(s))s.forEach(e=>{if(e instanceof Map&&e.has("offset")){let n=e;t=parseFloat(n.get("offset")),n.delete("offset")}});else if(s instanceof Map&&s.has("offset")){let e=s;t=parseFloat(e.get("offset")),e.delete("offset")}return t}function ur(s,t){if(s.hasOwnProperty("duration"))return s;if(typeof s=="number"){let r=_t(s,t).duration;return jt(r,0,"")}let e=s;if(e.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=jt(0,0,"");return r.dynamic=!0,r.strValue=e,r}let i=_t(e,t);return jt(i.duration,i.delay,i.easing)}function he(s){return s?(s=re({},s),s.params&&(s.params=lr(s.params))):s={},s}function jt(s,t,e){return{duration:s,delay:t,easing:e}}function yn(s,t,e,n,i,r,o=null,a=!1){return{type:1,element:s,keyframes:t,preStyleProps:e,postStyleProps:n,duration:i,delay:r,totalTime:i+r,easing:o,subTimeline:a}}var Ve=class{_map=new Map;get(t){return this._map.get(t)||[]}append(t,e){let n=this._map.get(t);n||this._map.set(t,n=[]),n.push(...e)}has(t){return this._map.has(t)}clear(){this._map.clear()}},mr=1,dr=":enter",hr=new RegExp(dr,"g"),fr=":leave",pr=new RegExp(fr,"g");function Gi(s,t,e,n,i,r=new Map,o=new Map,a,l,c=[]){return new tn().buildKeyframes(s,t,e,n,i,r,o,a,l,c)}var tn=class{buildKeyframes(t,e,n,i,r,o,a,l,c,m=[]){c=c||new Ve;let u=new nn(t,e,c,i,r,m,[]);u.options=l;let v=l.delay?X(l.delay):0;u.currentTimeline.delayNextStep(v),u.currentTimeline.setStyles([o],null,u.errors,l),B(this,n,u);let _=u.timelines.filter(h=>h.containsAnimation());if(_.length&&a.size){let h;for(let b=_.length-1;b>=0;b--){let C=_[b];if(C.element===e){h=C;break}}h&&!h.allowOnlyTimelineStyles()&&h.setStyles([a],null,u.errors,l)}return _.length?_.map(h=>h.buildKeyframes()):[yn(e,[],[],[],0,v,"",!1)]}visitTrigger(t,e){}visitState(t,e){}visitTransition(t,e){}visitAnimateChild(t,e){let n=e.subInstructions.get(e.element);if(n){let i=e.createSubContext(t.options),r=e.currentTimeline.currentTime,o=this._visitSubInstructions(n,i,i.options);r!=o&&e.transformIntoNewTimeline(o)}e.previousNode=t}visitAnimateRef(t,e){let n=e.createSubContext(t.options);n.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],e,n),this.visitReference(t.animation,n),e.transformIntoNewTimeline(n.currentTimeline.currentTime),e.previousNode=t}_applyAnimationRefDelays(t,e,n){for(let i of t){let r=i?.delay;if(r){let o=typeof r=="number"?r:X(Qe(r,i?.params??{},e.errors));n.delayNextStep(o)}}}_visitSubInstructions(t,e,n){let r=e.currentTimeline.currentTime,o=n.duration!=null?X(n.duration):null,a=n.delay!=null?X(n.delay):null;return o!==0&&t.forEach(l=>{let c=e.appendInstructionToTimeline(l,o,a);r=Math.max(r,c.duration+c.delay)}),r}visitReference(t,e){e.updateOptions(t.options,!0),B(this,t.animation,e),e.previousNode=t}visitSequence(t,e){let n=e.subContextCount,i=e,r=t.options;if(r&&(r.params||r.delay)&&(i=e.createSubContext(r),i.transformIntoNewTimeline(),r.delay!=null)){i.previousNode.type==E.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=St);let o=X(r.delay);i.delayNextStep(o)}t.steps.length&&(t.steps.forEach(o=>B(this,o,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>n&&i.transformIntoNewTimeline()),e.previousNode=t}visitGroup(t,e){let n=[],i=e.currentTimeline.currentTime,r=t.options&&t.options.delay?X(t.options.delay):0;t.steps.forEach(o=>{let a=e.createSubContext(t.options);r&&a.delayNextStep(r),B(this,o,a),i=Math.max(i,a.currentTimeline.currentTime),n.push(a.currentTimeline)}),n.forEach(o=>e.currentTimeline.mergeTimelineCollectedStyles(o)),e.transformIntoNewTimeline(i),e.previousNode=t}_visitTiming(t,e){if(t.dynamic){let n=t.strValue,i=e.params?Qe(n,e.params,e.errors):n;return _t(i,e.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,e){let n=e.currentAnimateTimings=this._visitTiming(t.timings,e),i=e.currentTimeline;n.delay&&(e.incrementTime(n.delay),i.snapshotCurrentStyles());let r=t.style;r.type==E.Keyframes?this.visitKeyframes(r,e):(e.incrementTime(n.duration),this.visitStyle(r,e),i.applyStylesToKeyframe()),e.currentAnimateTimings=null,e.previousNode=t}visitStyle(t,e){let n=e.currentTimeline,i=e.currentAnimateTimings;!i&&n.hasCurrentStyleProperties()&&n.forwardFrame();let r=i&&i.easing||t.easing;t.isEmptyStep?n.applyEmptyStep(r):n.setStyles(t.styles,r,e.errors,e.options),e.previousNode=t}visitKeyframes(t,e){let n=e.currentAnimateTimings,i=e.currentTimeline.duration,r=n.duration,a=e.createSubContext().currentTimeline;a.easing=n.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*r),a.setStyles(l.styles,l.easing,e.errors,e.options),a.applyStylesToKeyframe()}),e.currentTimeline.mergeTimelineCollectedStyles(a),e.transformIntoNewTimeline(i+r),e.previousNode=t}visitQuery(t,e){let n=e.currentTimeline.currentTime,i=t.options||{},r=i.delay?X(i.delay):0;r&&(e.previousNode.type===E.Style||n==0&&e.currentTimeline.hasCurrentStyleProperties())&&(e.currentTimeline.snapshotCurrentStyles(),e.previousNode=St);let o=n,a=e.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!i.optional,e.errors);e.currentQueryTotal=a.length;let l=null;a.forEach((c,m)=>{e.currentQueryIndex=m;let u=e.createSubContext(t.options,c);r&&u.delayNextStep(r),c===e.element&&(l=u.currentTimeline),B(this,t.animation,u),u.currentTimeline.applyStylesToKeyframe();let v=u.currentTimeline.currentTime;o=Math.max(o,v)}),e.currentQueryIndex=0,e.currentQueryTotal=0,e.transformIntoNewTimeline(o),l&&(e.currentTimeline.mergeTimelineCollectedStyles(l),e.currentTimeline.snapshotCurrentStyles()),e.previousNode=t}visitStagger(t,e){let n=e.parentContext,i=e.currentTimeline,r=t.timings,o=Math.abs(r.duration),a=o*(e.currentQueryTotal-1),l=o*e.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=a-l;break;case"full":l=n.currentStaggerTime;break}let m=e.currentTimeline;l&&m.delayNextStep(l);let u=m.currentTime;B(this,t.animation,e),e.previousNode=t,n.currentStaggerTime=i.currentTime-u+(i.startTime-n.currentTimeline.startTime)}},St={},nn=class s{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=St;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(t,e,n,i,r,o,a,l){this._driver=t,this.element=e,this.subInstructions=n,this._enterClassName=i,this._leaveClassName=r,this.errors=o,this.timelines=a,this.currentTimeline=l||new Et(this._driver,e,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,e){if(!t)return;let n=t,i=this.options;n.duration!=null&&(i.duration=X(n.duration)),n.delay!=null&&(i.delay=X(n.delay));let r=n.params;if(r){let o=i.params;o||(o=this.options.params={}),Object.keys(r).forEach(a=>{(!e||!o.hasOwnProperty(a))&&(o[a]=Qe(r[a],o,this.errors))})}}_copyOptions(){let t={};if(this.options){let e=this.options.params;if(e){let n=t.params={};Object.keys(e).forEach(i=>{n[i]=e[i]})}}return t}createSubContext(t=null,e,n){let i=e||this.element,r=new s(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,n||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(t),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(t){return this.previousNode=St,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,e,n){let i={duration:e??t.duration,delay:this.currentTimeline.currentTime+(n??0)+t.delay,easing:""},r=new sn(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,i,t.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,e,n,i,r,o){let a=[];if(i&&a.push(this.element),t.length>0){t=t.replace(hr,"."+this._enterClassName),t=t.replace(pr,"."+this._leaveClassName);let l=n!=1,c=this._driver.query(this.element,t,l);n!==0&&(c=n<0?c.slice(c.length+n,c.length):c.slice(0,n)),a.push(...c)}return!r&&a.length==0&&o.push(Ds(e)),a}},Et=class s{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(t,e,n,i){this._driver=t,this.element=e,this.startTime=n,this._elementTimelineStylesLookup=i,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(e),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(e,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let e=this._keyframes.size===1&&this._pendingStyles.size;this.duration||e?(this.forwardTime(this.currentTime+t),e&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,e){return this.applyStylesToKeyframe(),new s(this._driver,t,e||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=mr,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,e){this._localTimelineStyles.set(t,e),this._globalTimelineStyles.set(t,e),this._styleSummary.set(t,{time:this.currentTime,value:e})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[e,n]of this._globalTimelineStyles)this._backFill.set(e,n||$),this._currentKeyframe.set(e,$);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,e,n,i){e&&this._previousKeyframe.set("easing",e);let r=i&&i.params||{},o=gr(t,this._globalTimelineStyles);for(let[a,l]of o){let c=Qe(l,r,n);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??$),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,e)=>{this._currentKeyframe.set(e,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,e)=>{this._currentKeyframe.has(e)||this._currentKeyframe.set(e,t)}))}snapshotCurrentStyles(){for(let[t,e]of this._localTimelineStyles)this._pendingStyles.set(t,e),this._updateStyle(t,e)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let e in this._currentKeyframe)t.push(e);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((e,n)=>{let i=this._styleSummary.get(n);(!i||e.time>i.time)&&this._updateStyle(n,e.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,e=new Set,n=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((m,u)=>{m===mt?t.add(u):m===$&&e.add(u)}),n||c.set("offset",l/this.duration),i.push(c)});let r=[...t.values()],o=[...e.values()];if(n){let a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return yn(this.element,i,r,o,this.duration,this.startTime,this.easing,!1)}},sn=class extends Et{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(t,e,n,i,r,o,a=!1){super(t,e,o.delay),this.keyframes=n,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=a,this.timings={duration:o.duration,delay:o.delay,easing:o.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:e,duration:n,easing:i}=this.timings;if(this._stretchStartingKeyframe&&e){let r=[],o=n+e,a=e/o,l=new Map(t[0]);l.set("offset",0),r.push(l);let c=new Map(t[0]);c.set("offset",Oi(a)),r.push(c);let m=t.length-1;for(let u=1;u<=m;u++){let v=new Map(t[u]),_=v.get("offset"),h=e+_*n;v.set("offset",Oi(h/o)),r.push(v)}n=o,e=0,i="",t=r}return yn(this.element,t,this.preStyleProps,this.postStyleProps,n,e,i,!0)}};function Oi(s,t=3){let e=Math.pow(10,t-1);return Math.round(s*e)/e}function gr(s,t){let e=new Map,n;return s.forEach(i=>{if(i==="*"){n??=t.keys();for(let r of n)e.set(r,$)}else for(let[r,o]of i)e.set(r,o)}),e}function Fi(s,t,e,n,i,r,o,a,l,c,m,u,v){return{type:0,element:s,triggerName:t,isRemovalTransition:i,fromState:e,fromStyles:r,toState:n,toStyles:o,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:m,totalTime:u,errors:v}}var Ut={},wt=class{_triggerName;ast;_stateStyles;constructor(t,e,n){this._triggerName=t,this.ast=e,this._stateStyles=n}match(t,e,n,i){return yr(this.ast.matchers,t,e,n,i)}buildStyles(t,e,n){let i=this._stateStyles.get("*");return t!==void 0&&(i=this._stateStyles.get(t?.toString())||i),i?i.buildStyles(e,n):new Map}build(t,e,n,i,r,o,a,l,c,m){let u=[],v=this.ast.options&&this.ast.options.params||Ut,_=a&&a.params||Ut,h=this.buildStyles(n,_,u),b=l&&l.params||Ut,C=this.buildStyles(i,b,u),P=new Set,D=new Map,I=new Map,R=i==="void",ye={params:Xi(b,v),delay:this.ast.options?.delay},U=m?[]:Gi(t,e,this.ast.animation,r,o,h,C,ye,c,u),O=0;return U.forEach(F=>{O=Math.max(F.duration+F.delay,O)}),u.length?Fi(e,this._triggerName,n,i,R,h,C,[],[],D,I,O,u):(U.forEach(F=>{let ne=F.element,_e=z(D,ne,new Set);F.preStyleProps.forEach(ie=>_e.add(ie));let Sn=z(I,ne,new Set);F.postStyleProps.forEach(ie=>Sn.add(ie)),ne!==e&&P.add(ne)}),Fi(e,this._triggerName,n,i,R,h,C,U,[...P.values()],D,I,O))}};function yr(s,t,e,n,i){return s.some(r=>r(t,e,n,i))}function Xi(s,t){let e=re({},t);return Object.entries(s).forEach(([n,i])=>{i!=null&&(e[n]=i)}),e}var rn=class{styles;defaultParams;normalizer;constructor(t,e,n){this.styles=t,this.defaultParams=e,this.normalizer=n}buildStyles(t,e){let n=new Map,i=Xi(t,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((o,a)=>{o&&(o=Qe(o,i,e));let l=this.normalizer.normalizePropertyName(a,e);o=this.normalizer.normalizeStyleValue(a,l,o,e),n.set(a,o)})}),n}};function _r(s,t,e){return new on(s,t,e)}var on=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(t,e,n){this.name=t,this.ast=e,this._normalizer=n,e.states.forEach(i=>{let r=i.options&&i.options.params||{};this.states.set(i.name,new rn(i.style,r,n))}),xi(this.states,"true","1"),xi(this.states,"false","0"),e.transitions.forEach(i=>{this.transitionFactories.push(new wt(t,i,this.states))}),this.fallbackTransition=br(t,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,e,n,i){return this.transitionFactories.find(o=>o.match(t,e,n,i))||null}matchStyles(t,e,n){return this.fallbackTransition.buildStyles(t,e,n)}};function br(s,t,e){let n=[(o,a)=>!0],i={type:E.Sequence,steps:[],options:null},r={type:E.Transition,animation:i,matchers:n,options:null,queryCount:0,depCount:0};return new wt(s,r,t)}function xi(s,t,e){s.has(t)?s.has(e)||s.set(e,s.get(t)):s.has(e)&&s.set(t,s.get(e))}var vr=new Ve,an=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(t,e,n){this.bodyNode=t,this._driver=e,this._normalizer=n}register(t,e){let n=[],i=[],r=Yi(this._driver,e,n,i);if(n.length)throw Os(n);this._animations.set(t,r)}_buildPlayer(t,e,n){let i=t.element,r=Qi(this._normalizer,t.keyframes,e,n);return this._driver.animate(i,r,t.duration,t.delay,t.easing,[],!0)}create(t,e,n={}){let i=[],r=this._animations.get(t),o,a=new Map;if(r?(o=Gi(this._driver,e,r,Hi,Yt,new Map,new Map,n,vr,i),o.forEach(m=>{let u=z(a,m.element,new Map);m.postStyleProps.forEach(v=>u.set(v,null))})):(i.push(Fs()),o=[]),i.length)throw xs(i);a.forEach((m,u)=>{m.forEach((v,_)=>{m.set(_,this._driver.computeStyle(u,_,$))})});let l=o.map(m=>{let u=a.get(m.element);return this._buildPlayer(m,new Map,u)}),c=ee(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let e=this._getPlayer(t);e.destroy(),this._playersById.delete(t);let n=this.players.indexOf(e);n>=0&&this.players.splice(n,1)}_getPlayer(t){let e=this._playersById.get(t);if(!e)throw Ls(t);return e}listen(t,e,n,i){let r=dn(e,"","","");return mn(this._getPlayer(t),n,r,i),()=>{}}command(t,e,n,i){if(n=="register"){this.register(t,i[0]);return}if(n=="create"){let o=i[0]||{};this.create(t,e,o);return}let r=this._getPlayer(t);switch(n){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(t);break}}},Li="ng-animate-queued",Sr=".ng-animate-queued",Ht="ng-animate-disabled",Er=".ng-animate-disabled",wr="ng-star-inserted",Tr=".ng-star-inserted",Mr=[],Zi={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},Cr={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},j="__ng_removed",je=class{namespaceId;value;options;get params(){return this.options.params}constructor(t,e=""){this.namespaceId=e;let n=t&&t.hasOwnProperty("value"),i=n?t.value:t;if(this.value=Pr(i),n){let r=t,{value:o}=r,a=Cn(r,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let e=t.params;if(e){let n=this.options.params;Object.keys(e).forEach(i=>{n[i]==null&&(n[i]=e[i])})}}},qe="void",$t=new je(qe),ln=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(t,e,n){this.id=t,this.hostElement=e,this._engine=n,this._hostClassName="ng-tns-"+t,Q(e,this._hostClassName)}listen(t,e,n,i){if(!this._triggers.has(e))throw Bs(n,e);if(n==null||n.length==0)throw zs(e);if(!Dr(n))throw Ks(n,e);let r=z(this._elementListeners,t,[]),o={name:e,phase:n,callback:i};r.push(o);let a=z(this._engine.statesByElement,t,new Map);return a.has(e)||(Q(t,dt),Q(t,dt+"-"+e),a.set(e,$t)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(o);l>=0&&r.splice(l,1),this._triggers.has(e)||a.delete(e)})}}register(t,e){return this._triggers.has(t)?!1:(this._triggers.set(t,e),!0)}_getTrigger(t){let e=this._triggers.get(t);if(!e)throw qs(t);return e}trigger(t,e,n,i=!0){let r=this._getTrigger(e),o=new Ue(this.id,e,t),a=this._engine.statesByElement.get(t);a||(Q(t,dt),Q(t,dt+"-"+e),this._engine.statesByElement.set(t,a=new Map));let l=a.get(e),c=new je(n,this.id);if(!(n&&n.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(e,c),l||(l=$t),!(c.value===qe)&&l.value===c.value){if(!Nr(l.params,c.params)){let b=[],C=r.matchStyles(l.value,l.params,b),P=r.matchStyles(c.value,c.params,b);b.length?this._engine.reportError(b):this._engine.afterFlush(()=>{fe(t,C),W(t,P)})}return}let v=z(this._engine.playersByElement,t,[]);v.forEach(b=>{b.namespaceId==this.id&&b.triggerName==e&&b.queued&&b.destroy()});let _=r.matchTransition(l.value,c.value,t,c.params),h=!1;if(!_){if(!i)return;_=r.fallbackTransition,h=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:e,transition:_,fromState:l,toState:c,player:o,isFallbackTransition:h}),h||(Q(t,Li),o.onStart(()=>{Me(t,Li)})),o.onDone(()=>{let b=this.players.indexOf(o);b>=0&&this.players.splice(b,1);let C=this._engine.playersByElement.get(t);if(C){let P=C.indexOf(o);P>=0&&C.splice(P,1)}}),this.players.push(o),v.push(o),o}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(e=>e.delete(t)),this._elementListeners.forEach((e,n)=>{this._elementListeners.set(n,e.filter(i=>i.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let e=this._engine.playersByElement.get(t);e&&(e.forEach(n=>n.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,e){let n=this._engine.driver.query(t,yt,!0);n.forEach(i=>{if(i[j])return;let r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(o=>o.triggerLeaveAnimation(i,e,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>n.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(t,e,n,i){let r=this._engine.statesByElement.get(t),o=new Map;if(r){let a=[];if(r.forEach((l,c)=>{if(o.set(c,l.value),this._triggers.has(c)){let m=this.trigger(t,c,qe,i);m&&a.push(m)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,e,o),n&&ee(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let e=this._elementListeners.get(t),n=this._engine.statesByElement.get(t);if(e&&n){let i=new Set;e.forEach(r=>{let o=r.name;if(i.has(o))return;i.add(o);let l=this._triggers.get(o).fallbackTransition,c=n.get(o)||$t,m=new je(qe),u=new Ue(this.id,o,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:o,transition:l,fromState:c,toState:m,player:u,isFallbackTransition:!0})})}}removeNode(t,e){let n=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,e),this.triggerLeaveAnimation(t,e,!0))return;let i=!1;if(n.totalAnimations){let r=n.players.length?n.playersByQueriedElement.get(t):[];if(r&&r.length)i=!0;else{let o=t;for(;o=o.parentNode;)if(n.statesByElement.get(o)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(t),i)n.markElementAsRemoved(this.id,t,!1,e);else{let r=t[j];(!r||r===Zi)&&(n.afterFlush(()=>this.clearElementCache(t)),n.destroyInnerAnimations(t),n._onRemovalComplete(t,e))}}insertNode(t,e){Q(t,this._hostClassName)}drainQueuedTransitions(t){let e=[];return this._queue.forEach(n=>{let i=n.player;if(i.destroyed)return;let r=n.element,o=this._elementListeners.get(r);o&&o.forEach(a=>{if(a.name==n.triggerName){let l=dn(r,n.triggerName,n.fromState.value,n.toState.value);l._data=t,mn(n.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):e.push(n)}),this._queue=[],e.sort((n,i)=>{let r=n.transition.ast.depCount,o=i.transition.ast.depCount;return r==0||o==0?r-o:this._engine.driver.containsElement(n.element,i.element)?1:-1})}destroy(t){this.players.forEach(e=>e.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},cn=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(t,e)=>{};_onRemovalComplete(t,e){this.onRemovalComplete(t,e)}constructor(t,e,n){this.bodyNode=t,this.driver=e,this._normalizer=n}get queuedPlayers(){let t=[];return this._namespaceList.forEach(e=>{e.players.forEach(n=>{n.queued&&t.push(n)})}),t}createNamespace(t,e){let n=new ln(t,e,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,e)?this._balanceNamespaceList(n,e):(this.newHostElements.set(e,n),this.collectEnterElement(e)),this._namespaceLookup[t]=n}_balanceNamespaceList(t,e){let n=this._namespaceList,i=this.namespacesByHostElement;if(n.length-1>=0){let o=!1,a=this.driver.getParentElement(e);for(;a;){let l=i.get(a);if(l){let c=n.indexOf(l);n.splice(c+1,0,t),o=!0;break}a=this.driver.getParentElement(a)}o||n.unshift(t)}else n.push(t);return i.set(e,t),t}register(t,e){let n=this._namespaceLookup[t];return n||(n=this.createNamespace(t,e)),n}registerTrigger(t,e,n){let i=this._namespaceLookup[t];i&&i.register(e,n)&&this.totalAnimations++}destroy(t,e){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let n=this._fetchNamespace(t);this.namespacesByHostElement.delete(n.hostElement);let i=this._namespaceList.indexOf(n);i>=0&&this._namespaceList.splice(i,1),n.destroy(e),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let e=new Set,n=this.statesByElement.get(t);if(n){for(let i of n.values())if(i.namespaceId){let r=this._fetchNamespace(i.namespaceId);r&&e.add(r)}}return e}trigger(t,e,n,i){if(pt(e)){let r=this._fetchNamespace(t);if(r)return r.trigger(e,n,i),!0}return!1}insertNode(t,e,n,i){if(!pt(e))return;let r=e[j];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let o=this.collectedLeaveElements.indexOf(e);o>=0&&this.collectedLeaveElements.splice(o,1)}if(t){let o=this._fetchNamespace(t);o&&o.insertNode(e,n)}i&&this.collectEnterElement(e)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,e){e?this.disabledNodes.has(t)||(this.disabledNodes.add(t),Q(t,Ht)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Me(t,Ht))}removeNode(t,e,n){if(pt(e)){let i=t?this._fetchNamespace(t):null;i?i.removeNode(e,n):this.markElementAsRemoved(t,e,!1,n);let r=this.namespacesByHostElement.get(e);r&&r.id!==t&&r.removeNode(e,n)}else this._onRemovalComplete(e,n)}markElementAsRemoved(t,e,n,i,r){this.collectedLeaveElements.push(e),e[j]={namespaceId:t,setForRemoval:i,hasAnimation:n,removedBeforeQueried:!1,previousTriggersValues:r}}listen(t,e,n,i,r){return pt(e)?this._fetchNamespace(t).listen(e,n,i,r):()=>{}}_buildInstruction(t,e,n,i,r){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,n,i,t.fromState.options,t.toState.options,e,r)}destroyInnerAnimations(t){let e=this.driver.query(t,yt,!0);e.forEach(n=>this.destroyActiveAnimationsForElement(n)),this.playersByQueriedElement.size!=0&&(e=this.driver.query(t,Gt,!0),e.forEach(n=>this.finishActiveQueriedAnimationOnElement(n)))}destroyActiveAnimationsForElement(t){let e=this.playersByElement.get(t);e&&e.forEach(n=>{n.queued?n.markedForDestroy=!0:n.destroy()})}finishActiveQueriedAnimationOnElement(t){let e=this.playersByQueriedElement.get(t);e&&e.forEach(n=>n.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return ee(this.players).onDone(()=>t());t()})}processLeaveNode(t){let e=t[j];if(e&&e.setForRemoval){if(t[j]=Zi,e.namespaceId){this.destroyInnerAnimations(t);let n=this._fetchNamespace(e.namespaceId);n&&n.clearElementCache(t)}this._onRemovalComplete(t,e.setForRemoval)}t.classList?.contains(Ht)&&this.markElementAsDisabled(t,!1),this.driver.query(t,Er,!0).forEach(n=>{this.markElementAsDisabled(n,!1)})}flush(t=-1){let e=[];if(this.newHostElements.size&&(this.newHostElements.forEach((n,i)=>this._balanceNamespaceList(n,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let n=0;n<this.collectedEnterElements.length;n++){let i=this.collectedEnterElements[n];Q(i,wr)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let n=[];try{e=this._flushAnimations(n,t)}finally{for(let i=0;i<n.length;i++)n[i]()}}else for(let n=0;n<this.collectedLeaveElements.length;n++){let i=this.collectedLeaveElements[n];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(n=>n()),this._flushFns=[],this._whenQuietFns.length){let n=this._whenQuietFns;this._whenQuietFns=[],e.length?ee(e).onDone(()=>{n.forEach(i=>i())}):n.forEach(i=>i())}}reportError(t){throw Qs(t)}_flushAnimations(t,e){let n=new Ve,i=[],r=new Map,o=[],a=new Map,l=new Map,c=new Map,m=new Set;this.disabledNodes.forEach(d=>{m.add(d);let f=this.driver.query(d,Sr,!0);for(let g=0;g<f.length;g++)m.add(f[g])});let u=this.bodyNode,v=Array.from(this.statesByElement.keys()),_=Ki(v,this.collectedEnterElements),h=new Map,b=0;_.forEach((d,f)=>{let g=Hi+b++;h.set(f,g),d.forEach(w=>Q(w,g))});let C=[],P=new Set,D=new Set;for(let d=0;d<this.collectedLeaveElements.length;d++){let f=this.collectedLeaveElements[d],g=f[j];g&&g.setForRemoval&&(C.push(f),P.add(f),g.hasAnimation?this.driver.query(f,Tr,!0).forEach(w=>P.add(w)):D.add(f))}let I=new Map,R=Ki(v,Array.from(P));R.forEach((d,f)=>{let g=Yt+b++;I.set(f,g),d.forEach(w=>Q(w,g))}),t.push(()=>{_.forEach((d,f)=>{let g=h.get(f);d.forEach(w=>Me(w,g))}),R.forEach((d,f)=>{let g=I.get(f);d.forEach(w=>Me(w,g))}),C.forEach(d=>{this.processLeaveNode(d)})});let ye=[],U=[];for(let d=this._namespaceList.length-1;d>=0;d--)this._namespaceList[d].drainQueuedTransitions(e).forEach(g=>{let w=g.player,k=g.element;if(ye.push(w),this.collectedEnterElements.length){let N=k[j];if(N&&N.setForMove){if(N.previousTriggersValues&&N.previousTriggersValues.has(g.triggerName)){let se=N.previousTriggersValues.get(g.triggerName),K=this.statesByElement.get(g.element);if(K&&K.has(g.triggerName)){let We=K.get(g.triggerName);We.value=se,K.set(g.triggerName,We)}}w.destroy();return}}let H=!u||!this.driver.containsElement(u,k),x=I.get(k),Z=h.get(k),A=this._buildInstruction(g,n,Z,x,H);if(A.errors&&A.errors.length){U.push(A);return}if(H){w.onStart(()=>fe(k,A.fromStyles)),w.onDestroy(()=>W(k,A.toStyles)),i.push(w);return}if(g.isFallbackTransition){w.onStart(()=>fe(k,A.fromStyles)),w.onDestroy(()=>W(k,A.toStyles)),i.push(w);return}let Tn=[];A.timelines.forEach(N=>{N.stretchStartingKeyframe=!0,this.disabledNodes.has(N.element)||Tn.push(N)}),A.timelines=Tn,n.append(k,A.timelines);let ds={instruction:A,player:w,element:k};o.push(ds),A.queriedElements.forEach(N=>z(a,N,[]).push(w)),A.preStyleProps.forEach((N,se)=>{if(N.size){let K=l.get(se);K||l.set(se,K=new Set),N.forEach((We,Lt)=>K.add(Lt))}}),A.postStyleProps.forEach((N,se)=>{let K=c.get(se);K||c.set(se,K=new Set),N.forEach((We,Lt)=>K.add(Lt))})});if(U.length){let d=[];U.forEach(f=>{d.push(Vs(f.triggerName,f.errors))}),ye.forEach(f=>f.destroy()),this.reportError(d)}let O=new Map,F=new Map;o.forEach(d=>{let f=d.element;n.has(f)&&(F.set(f,f),this._beforeAnimationBuild(d.player.namespaceId,d.instruction,O))}),i.forEach(d=>{let f=d.element;this._getPreviousPlayers(f,!1,d.namespaceId,d.triggerName,null).forEach(w=>{z(O,f,[]).push(w),w.destroy()})});let ne=C.filter(d=>qi(d,l,c)),_e=new Map;zi(_e,this.driver,D,c,$).forEach(d=>{qi(d,l,c)&&ne.push(d)});let ie=new Map;_.forEach((d,f)=>{zi(ie,this.driver,new Set(d),l,mt)}),ne.forEach(d=>{let f=_e.get(d),g=ie.get(d);_e.set(d,new Map([...f?.entries()??[],...g?.entries()??[]]))});let xt=[],En=[],wn={};o.forEach(d=>{let{element:f,player:g,instruction:w}=d;if(n.has(f)){if(m.has(f)){g.onDestroy(()=>W(f,w.toStyles)),g.disabled=!0,g.overrideTotalTime(w.totalTime),i.push(g);return}let k=wn;if(F.size>1){let x=f,Z=[];for(;x=x.parentNode;){let A=F.get(x);if(A){k=A;break}Z.push(x)}Z.forEach(A=>F.set(A,k))}let H=this._buildAnimation(g.namespaceId,w,O,r,ie,_e);if(g.setRealPlayer(H),k===wn)xt.push(g);else{let x=this.playersByElement.get(k);x&&x.length&&(g.parentPlayer=ee(x)),i.push(g)}}else fe(f,w.fromStyles),g.onDestroy(()=>W(f,w.toStyles)),En.push(g),m.has(f)&&i.push(g)}),En.forEach(d=>{let f=r.get(d.element);if(f&&f.length){let g=ee(f);d.setRealPlayer(g)}}),i.forEach(d=>{d.parentPlayer?d.syncPlayerEvents(d.parentPlayer):d.destroy()});for(let d=0;d<C.length;d++){let f=C[d],g=f[j];if(Me(f,Yt),g&&g.hasAnimation)continue;let w=[];if(a.size){let H=a.get(f);H&&H.length&&w.push(...H);let x=this.driver.query(f,Gt,!0);for(let Z=0;Z<x.length;Z++){let A=a.get(x[Z]);A&&A.length&&w.push(...A)}}let k=w.filter(H=>!H.destroyed);k.length?kr(this,f,k):this.processLeaveNode(f)}return C.length=0,xt.forEach(d=>{this.players.push(d),d.onDone(()=>{d.destroy();let f=this.players.indexOf(d);this.players.splice(f,1)}),d.play()}),xt}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,e,n,i,r){let o=[];if(e){let a=this.playersByQueriedElement.get(t);a&&(o=a)}else{let a=this.playersByElement.get(t);if(a){let l=!r||r==qe;a.forEach(c=>{c.queued||!l&&c.triggerName!=i||o.push(c)})}}return(n||i)&&(o=o.filter(a=>!(n&&n!=a.namespaceId||i&&i!=a.triggerName))),o}_beforeAnimationBuild(t,e,n){let i=e.triggerName,r=e.element,o=e.isRemovalTransition?void 0:t,a=e.isRemovalTransition?void 0:i;for(let l of e.timelines){let c=l.element,m=c!==r,u=z(n,c,[]);this._getPreviousPlayers(c,m,o,a,e.toState).forEach(_=>{let h=_.getRealPlayer();h.beforeDestroy&&h.beforeDestroy(),_.destroy(),u.push(_)})}fe(r,e.fromStyles)}_buildAnimation(t,e,n,i,r,o){let a=e.triggerName,l=e.element,c=[],m=new Set,u=new Set,v=e.timelines.map(h=>{let b=h.element;m.add(b);let C=b[j];if(C&&C.removedBeforeQueried)return new J(h.duration,h.delay);let P=b!==l,D=Rr((n.get(b)||Mr).map(O=>O.getRealPlayer())).filter(O=>{let F=O;return F.element?F.element===b:!1}),I=r.get(b),R=o.get(b),ye=Qi(this._normalizer,h.keyframes,I,R),U=this._buildPlayer(h,ye,D);if(h.subTimeline&&i&&u.add(b),P){let O=new Ue(t,a,b);O.setRealPlayer(U),c.push(O)}return U});c.forEach(h=>{z(this.playersByQueriedElement,h.element,[]).push(h),h.onDone(()=>Ar(this.playersByQueriedElement,h.element,h))}),m.forEach(h=>Q(h,Ri));let _=ee(v);return _.onDestroy(()=>{m.forEach(h=>Me(h,Ri)),W(l,e.toStyles)}),u.forEach(h=>{z(i,h,[]).push(_)}),_}_buildPlayer(t,e,n){return e.length>0?this.driver.animate(t.element,e,t.duration,t.delay,t.easing,n):new J(t.duration,t.delay)}},Ue=class{namespaceId;triggerName;element;_player=new J;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(t,e,n){this.namespaceId=t,this.triggerName=e,this.element=n}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((e,n)=>{e.forEach(i=>mn(t,n,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let e=this._player;e.triggerCallback&&t.onStart(()=>e.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,e){z(this._queuedCallbacks,t,[]).push(e)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let e=this._player;e.triggerCallback&&e.triggerCallback(t)}};function Ar(s,t,e){let n=s.get(t);if(n){if(n.length){let i=n.indexOf(e);n.splice(i,1)}n.length==0&&s.delete(t)}return n}function Pr(s){return s??null}function pt(s){return s&&s.nodeType===1}function Dr(s){return s=="start"||s=="done"}function Bi(s,t){let e=s.style.display;return s.style.display=t??"none",e}function zi(s,t,e,n,i){let r=[];e.forEach(l=>r.push(Bi(l)));let o=[];n.forEach((l,c)=>{let m=new Map;l.forEach(u=>{let v=t.computeStyle(c,u,i);m.set(u,v),(!v||v.length==0)&&(c[j]=Cr,o.push(c))}),s.set(c,m)});let a=0;return e.forEach(l=>Bi(l,r[a++])),o}function Ki(s,t){let e=new Map;if(s.forEach(a=>e.set(a,[])),t.length==0)return e;let n=1,i=new Set(t),r=new Map;function o(a){if(!a)return n;let l=r.get(a);if(l)return l;let c=a.parentNode;return e.has(c)?l=c:i.has(c)?l=n:l=o(c),r.set(a,l),l}return t.forEach(a=>{let l=o(a);l!==n&&e.get(l).push(a)}),e}function Q(s,t){s.classList?.add(t)}function Me(s,t){s.classList?.remove(t)}function kr(s,t,e){ee(e).onDone(()=>s.processLeaveNode(t))}function Rr(s){let t=[];return Ji(s,t),t}function Ji(s,t){for(let e=0;e<s.length;e++){let n=s[e];n instanceof ze?Ji(n.players,t):t.push(n)}}function Nr(s,t){let e=Object.keys(s),n=Object.keys(t);if(e.length!=n.length)return!1;for(let i=0;i<e.length;i++){let r=e[i];if(!t.hasOwnProperty(r)||s[r]!==t[r])return!1}return!0}function qi(s,t,e){let n=e.get(s);if(!n)return!1;let i=t.get(s);return i?n.forEach(r=>i.add(r)):t.set(s,n),e.delete(s),!0}var Ce=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(t,e)=>{};constructor(t,e,n){this._driver=e,this._normalizer=n,this._transitionEngine=new cn(t.body,e,n),this._timelineEngine=new an(t.body,e,n),this._transitionEngine.onRemovalComplete=(i,r)=>this.onRemovalComplete(i,r)}registerTrigger(t,e,n,i,r){let o=t+"-"+i,a=this._triggerCache[o];if(!a){let l=[],c=[],m=Yi(this._driver,r,l,c);if(l.length)throw Ns(i,l);a=_r(i,m,this._normalizer),this._triggerCache[o]=a}this._transitionEngine.registerTrigger(e,i,a)}register(t,e){this._transitionEngine.register(t,e)}destroy(t,e){this._transitionEngine.destroy(t,e)}onInsert(t,e,n,i){this._transitionEngine.insertNode(t,e,n,i)}onRemove(t,e,n){this._transitionEngine.removeNode(t,e,n)}disableAnimations(t,e){this._transitionEngine.markElementAsDisabled(t,e)}process(t,e,n,i){if(n.charAt(0)=="@"){let[r,o]=Di(n),a=i;this._timelineEngine.command(r,e,o,a)}else this._transitionEngine.trigger(t,e,n,i)}listen(t,e,n,i,r){if(n.charAt(0)=="@"){let[o,a]=Di(n);return this._timelineEngine.listen(o,e,a,r)}return this._transitionEngine.listen(t,e,n,i,r)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function Ir(s,t){let e=null,n=null;return Array.isArray(t)&&t.length?(e=Wt(t[0]),t.length>1&&(n=Wt(t[t.length-1]))):t instanceof Map&&(e=Wt(t)),e||n?new Or(s,e,n):null}var Or=(()=>{class s{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(e,n,i){this._element=e,this._startStyles=n,this._endStyles=i;let r=s.initialStylesByElement.get(e);r||s.initialStylesByElement.set(e,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&W(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(W(this._element,this._initialStyles),this._endStyles&&(W(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(s.initialStylesByElement.delete(this._element),this._startStyles&&(fe(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(fe(this._element,this._endStyles),this._endStyles=null),W(this._element,this._initialStyles),this._state=3)}}return s})();function Wt(s){let t=null;return s.forEach((e,n)=>{Fr(n)&&(t=t||new Map,t.set(n,e))}),t}function Fr(s){return s==="display"||s==="position"}var Tt=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(t,e,n,i){this.element=t,this.keyframes=e,this.options=n,this._specialStyles=i,this._duration=n.duration,this._delay=n.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let e=()=>this._onFinish();this.domPlayer.addEventListener("finish",e),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",e)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let e=[];return t.forEach(n=>{e.push(Object.fromEntries(n))}),e}_triggerWebAnimation(t,e,n){return t.animate(this._convertKeyframesToObject(e),n)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((n,i)=>{i!=="offset"&&t.set(i,this._finished?n:gn(this.element,i))}),this.currentSnapshot=t}triggerCallback(t){let e=t==="start"?this._onStartFns:this._onDoneFns;e.forEach(n=>n()),e.length=0}},Mt=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,e){return Vi(t,e)}getParentElement(t){return hn(t)}query(t,e,n){return ji(t,e,n)}computeStyle(t,e,n){return gn(t,e)}animate(t,e,n,i,r,o=[]){let a=i==0?"both":"forwards",l={duration:n,delay:i,fill:a};r&&(l.easing=r);let c=new Map,m=o.filter(_=>_ instanceof Tt);er(n,i)&&m.forEach(_=>{_.currentSnapshot.forEach((h,b)=>c.set(b,h))});let u=Xs(e).map(_=>new Map(_));u=tr(t,u,c);let v=Ir(t,u);return new Tt(t,u,l,v)}};var gt="@",es="@.disabled",Ct=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(t,e,n,i){this.namespaceId=t,this.delegate=e,this.engine=n,this._onDestroy=i}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,e){return this.delegate.createElement(t,e)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,e){this.delegate.appendChild(t,e),this.engine.onInsert(this.namespaceId,e,t,!1)}insertBefore(t,e,n,i=!0){this.delegate.insertBefore(t,e,n),this.engine.onInsert(this.namespaceId,e,t,i)}removeChild(t,e,n){this.parentNode(e)&&this.engine.onRemove(this.namespaceId,e,this.delegate)}selectRootElement(t,e){return this.delegate.selectRootElement(t,e)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,e,n,i){this.delegate.setAttribute(t,e,n,i)}removeAttribute(t,e,n){this.delegate.removeAttribute(t,e,n)}addClass(t,e){this.delegate.addClass(t,e)}removeClass(t,e){this.delegate.removeClass(t,e)}setStyle(t,e,n,i){this.delegate.setStyle(t,e,n,i)}removeStyle(t,e,n){this.delegate.removeStyle(t,e,n)}setProperty(t,e,n){e.charAt(0)==gt&&e==es?this.disableAnimations(t,!!n):this.delegate.setProperty(t,e,n)}setValue(t,e){this.delegate.setValue(t,e)}listen(t,e,n,i){return this.delegate.listen(t,e,n,i)}disableAnimations(t,e){this.engine.disableAnimations(t,e)}},un=class extends Ct{factory;constructor(t,e,n,i,r){super(e,n,i,r),this.factory=t,this.namespaceId=e}setProperty(t,e,n){e.charAt(0)==gt?e.charAt(1)=="."&&e==es?(n=n===void 0?!0:!!n,this.disableAnimations(t,n)):this.engine.process(this.namespaceId,t,e.slice(1),n):this.delegate.setProperty(t,e,n)}listen(t,e,n,i){if(e.charAt(0)==gt){let r=xr(t),o=e.slice(1),a="";return o.charAt(0)!=gt&&([o,a]=Lr(o)),this.engine.listen(this.namespaceId,r,o,a,l=>{let c=l._data||-1;this.factory.scheduleListenerCallback(c,n,l)})}return this.delegate.listen(t,e,n,i)}};function xr(s){switch(s){case"body":return document.body;case"document":return document;case"window":return window;default:return s}}function Lr(s){let t=s.indexOf("."),e=s.substring(0,t),n=s.slice(t+1);return[e,n]}var At=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(t,e,n){this.delegate=t,this.engine=e,this._zone=n,e.onRemovalComplete=(i,r)=>{r?.removeChild(null,i)}}createRenderer(t,e){let n="",i=this.delegate.createRenderer(t,e);if(!t||!e?.data?.animation){let c=this._rendererCache,m=c.get(i);if(!m){let u=()=>c.delete(i);m=new Ct(n,i,this.engine,u),c.set(i,m)}return m}let r=e.id,o=e.id+"-"+this._currentId;this._currentId++,this.engine.register(o,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(r,o,t,c.name,c)};return e.data.animation.forEach(a),new un(this,o,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,e,n){if(t>=0&&t<this._microtaskId){this._zone.run(()=>e(n));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{let[o,a]=r;o(a)}),this._animationCallbacksBuffer=[]})}),i.push([e,n])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(t){this.engine.flush(),this.delegate.componentReplaced?.(t)}};var zr=(()=>{class s extends Ce{constructor(e,n,i){super(e,n,i)}ngOnDestroy(){this.flush()}static \u0275fac=function(n){return new(n||s)(oe(we),oe(pe),oe(ge))};static \u0275prov=be({token:s,factory:s.\u0275fac})}return s})();function Kr(){return new bt}function qr(s,t,e){return new At(s,t,e)}var ts=[{provide:ge,useFactory:Kr},{provide:Ce,useClass:zr},{provide:Bn,useFactory:qr,deps:[Zn,Ce,et]}],Qr=[{provide:pe,useFactory:()=>new Mt},{provide:ke,useValue:"BrowserAnimations"},...ts],ko=[{provide:pe,useClass:fn},{provide:ke,useValue:"NoopAnimations"},...ts];function ns(){return On("NgEagerAnimations"),[...Qr]}var Pt=class s{static \u0275fac=function(e){return new(e||s)};static \u0275cmp=L({type:s,selectors:[["app-home"]],decls:15,vars:0,consts:[[1,"home-container"],[1,"welcome-card"],["mat-raised-button","","color","primary","routerLink","/map"],["mat-raised-button","","color","accent","routerLink","/lines"],["mat-raised-button","","color","warn","routerLink","/points"]],template:function(e,n){e&1&&(S(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),y(4,"Welcome to Optical Lines Database"),p()(),S(5,"mat-card-content")(6,"p"),y(7," This application provides a comprehensive database of optical lines and connection points. You can view the data on a map, browse tables, and if you're a provider, manage your own lines and points. "),p()(),S(8,"mat-card-actions")(9,"button",2),y(10,"View Map"),p(),S(11,"button",3),y(12,"Browse Lines"),p(),S(13,"button",4),y(14,"Browse Points"),p()()()())},dependencies:[vi,pi,_i,yi,bi,gi,lt,at,ot],styles:[".home-container[_ngcontent-%COMP%]{padding:20px;display:flex;justify-content:center;align-items:center;min-height:calc(100vh - 120px)}.welcome-card[_ngcontent-%COMP%]{max-width:800px;width:100%}mat-card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:10px}"]})};var is=[{path:"",redirectTo:"home",pathMatch:"full"},{path:"home",component:Pt},{path:"map",loadChildren:()=>import("./chunk-LUDYLE2Q.js").then(s=>s.MAP_ROUTES)},{path:"lines",loadChildren:()=>import("./chunk-4GSQWCKL.js").then(s=>s.LINES_ROUTES)},{path:"points",loadChildren:()=>import("./chunk-5KROCUQ4.js").then(s=>s.POINTS_ROUTES)},{path:"auth",loadChildren:()=>import("./chunk-BCNHE33H.js").then(s=>s.AUTH_ROUTES)},{path:"provider",loadChildren:()=>import("./chunk-5GI5MFGY.js").then(s=>s.PROVIDER_ROUTES)},{path:"**",redirectTo:"home"}];var Dt=class s{constructor(t,e){this.authService=t;this.router=e}intercept(t,e){let n=this.authService.getToken();return n&&(t=t.clone({setHeaders:{Authorization:`Bearer ${n}`}})),e.handle(t).pipe(Dn(i=>(i.status===401&&(this.authService.logout(),this.router.navigate(["/auth/login"])),An(()=>i))))}static \u0275fac=function(e){return new(e||s)(oe(ut),oe(ti))};static \u0275prov=be({token:s,factory:s.\u0275fac})};var ss={providers:[Hn({eventCoalescing:!0}),ni(is),Gn(Xn()),ns(),{provide:Yn,useClass:Dt,multi:!0}]};var Ur=["*",[["mat-toolbar-row"]]],Hr=["*","mat-toolbar-row"],$r=(()=>{class s{static \u0275fac=function(n){return new(n||s)};static \u0275dir=tt({type:s,selectors:[["mat-toolbar-row"]],hostAttrs:[1,"mat-toolbar-row"],exportAs:["matToolbarRow"]})}return s})(),kt=(()=>{class s{_elementRef=M(ve);_platform=M(ii);_document=M(we);color;_toolbarRows;constructor(){}ngAfterViewInit(){this._platform.isBrowser&&(this._checkToolbarMixedModes(),this._toolbarRows.changes.subscribe(()=>this._checkToolbarMixedModes()))}_checkToolbarMixedModes(){this._toolbarRows.length}static \u0275fac=function(n){return new(n||s)};static \u0275cmp=L({type:s,selectors:[["mat-toolbar"]],contentQueries:function(n,i,r){if(n&1&&Ee(r,$r,5),n&2){let o;ue(o=me())&&(i._toolbarRows=o)}},hostAttrs:[1,"mat-toolbar"],hostVars:6,hostBindings:function(n,i){n&2&&(nt(i.color?"mat-"+i.color:""),Oe("mat-toolbar-multiple-rows",i._toolbarRows.length>0)("mat-toolbar-single-row",i._toolbarRows.length===0))},inputs:{color:"color"},exportAs:["matToolbar"],ngContentSelectors:Hr,decls:2,vars:0,template:function(n,i){n&1&&(Le(Ur),ce(0),ce(1,1))},styles:[".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}"],encapsulation:2,changeDetection:0})}return s})();var Rt=(()=>{class s{static \u0275fac=function(n){return new(n||s)};static \u0275mod=Re({type:s});static \u0275inj=Pe({imports:[Te,Te]})}return s})();var Jr=["mat-menu-item",""],eo=[[["mat-icon"],["","matMenuItemIcon",""]],"*"],to=["mat-icon, [matMenuItemIcon]","*"];function no(s,t){s&1&&(Nn(),S(0,"svg",2),Y(1,"polygon",3),p())}var io=["*"];function so(s,t){if(s&1){let e=it();S(0,"div",0),Se("click",function(){ae(e);let i=G();return le(i.closed.emit("click"))})("animationstart",function(i){ae(e);let r=G();return le(r._onAnimationStart(i.animationName))})("animationend",function(i){ae(e);let r=G();return le(r._onAnimationDone(i.animationName))})("animationcancel",function(i){ae(e);let r=G();return le(r._onAnimationDone(i.animationName))}),S(1,"div",1),ce(2),p()()}if(s&2){let e=G();nt(e._classList),Oe("mat-menu-panel-animations-disabled",e._animationsDisabled)("mat-menu-panel-exit-animation",e._panelAnimationState==="void")("mat-menu-panel-animating",e._isAnimating),V("id",e.panelId),Ie("aria-label",e.ariaLabel||null)("aria-labelledby",e.ariaLabelledby||null)("aria-describedby",e.ariaDescribedby||null)}}var vn=new De("MAT_MENU_PANEL"),$e=(()=>{class s{_elementRef=M(ve);_document=M(we);_focusMonitor=M(qt);_parentMenu=M(vn,{optional:!0});_changeDetectorRef=M(rt);role="menuitem";disabled=!1;disableRipple=!1;_hovered=new Ge;_focused=new Ge;_highlighted=!1;_triggersSubmenu=!1;constructor(){M(ri).load(di),this._parentMenu?.addItem?.(this)}focus(e,n){this._focusMonitor&&e?this._focusMonitor.focusVia(this._getHostElement(),e,n):this._getHostElement().focus(n),this._focused.next(this)}ngAfterViewInit(){this._focusMonitor&&this._focusMonitor.monitor(this._elementRef,!1)}ngOnDestroy(){this._focusMonitor&&this._focusMonitor.stopMonitoring(this._elementRef),this._parentMenu&&this._parentMenu.removeItem&&this._parentMenu.removeItem(this),this._hovered.complete(),this._focused.complete()}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._elementRef.nativeElement}_checkDisabled(e){this.disabled&&(e.preventDefault(),e.stopPropagation())}_handleMouseEnter(){this._hovered.next(this)}getLabel(){let e=this._elementRef.nativeElement.cloneNode(!0),n=e.querySelectorAll("mat-icon, .material-icons");for(let i=0;i<n.length;i++)n[i].remove();return e.textContent?.trim()||""}_setHighlighted(e){this._highlighted=e,this._changeDetectorRef.markForCheck()}_setTriggersSubmenu(e){this._triggersSubmenu=e,this._changeDetectorRef.markForCheck()}_hasFocus(){return this._document&&this._document.activeElement===this._getHostElement()}static \u0275fac=function(n){return new(n||s)};static \u0275cmp=L({type:s,selectors:[["","mat-menu-item",""]],hostAttrs:[1,"mat-mdc-menu-item","mat-focus-indicator"],hostVars:8,hostBindings:function(n,i){n&1&&Se("click",function(o){return i._checkDisabled(o)})("mouseenter",function(){return i._handleMouseEnter()}),n&2&&(Ie("role",i.role)("tabindex",i._getTabIndex())("aria-disabled",i.disabled)("disabled",i.disabled||null),Oe("mat-mdc-menu-item-highlighted",i._highlighted)("mat-mdc-menu-item-submenu-trigger",i._triggersSubmenu))},inputs:{role:"role",disabled:[2,"disabled","disabled",Be],disableRipple:[2,"disableRipple","disableRipple",Be]},exportAs:["matMenuItem"],features:[Kt],attrs:Jr,ngContentSelectors:to,decls:5,vars:3,consts:[[1,"mat-mdc-menu-item-text"],["matRipple","",1,"mat-mdc-menu-ripple",3,"matRippleDisabled","matRippleTrigger"],["viewBox","0 0 5 10","focusable","false","aria-hidden","true",1,"mat-mdc-menu-submenu-icon"],["points","0,0 5,5 0,10"]],template:function(n,i){n&1&&(Le(eo),ce(0),S(1,"span",0),ce(2,1),p(),Y(3,"div",1),Ne(4,no,2,0,":svg:svg",2)),n&2&&(q(3),V("matRippleDisabled",i.disableRipple||i.disabled)("matRippleTrigger",i._getHostElement()),q(),Qn(i._triggersSubmenu?4:-1))},dependencies:[hi],encapsulation:2,changeDetection:0})}return s})();var ro=new De("MatMenuContent");var oo=new De("mat-menu-default-options",{providedIn:"root",factory:ao});function ao(){return{overlapTrigger:!1,xPosition:"after",yPosition:"below",backdropClass:"cdk-overlay-transparent-backdrop"}}var bn="_mat-menu-enter",Nt="_mat-menu-exit",Ae=(()=>{class s{_elementRef=M(ve);_changeDetectorRef=M(rt);_injector=M(In);_keyManager;_xPosition;_yPosition;_firstItemFocusRef;_exitFallbackTimeout;_animationsDisabled;_allItems;_directDescendantItems=new Fn;_classList={};_panelAnimationState="void";_animationDone=new Ge;_isAnimating=!1;parentMenu;direction;overlayPanelClass;backdropClass;ariaLabel;ariaLabelledby;ariaDescribedby;get xPosition(){return this._xPosition}set xPosition(e){this._xPosition=e,this.setPositionClasses()}get yPosition(){return this._yPosition}set yPosition(e){this._yPosition=e,this.setPositionClasses()}templateRef;items;lazyContent;overlapTrigger;hasBackdrop;set panelClass(e){let n=this._previousPanelClass,i=re({},this._classList);n&&n.length&&n.split(" ").forEach(r=>{i[r]=!1}),this._previousPanelClass=e,e&&e.length&&(e.split(" ").forEach(r=>{i[r]=!0}),this._elementRef.nativeElement.className=""),this._classList=i}_previousPanelClass;get classList(){return this.panelClass}set classList(e){this.panelClass=e}closed=new Je;close=this.closed;panelId=M(ui).getId("mat-menu-panel-");constructor(){let e=M(oo);this.overlayPanelClass=e.overlayPanelClass||"",this._xPosition=e.xPosition,this._yPosition=e.yPosition,this.backdropClass=e.backdropClass,this.overlapTrigger=e.overlapTrigger,this.hasBackdrop=e.hasBackdrop,this._animationsDisabled=M(ke,{optional:!0})==="NoopAnimations"}ngOnInit(){this.setPositionClasses()}ngAfterContentInit(){this._updateDirectDescendants(),this._keyManager=new ai(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd(),this._keyManager.tabOut.subscribe(()=>this.closed.emit("tab")),this._directDescendantItems.changes.pipe(Ze(this._directDescendantItems),zt(e=>Xe(...e.map(n=>n._focused)))).subscribe(e=>this._keyManager.updateActiveItem(e)),this._directDescendantItems.changes.subscribe(e=>{let n=this._keyManager;if(this._panelAnimationState==="enter"&&n.activeItem?._hasFocus()){let i=e.toArray(),r=Math.max(0,Math.min(i.length-1,n.activeItemIndex||0));i[r]&&!i[r].disabled?n.setActiveItem(r):n.setNextItemActive()}})}ngOnDestroy(){this._keyManager?.destroy(),this._directDescendantItems.destroy(),this.closed.complete(),this._firstItemFocusRef?.destroy(),clearTimeout(this._exitFallbackTimeout)}_hovered(){return this._directDescendantItems.changes.pipe(Ze(this._directDescendantItems),zt(n=>Xe(...n.map(i=>i._hovered))))}addItem(e){}removeItem(e){}_handleKeydown(e){let n=e.keyCode,i=this._keyManager;switch(n){case 27:oi(e)||(e.preventDefault(),this.closed.emit("keydown"));break;case 37:this.parentMenu&&this.direction==="ltr"&&this.closed.emit("keydown");break;case 39:this.parentMenu&&this.direction==="rtl"&&this.closed.emit("keydown");break;default:(n===38||n===40)&&i.setFocusOrigin("keyboard"),i.onKeydown(e);return}}focusFirstItem(e="program"){this._firstItemFocusRef?.destroy(),this._firstItemFocusRef=xn(()=>{let n=this._resolvePanel();if(!n||!n.contains(document.activeElement)){let i=this._keyManager;i.setFocusOrigin(e).setFirstItemActive(),!i.activeItem&&n&&n.focus()}},{injector:this._injector})}resetActiveItem(){this._keyManager.setActiveItem(-1)}setElevation(e){}setPositionClasses(e=this.xPosition,n=this.yPosition){this._classList=Mn(re({},this._classList),{"mat-menu-before":e==="before","mat-menu-after":e==="after","mat-menu-above":n==="above","mat-menu-below":n==="below"}),this._changeDetectorRef.markForCheck()}_onAnimationDone(e){let n=e===Nt;(n||e===bn)&&(n&&(clearTimeout(this._exitFallbackTimeout),this._exitFallbackTimeout=void 0),this._animationDone.next(n?"void":"enter"),this._isAnimating=!1)}_onAnimationStart(e){(e===bn||e===Nt)&&(this._isAnimating=!0)}_setIsOpen(e){if(this._panelAnimationState=e?"enter":"void",e){if(this._keyManager.activeItemIndex===0){let n=this._resolvePanel();n&&(n.scrollTop=0)}}else this._animationsDisabled||(this._exitFallbackTimeout=setTimeout(()=>this._onAnimationDone(Nt),200));this._animationsDisabled&&setTimeout(()=>{this._onAnimationDone(e?bn:Nt)}),this._changeDetectorRef.markForCheck()}_updateDirectDescendants(){this._allItems.changes.pipe(Ze(this._allItems)).subscribe(e=>{this._directDescendantItems.reset(e.filter(n=>n._parentMenu===this)),this._directDescendantItems.notifyOnChanges()})}_resolvePanel(){let e=null;return this._directDescendantItems.length&&(e=this._directDescendantItems.first._getHostElement().closest('[role="menu"]')),e}static \u0275fac=function(n){return new(n||s)};static \u0275cmp=L({type:s,selectors:[["mat-menu"]],contentQueries:function(n,i,r){if(n&1&&(Ee(r,ro,5),Ee(r,$e,5),Ee(r,$e,4)),n&2){let o;ue(o=me())&&(i.lazyContent=o.first),ue(o=me())&&(i._allItems=o),ue(o=me())&&(i.items=o)}},viewQuery:function(n,i){if(n&1&&Vn(Ln,5),n&2){let r;ue(r=me())&&(i.templateRef=r.first)}},hostVars:3,hostBindings:function(n,i){n&2&&Ie("aria-label",null)("aria-labelledby",null)("aria-describedby",null)},inputs:{backdropClass:"backdropClass",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],xPosition:"xPosition",yPosition:"yPosition",overlapTrigger:[2,"overlapTrigger","overlapTrigger",Be],hasBackdrop:[2,"hasBackdrop","hasBackdrop",e=>e==null?null:Be(e)],panelClass:[0,"class","panelClass"],classList:"classList"},outputs:{closed:"closed",close:"close"},exportAs:["matMenu"],features:[Un([{provide:vn,useExisting:s}]),Kt],ngContentSelectors:io,decls:1,vars:0,consts:[["tabindex","-1","role","menu",1,"mat-mdc-menu-panel",3,"click","animationstart","animationend","animationcancel","id"],[1,"mat-mdc-menu-content"]],template:function(n,i){n&1&&(Le(),Ne(0,so,3,12,"ng-template"))},styles:['mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:"";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}'],encapsulation:2,changeDetection:0})}return s})(),ls=new De("mat-menu-scroll-strategy",{providedIn:"root",factory:()=>{let s=M(ct);return()=>s.scrollStrategies.reposition()}});function lo(s){return()=>s.scrollStrategies.reposition()}var co={provide:ls,deps:[ct],useFactory:lo},uo={passive:!0};var He=new WeakMap,cs=(()=>{class s{_overlay=M(ct);_element=M(ve);_viewContainerRef=M(qn);_menuItemInstance=M($e,{optional:!0,self:!0});_dir=M(mi,{optional:!0});_focusMonitor=M(qt);_ngZone=M(et);_scrollStrategy=M(ls);_changeDetectorRef=M(rt);_cleanupTouchstart;_portal;_overlayRef=null;_menuOpen=!1;_closingActionsSubscription=Ye.EMPTY;_hoverSubscription=Ye.EMPTY;_menuCloseSubscription=Ye.EMPTY;_pendingRemoval;_parentMaterialMenu;_parentInnerPadding;_openedBy=void 0;get _deprecatedMatMenuTriggerFor(){return this.menu}set _deprecatedMatMenuTriggerFor(e){this.menu=e}get menu(){return this._menu}set menu(e){e!==this._menu&&(this._menu=e,this._menuCloseSubscription.unsubscribe(),e&&(this._parentMaterialMenu,this._menuCloseSubscription=e.close.subscribe(n=>{this._destroyMenu(n),(n==="click"||n==="tab")&&this._parentMaterialMenu&&this._parentMaterialMenu.closed.emit(n)})),this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu()))}_menu;menuData;restoreFocus=!0;menuOpened=new Je;onMenuOpen=this.menuOpened;menuClosed=new Je;onMenuClose=this.menuClosed;constructor(){let e=M(vn,{optional:!0}),n=M(zn);this._parentMaterialMenu=e instanceof Ae?e:void 0,this._cleanupTouchstart=si(n,this._element.nativeElement,"touchstart",i=>{ci(i)||(this._openedBy="touch")},uo)}ngAfterContentInit(){this._handleHover()}ngOnDestroy(){this.menu&&this._ownsMenu(this.menu)&&He.delete(this.menu),this._cleanupTouchstart(),this._pendingRemoval?.unsubscribe(),this._menuCloseSubscription.unsubscribe(),this._closingActionsSubscription.unsubscribe(),this._hoverSubscription.unsubscribe(),this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=null)}get menuOpen(){return this._menuOpen}get dir(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}triggersSubmenu(){return!!(this._menuItemInstance&&this._parentMaterialMenu&&this.menu)}toggleMenu(){return this._menuOpen?this.closeMenu():this.openMenu()}openMenu(){let e=this.menu;if(this._menuOpen||!e)return;this._pendingRemoval?.unsubscribe();let n=He.get(e);He.set(e,this),n&&n!==this&&n.closeMenu();let i=this._createOverlay(e),r=i.getConfig(),o=r.positionStrategy;this._setPosition(e,o),r.hasBackdrop=e.hasBackdrop==null?!this.triggersSubmenu():e.hasBackdrop,i.hasAttached()||(i.attach(this._getPortal(e)),e.lazyContent?.attach(this.menuData)),this._closingActionsSubscription=this._menuClosingActions().subscribe(()=>this.closeMenu()),e.parentMenu=this.triggersSubmenu()?this._parentMaterialMenu:void 0,e.direction=this.dir,e.focusFirstItem(this._openedBy||"program"),this._setIsMenuOpen(!0),e instanceof Ae&&(e._setIsOpen(!0),e._directDescendantItems.changes.pipe(Rn(e.close)).subscribe(()=>{o.withLockedPosition(!1).reapplyLastPosition(),o.withLockedPosition(!0)}))}closeMenu(){this.menu?.close.emit()}focus(e,n){this._focusMonitor&&e?this._focusMonitor.focusVia(this._element,e,n):this._element.nativeElement.focus(n)}updatePosition(){this._overlayRef?.updatePosition()}_destroyMenu(e){let n=this._overlayRef,i=this._menu;!n||!this.menuOpen||(this._closingActionsSubscription.unsubscribe(),this._pendingRemoval?.unsubscribe(),i instanceof Ae&&this._ownsMenu(i)?(this._pendingRemoval=i._animationDone.pipe(kn(1)).subscribe(()=>{n.detach(),i.lazyContent?.detach()}),i._setIsOpen(!1)):(n.detach(),i?.lazyContent?.detach()),i&&this._ownsMenu(i)&&He.delete(i),this.restoreFocus&&(e==="keydown"||!this._openedBy||!this.triggersSubmenu())&&this.focus(this._openedBy),this._openedBy=void 0,this._setIsMenuOpen(!1))}_setIsMenuOpen(e){e!==this._menuOpen&&(this._menuOpen=e,this._menuOpen?this.menuOpened.emit():this.menuClosed.emit(),this.triggersSubmenu()&&this._menuItemInstance._setHighlighted(e),this._changeDetectorRef.markForCheck())}_createOverlay(e){if(!this._overlayRef){let n=this._getOverlayConfig(e);this._subscribeToPositions(e,n.positionStrategy),this._overlayRef=this._overlay.create(n),this._overlayRef.keydownEvents().subscribe(i=>{this.menu instanceof Ae&&this.menu._handleKeydown(i)})}return this._overlayRef}_getOverlayConfig(e){return new Mi({positionStrategy:this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn(".mat-menu-panel, .mat-mdc-menu-panel"),backdropClass:e.backdropClass||"cdk-overlay-transparent-backdrop",panelClass:e.overlayPanelClass,scrollStrategy:this._scrollStrategy(),direction:this._dir||"ltr"})}_subscribeToPositions(e,n){e.setPositionClasses&&n.positionChanges.subscribe(i=>{this._ngZone.run(()=>{let r=i.connectionPair.overlayX==="start"?"after":"before",o=i.connectionPair.overlayY==="top"?"below":"above";e.setPositionClasses(r,o)})})}_setPosition(e,n){let[i,r]=e.xPosition==="before"?["end","start"]:["start","end"],[o,a]=e.yPosition==="above"?["bottom","top"]:["top","bottom"],[l,c]=[o,a],[m,u]=[i,r],v=0;if(this.triggersSubmenu()){if(u=i=e.xPosition==="before"?"start":"end",r=m=i==="end"?"start":"end",this._parentMaterialMenu){if(this._parentInnerPadding==null){let _=this._parentMaterialMenu.items.first;this._parentInnerPadding=_?_._getHostElement().offsetTop:0}v=o==="bottom"?this._parentInnerPadding:-this._parentInnerPadding}}else e.overlapTrigger||(l=o==="top"?"bottom":"top",c=a==="top"?"bottom":"top");n.withPositions([{originX:i,originY:l,overlayX:m,overlayY:o,offsetY:v},{originX:r,originY:l,overlayX:u,overlayY:o,offsetY:v},{originX:i,originY:c,overlayX:m,overlayY:a,offsetY:-v},{originX:r,originY:c,overlayX:u,overlayY:a,offsetY:-v}])}_menuClosingActions(){let e=this._overlayRef.backdropClick(),n=this._overlayRef.detachments(),i=this._parentMaterialMenu?this._parentMaterialMenu.closed:Bt(),r=this._parentMaterialMenu?this._parentMaterialMenu._hovered().pipe(Pn(o=>this._menuOpen&&o!==this._menuItemInstance)):Bt();return Xe(e,i,r,n)}_handleMousedown(e){li(e)||(this._openedBy=e.button===0?"mouse":void 0,this.triggersSubmenu()&&e.preventDefault())}_handleKeydown(e){let n=e.keyCode;(n===13||n===32)&&(this._openedBy="keyboard"),this.triggersSubmenu()&&(n===39&&this.dir==="ltr"||n===37&&this.dir==="rtl")&&(this._openedBy="keyboard",this.openMenu())}_handleClick(e){this.triggersSubmenu()?(e.stopPropagation(),this.openMenu()):this.toggleMenu()}_handleHover(){this.triggersSubmenu()&&this._parentMaterialMenu&&(this._hoverSubscription=this._parentMaterialMenu._hovered().subscribe(e=>{e===this._menuItemInstance&&!e.disabled&&(this._openedBy="mouse",this.openMenu())}))}_getPortal(e){return(!this._portal||this._portal.templateRef!==e.templateRef)&&(this._portal=new Ti(e.templateRef,this._viewContainerRef)),this._portal}_ownsMenu(e){return He.get(e)===this}static \u0275fac=function(n){return new(n||s)};static \u0275dir=tt({type:s,selectors:[["","mat-menu-trigger-for",""],["","matMenuTriggerFor",""]],hostAttrs:[1,"mat-mdc-menu-trigger"],hostVars:3,hostBindings:function(n,i){n&1&&Se("click",function(o){return i._handleClick(o)})("mousedown",function(o){return i._handleMousedown(o)})("keydown",function(o){return i._handleKeydown(o)}),n&2&&Ie("aria-haspopup",i.menu?"menu":null)("aria-expanded",i.menuOpen)("aria-controls",i.menuOpen?i.menu.panelId:null)},inputs:{_deprecatedMatMenuTriggerFor:[0,"mat-menu-trigger-for","_deprecatedMatMenuTriggerFor"],menu:[0,"matMenuTriggerFor","menu"],menuData:[0,"matMenuTriggerData","menuData"],restoreFocus:[0,"matMenuTriggerRestoreFocus","restoreFocus"]},outputs:{menuOpened:"menuOpened",onMenuOpen:"onMenuOpen",menuClosed:"menuClosed",onMenuClose:"onMenuClose"},exportAs:["matMenuTrigger"]})}return s})(),us=(()=>{class s{static \u0275fac=function(n){return new(n||s)};static \u0275mod=Re({type:s});static \u0275inj=Pe({providers:[co],imports:[fi,Te,Ci,wi,Te]})}return s})(),ms={transformMenu:{type:7,name:"transformMenu",definitions:[{type:0,name:"void",styles:{type:6,styles:{opacity:0,transform:"scale(0.8)"},offset:null}},{type:1,expr:"void => enter",animation:{type:4,styles:{type:6,styles:{opacity:1,transform:"scale(1)"},offset:null},timings:"120ms cubic-bezier(0, 0, 0.2, 1)"},options:null},{type:1,expr:"* => void",animation:{type:4,styles:{type:6,styles:{opacity:0},offset:null},timings:"100ms 25ms linear"},options:null}],options:{}},fadeInItems:{type:7,name:"fadeInItems",definitions:[{type:0,name:"showing",styles:{type:6,styles:{opacity:1},offset:null}},{type:1,expr:"void => *",animation:[{type:6,styles:{opacity:0},offset:null},{type:4,styles:null,timings:"400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)"}],options:null}],options:{}}},Oa=ms.fadeInItems,Fa=ms.transformMenu;function ho(s,t){if(s&1&&(Fe(0),S(1,"button",10)(2,"mat-icon"),y(3,"business"),p(),y(4," Provider "),S(5,"mat-icon"),y(6,"arrow_drop_down"),p()(),S(7,"mat-menu",null,0)(9,"button",11)(10,"mat-icon"),y(11,"dashboard"),p(),y(12," Dashboard "),p(),S(13,"button",12)(14,"mat-icon"),y(15,"timeline"),p(),y(16," My Lines "),p(),S(17,"button",13)(18,"mat-icon"),y(19,"place"),p(),y(20," My Points "),p(),S(21,"button",14)(22,"mat-icon"),y(23,"data_usage"),p(),y(24," Capacity Management "),p()(),xe()),s&2){let e=st(8);q(),V("matMenuTriggerFor",e)}}function fo(s,t){if(s&1&&(Fe(0),S(1,"button",10)(2,"mat-icon"),y(3,"admin_panel_settings"),p(),y(4," Admin "),S(5,"mat-icon"),y(6,"arrow_drop_down"),p()(),S(7,"mat-menu",null,1)(9,"button",15)(10,"mat-icon"),y(11,"dashboard"),p(),y(12," Dashboard "),p(),S(13,"button",16)(14,"mat-icon"),y(15,"people"),p(),y(16," Users "),p(),S(17,"button",17)(18,"mat-icon"),y(19,"business"),p(),y(20," Providers "),p()(),xe()),s&2){let e=st(8);q(),V("matMenuTriggerFor",e)}}function po(s,t){if(s&1){let e=it();Fe(0),S(1,"button",10)(2,"mat-icon"),y(3,"account_circle"),p(),y(4),S(5,"mat-icon"),y(6,"arrow_drop_down"),p()(),S(7,"mat-menu",null,2)(9,"button",18)(10,"mat-icon"),y(11,"person"),p(),y(12," Profile "),p(),S(13,"button",19),Se("click",function(){ae(e);let i=G();return le(i.logout())}),S(14,"mat-icon"),y(15,"exit_to_app"),p(),y(16," Logout "),p()(),xe()}if(s&2){let e=st(8),n=G();q(),V("matMenuTriggerFor",e),q(3),jn(" ",n.currentUser==null?null:n.currentUser.username," ")}}function go(s,t){s&1&&(Fe(0),S(1,"button",20)(2,"mat-icon"),y(3,"login"),p(),y(4," Login "),p(),S(5,"button",21)(6,"mat-icon"),y(7,"person_add"),p(),y(8," Register "),p(),xe())}var It=class s{constructor(t){this.authService=t}isAuthenticated=!1;currentUser=null;ngOnInit(){this.authService.isAuthenticated$.subscribe(t=>{this.isAuthenticated=t}),this.authService.currentUser$.subscribe(t=>{this.currentUser=t})}logout(){this.authService.logout()}static \u0275fac=function(e){return new(e||s)(Kn(ut))};static \u0275cmp=L({type:s,selectors:[["app-header"]],decls:20,vars:4,consts:[["providerMenu","matMenu"],["adminMenu","matMenu"],["userMenu","matMenu"],["color","primary"],["routerLink","/",1,"app-title"],[1,"spacer"],["mat-button","","routerLink","/map"],["mat-button","","routerLink","/lines"],["mat-button","","routerLink","/points"],[4,"ngIf"],["mat-button","",3,"matMenuTriggerFor"],["mat-menu-item","","routerLink","/provider"],["mat-menu-item","","routerLink","/provider/lines"],["mat-menu-item","","routerLink","/provider/points"],["mat-menu-item","","routerLink","/provider/capacity"],["mat-menu-item","","routerLink","/admin"],["mat-menu-item","","routerLink","/admin/users"],["mat-menu-item","","routerLink","/admin/providers"],["mat-menu-item","","routerLink","/auth/profile"],["mat-menu-item","",3,"click"],["mat-button","","routerLink","/auth/login"],["mat-button","","routerLink","/auth/register"]],template:function(e,n){e&1&&(S(0,"mat-toolbar",3)(1,"span",4),y(2,"Optical Lines Database"),p(),Y(3,"span",5),S(4,"button",6)(5,"mat-icon"),y(6,"map"),p(),y(7," Map "),p(),S(8,"button",7)(9,"mat-icon"),y(10,"timeline"),p(),y(11," Lines "),p(),S(12,"button",8)(13,"mat-icon"),y(14,"place"),p(),y(15," Points "),p(),Ne(16,ho,25,1,"ng-container",9)(17,fo,21,1,"ng-container",9)(18,po,17,2,"ng-container",9)(19,go,9,0,"ng-container",9),p()),e&2&&(q(16),V("ngIf",n.isAuthenticated&&(n.currentUser==null?null:n.currentUser.role)==="provider"),q(),V("ngIf",n.isAuthenticated&&(n.currentUser==null?null:n.currentUser.role)==="admin"),q(),V("ngIf",n.isAuthenticated),q(),V("ngIf",!n.isAuthenticated))},dependencies:[Wn,$n,Rt,kt,lt,at,Ei,Si,us,Ae,$e,cs,ot],styles:[".spacer[_ngcontent-%COMP%]{flex:1 1 auto}mat-toolbar[_ngcontent-%COMP%]{display:flex;align-items:center}.app-title[_ngcontent-%COMP%]{cursor:pointer;font-weight:500;letter-spacing:.5px}button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:4px}"]})};var Ot=class s{static \u0275fac=function(e){return new(e||s)};static \u0275cmp=L({type:s,selectors:[["app-footer"]],decls:3,vars:0,consts:[["color","primary",1,"footer"]],template:function(e,n){e&1&&(S(0,"mat-toolbar",0)(1,"span"),y(2,"\xA9 2024 Optical Lines Database"),p()())},dependencies:[Rt,kt],styles:[".footer[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;height:40px;display:flex;justify-content:center;font-size:14px}"]})};var Ft=class s{title="Optical Lines Database";static \u0275fac=function(e){return new(e||s)};static \u0275cmp=L({type:s,selectors:[["app-root"]],decls:5,vars:0,consts:[[1,"app-container"],[1,"main-content"]],template:function(e,n){e&1&&(S(0,"div",0),Y(1,"app-header"),S(2,"main",1),Y(3,"router-outlet"),p(),Y(4,"app-footer"),p())},dependencies:[ei,It,Ot],styles:[".app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh}.main-content[_ngcontent-%COMP%]{flex:1;padding-bottom:40px}"]})};Jn(Ft,ss).catch(s=>console.error(s));
