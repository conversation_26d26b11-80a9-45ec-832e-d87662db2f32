import{$ as f,$a as $n,A as Z,Aa as Cn,Ac as ei,Ba as nr,Bc as ke,C as Jt,<PERSON>a as An,Cc as vt,D as he,Da as fe,Db as Hn,F as se,Fa as ir,G as Q,Ga as Mn,H as Zt,I as gn,Ia as In,Ja as Ue,Jb as Wn,K as mn,Ka as pe,Kc as ti,<PERSON> as ge,Ma as Dn,Mc as dr,Na as On,Nc as ri,O as vn,Oa as Pn,Oc as yt,P as _,Pa as Nn,Q as yn,Qa as Un,Ra as xn,S as A,Sa as _n,T as E,Ta as ae,Ua as kn,V as w,W as dt,Wa as xe,X as wn,Y as C,Ya as Ln,Z as Qt,Za as jn,_ as v,_a as me,a as un,ab as Fn,b as ln,c as dn,ca as De,d as Wt,da as Rn,db as zn,e as Kt,ea as oe,eb as sr,f as J,fa as F,fb as Bn,g as P,ga as er,gb as ht,hb as or,hc as gt,i as W,j as N,jc as ur,k as p,kb as Vn,l as $,la as tr,lc as Kn,m as hn,ma as Sn,mb as ar,mc as mt,n as fn,na as Oe,nb as qn,o as y,ob as cr,p as lt,pa as bn,pb as ft,q as x,qa as Pe,qb as pt,r as Xt,ra as Ne,rb as Gn,s as pn,sa as ee,sc as Xn,t as Yt,ta as rr,tc as lr,ua as En,uc as Yn,va as Tn,vc as Jn,wc as U,x as K,xc as Zn,yc as _e,z as V,zc as Qn}from"./chunk-BS5MTC5G.js";import{a as d,b as O}from"./chunk-C6Q5SG76.js";var je=class{},Rt=class{},ce=class t{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(r){r?typeof r=="string"?this.lazyInit=()=>{this.headers=new Map,r.split(`
`).forEach(e=>{let n=e.indexOf(":");if(n>0){let i=e.slice(0,n),s=e.slice(n+1).trim();this.addHeaderEntry(i,s)}})}:typeof Headers<"u"&&r instanceof Headers?(this.headers=new Map,r.forEach((e,n)=>{this.addHeaderEntry(n,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(r).forEach(([e,n])=>{this.setHeaderEntries(e,n)})}:this.headers=new Map}has(r){return this.init(),this.headers.has(r.toLowerCase())}get(r){this.init();let e=this.headers.get(r.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(r){return this.init(),this.headers.get(r.toLowerCase())||null}append(r,e){return this.clone({name:r,value:e,op:"a"})}set(r,e){return this.clone({name:r,value:e,op:"s"})}delete(r,e){return this.clone({name:r,value:e,op:"d"})}maybeSetNormalizedName(r,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,r)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(r=>this.applyUpdate(r)),this.lazyUpdate=null))}copyFrom(r){r.init(),Array.from(r.headers.keys()).forEach(e=>{this.headers.set(e,r.headers.get(e)),this.normalizedNames.set(e,r.normalizedNames.get(e))})}clone(r){let e=new t;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([r]),e}applyUpdate(r){let e=r.name.toLowerCase();switch(r.op){case"a":case"s":let n=r.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(r.name,e);let i=(r.op==="a"?this.headers.get(e):void 0)||[];i.push(...n),this.headers.set(e,i);break;case"d":let s=r.value;if(!s)this.headers.delete(e),this.normalizedNames.delete(e);else{let o=this.headers.get(e);if(!o)return;o=o.filter(c=>s.indexOf(c)===-1),o.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,o)}break}}addHeaderEntry(r,e){let n=r.toLowerCase();this.maybeSetNormalizedName(r,n),this.headers.has(n)?this.headers.get(n).push(e):this.headers.set(n,[e])}setHeaderEntries(r,e){let n=(Array.isArray(e)?e:[e]).map(s=>s.toString()),i=r.toLowerCase();this.headers.set(i,n),this.maybeSetNormalizedName(r,i)}forEach(r){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>r(this.normalizedNames.get(e),this.headers.get(e)))}};var fr=class{encodeKey(r){return ni(r)}encodeValue(r){return ni(r)}decodeKey(r){return decodeURIComponent(r)}decodeValue(r){return decodeURIComponent(r)}};function Ms(t,r){let e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(i=>{let s=i.indexOf("="),[o,c]=s==-1?[r.decodeKey(i),""]:[r.decodeKey(i.slice(0,s)),r.decodeValue(i.slice(s+1))],a=e.get(o)||[];a.push(c),e.set(o,a)}),e}var Is=/%(\d[a-f0-9])/gi,Ds={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ni(t){return encodeURIComponent(t).replace(Is,(r,e)=>Ds[e]??r)}function wt(t){return`${t}`}var te=class t{map;encoder;updates=null;cloneFrom=null;constructor(r={}){if(this.encoder=r.encoder||new fr,r.fromString){if(r.fromObject)throw new E(2805,!1);this.map=Ms(r.fromString,this.encoder)}else r.fromObject?(this.map=new Map,Object.keys(r.fromObject).forEach(e=>{let n=r.fromObject[e],i=Array.isArray(n)?n.map(wt):[wt(n)];this.map.set(e,i)})):this.map=null}has(r){return this.init(),this.map.has(r)}get(r){this.init();let e=this.map.get(r);return e?e[0]:null}getAll(r){return this.init(),this.map.get(r)||null}keys(){return this.init(),Array.from(this.map.keys())}append(r,e){return this.clone({param:r,value:e,op:"a"})}appendAll(r){let e=[];return Object.keys(r).forEach(n=>{let i=r[n];Array.isArray(i)?i.forEach(s=>{e.push({param:n,value:s,op:"a"})}):e.push({param:n,value:i,op:"a"})}),this.clone(e)}set(r,e){return this.clone({param:r,value:e,op:"s"})}delete(r,e){return this.clone({param:r,value:e,op:"d"})}toString(){return this.init(),this.keys().map(r=>{let e=this.encoder.encodeKey(r);return this.map.get(r).map(n=>e+"="+this.encoder.encodeValue(n)).join("&")}).filter(r=>r!=="").join("&")}clone(r){let e=new t({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(r),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(r=>this.map.set(r,this.cloneFrom.map.get(r))),this.updates.forEach(r=>{switch(r.op){case"a":case"s":let e=(r.op==="a"?this.map.get(r.param):void 0)||[];e.push(wt(r.value)),this.map.set(r.param,e);break;case"d":if(r.value!==void 0){let n=this.map.get(r.param)||[],i=n.indexOf(wt(r.value));i!==-1&&n.splice(i,1),n.length>0?this.map.set(r.param,n):this.map.delete(r.param)}else{this.map.delete(r.param);break}}}),this.cloneFrom=this.updates=null)}};var pr=class{map=new Map;set(r,e){return this.map.set(r,e),this}get(r){return this.map.has(r)||this.map.set(r,r.defaultValue()),this.map.get(r)}delete(r){return this.map.delete(r),this}has(r){return this.map.has(r)}keys(){return this.map.keys()}};function Os(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function ii(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function si(t){return typeof Blob<"u"&&t instanceof Blob}function oi(t){return typeof FormData<"u"&&t instanceof FormData}function Ps(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var ai="Content-Type",ci="Accept",hi="X-Request-URL",fi="text/plain",pi="application/json",Ns=`${pi}, ${fi}, */*`,Le=class t{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(r,e,n,i){this.url=e,this.method=r.toUpperCase();let s;if(Os(this.method)||i?(this.body=n!==void 0?n:null,s=i):s=n,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.context&&(this.context=s.context),s.params&&(this.params=s.params),this.transferCache=s.transferCache),this.headers??=new ce,this.context??=new pr,!this.params)this.params=new te,this.urlWithParams=e;else{let o=this.params.toString();if(o.length===0)this.urlWithParams=e;else{let c=e.indexOf("?"),a=c===-1?"?":c<e.length-1?"&":"";this.urlWithParams=e+a+o}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||ii(this.body)||si(this.body)||oi(this.body)||Ps(this.body)?this.body:this.body instanceof te?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||oi(this.body)?null:si(this.body)?this.body.type||null:ii(this.body)?null:typeof this.body=="string"?fi:this.body instanceof te?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?pi:null}clone(r={}){let e=r.method||this.method,n=r.url||this.url,i=r.responseType||this.responseType,s=r.transferCache??this.transferCache,o=r.body!==void 0?r.body:this.body,c=r.withCredentials??this.withCredentials,a=r.reportProgress??this.reportProgress,u=r.headers||this.headers,l=r.params||this.params,h=r.context??this.context;return r.setHeaders!==void 0&&(u=Object.keys(r.setHeaders).reduce((g,T)=>g.set(T,r.setHeaders[T]),u)),r.setParams&&(l=Object.keys(r.setParams).reduce((g,T)=>g.set(T,r.setParams[T]),l)),new t(e,n,o,{params:l,headers:u,context:h,reportProgress:a,responseType:i,withCredentials:c,transferCache:s})}},ve=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(ve||{}),$e=class{headers;status;statusText;url;ok;type;constructor(r,e=200,n="OK"){this.headers=r.headers||new ce,this.status=r.status!==void 0?r.status:e,this.statusText=r.statusText||n,this.url=r.url||null,this.ok=this.status>=200&&this.status<300}},gr=class t extends $e{constructor(r={}){super(r)}type=ve.ResponseHeader;clone(r={}){return new t({headers:r.headers||this.headers,status:r.status!==void 0?r.status:this.status,statusText:r.statusText||this.statusText,url:r.url||this.url||void 0})}},St=class t extends $e{body;constructor(r={}){super(r),this.body=r.body!==void 0?r.body:null}type=ve.Response;clone(r={}){return new t({body:r.body!==void 0?r.body:this.body,headers:r.headers||this.headers,status:r.status!==void 0?r.status:this.status,statusText:r.statusText||this.statusText,url:r.url||this.url||void 0})}},bt=class extends $e{name="HttpErrorResponse";message;error;ok=!1;constructor(r){super(r,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${r.url||"(unknown url)"}`:this.message=`Http failure response for ${r.url||"(unknown url)"}: ${r.status} ${r.statusText}`,this.error=r.error||null}},Us=200,xs=204;function hr(t,r){return{body:r,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var vr=(()=>{class t{handler;constructor(e){this.handler=e}request(e,n,i={}){let s;if(e instanceof Le)s=e;else{let a;i.headers instanceof ce?a=i.headers:a=new ce(i.headers);let u;i.params&&(i.params instanceof te?u=i.params:u=new te({fromObject:i.params})),s=new Le(e,n,i.body!==void 0?i.body:null,{headers:a,context:i.context,params:u,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let o=p(s).pipe(Z(a=>this.handler.handle(a)));if(e instanceof Le||i.observe==="events")return o;let c=o.pipe(K(a=>a instanceof St));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return c.pipe(y(a=>{if(a.body!==null&&!(a.body instanceof ArrayBuffer))throw new E(2806,!1);return a.body}));case"blob":return c.pipe(y(a=>{if(a.body!==null&&!(a.body instanceof Blob))throw new E(2807,!1);return a.body}));case"text":return c.pipe(y(a=>{if(a.body!==null&&typeof a.body!="string")throw new E(2808,!1);return a.body}));case"json":default:return c.pipe(y(a=>a.body))}case"response":return c;default:throw new E(2809,!1)}}delete(e,n={}){return this.request("DELETE",e,n)}get(e,n={}){return this.request("GET",e,n)}head(e,n={}){return this.request("HEAD",e,n)}jsonp(e,n){return this.request("JSONP",e,{params:new te().append(n,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,n={}){return this.request("OPTIONS",e,n)}patch(e,n,i={}){return this.request("PATCH",e,hr(i,n))}post(e,n,i={}){return this.request("POST",e,hr(i,n))}put(e,n,i={}){return this.request("PUT",e,hr(i,n))}static \u0275fac=function(n){return new(n||t)(v(je))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();var _s=new C("");function gi(t,r){return r(t)}function ks(t,r){return(e,n)=>r.intercept(e,{handle:i=>t(i,n)})}function Ls(t,r,e){return(n,i)=>F(e,()=>r(n,s=>t(s,i)))}var js=new C(""),yr=new C(""),$s=new C(""),mi=new C("",{providedIn:"root",factory:()=>!0});function Fs(){let t=null;return(r,e)=>{t===null&&(t=(f(js,{optional:!0})??[]).reduceRight(ks,gi));let n=f(Pe);if(f(mi)){let s=n.add();return t(r,e).pipe(se(()=>n.remove(s)))}else return t(r,e)}}var ui=(()=>{class t extends je{backend;injector;chain=null;pendingTasks=f(Pe);contributeToStability=f(mi);constructor(e,n){super(),this.backend=e,this.injector=n}handle(e){if(this.chain===null){let n=Array.from(new Set([...this.injector.get(yr),...this.injector.get($s,[])]));this.chain=n.reduceRight((i,s)=>Ls(i,s,this.injector),gi)}if(this.contributeToStability){let n=this.pendingTasks.add();return this.chain(e,i=>this.backend.handle(i)).pipe(se(()=>this.pendingTasks.remove(n)))}else return this.chain(e,n=>this.backend.handle(n))}static \u0275fac=function(n){return new(n||t)(v(Rt),v(oe))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();var zs=/^\)\]\}',?\n/,Bs=RegExp(`^${hi}:`,"m");function Vs(t){return"responseURL"in t&&t.responseURL?t.responseURL:Bs.test(t.getAllResponseHeaders())?t.getResponseHeader(hi):null}var li=(()=>{class t{xhrFactory;constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new E(-2800,!1);let n=this.xhrFactory;return(n.\u0275loadImpl?N(n.\u0275loadImpl()):p(null)).pipe(_(()=>new dn(s=>{let o=n.build();if(o.open(e.method,e.urlWithParams),e.withCredentials&&(o.withCredentials=!0),e.headers.forEach((S,R)=>o.setRequestHeader(S,R.join(","))),e.headers.has(ci)||o.setRequestHeader(ci,Ns),!e.headers.has(ai)){let S=e.detectContentTypeHeader();S!==null&&o.setRequestHeader(ai,S)}if(e.responseType){let S=e.responseType.toLowerCase();o.responseType=S!=="json"?S:"text"}let c=e.serializeBody(),a=null,u=()=>{if(a!==null)return a;let S=o.statusText||"OK",R=new ce(o.getAllResponseHeaders()),j=Vs(o)||e.url;return a=new gr({headers:R,status:o.status,statusText:S,url:j}),a},l=()=>{let{headers:S,status:R,statusText:j,url:ut}=u(),M=null;R!==xs&&(M=typeof o.response>"u"?o.responseText:o.response),R===0&&(R=M?Us:0);let Ht=R>=200&&R<300;if(e.responseType==="json"&&typeof M=="string"){let Es=M;M=M.replace(zs,"");try{M=M!==""?JSON.parse(M):null}catch(Ts){M=Es,Ht&&(Ht=!1,M={error:Ts,text:M})}}Ht?(s.next(new St({body:M,headers:S,status:R,statusText:j,url:ut||void 0})),s.complete()):s.error(new bt({error:M,headers:S,status:R,statusText:j,url:ut||void 0}))},h=S=>{let{url:R}=u(),j=new bt({error:S,status:o.status||0,statusText:o.statusText||"Unknown Error",url:R||void 0});s.error(j)},g=!1,T=S=>{g||(s.next(u()),g=!0);let R={type:ve.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(R.total=S.total),e.responseType==="text"&&o.responseText&&(R.partialText=o.responseText),s.next(R)},I=S=>{let R={type:ve.UploadProgress,loaded:S.loaded};S.lengthComputable&&(R.total=S.total),s.next(R)};return o.addEventListener("load",l),o.addEventListener("error",h),o.addEventListener("timeout",h),o.addEventListener("abort",h),e.reportProgress&&(o.addEventListener("progress",T),c!==null&&o.upload&&o.upload.addEventListener("progress",I)),o.send(c),s.next({type:ve.Sent}),()=>{o.removeEventListener("error",h),o.removeEventListener("abort",h),o.removeEventListener("load",l),o.removeEventListener("timeout",h),e.reportProgress&&(o.removeEventListener("progress",T),c!==null&&o.upload&&o.upload.removeEventListener("progress",I)),o.readyState!==o.DONE&&o.abort()}})))}static \u0275fac=function(n){return new(n||t)(v(yt))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),vi=new C(""),qs="XSRF-TOKEN",Gs=new C("",{providedIn:"root",factory:()=>qs}),Hs="X-XSRF-TOKEN",Ws=new C("",{providedIn:"root",factory:()=>Hs}),Et=class{},Ks=(()=>{class t{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(e,n,i){this.doc=e,this.platform=n,this.cookieName=i}getToken(){if(this.platform==="server")return null;let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=vt(e,this.cookieName),this.lastCookieString=e),this.lastToken}static \u0275fac=function(n){return new(n||t)(v(U),v(fe),v(Gs))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();function Xs(t,r){let e=t.url.toLowerCase();if(!f(vi)||t.method==="GET"||t.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return r(t);let n=f(Et).getToken(),i=f(Ws);return n!=null&&!t.headers.has(i)&&(t=t.clone({headers:t.headers.set(i,n)})),r(t)}var yi=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(yi||{});function Ys(t,r){return{\u0275kind:t,\u0275providers:r}}function _c(...t){let r=[vr,li,ui,{provide:je,useExisting:ui},{provide:Rt,useFactory:()=>f(_s,{optional:!0})??f(li)},{provide:yr,useValue:Xs,multi:!0},{provide:vi,useValue:!0},{provide:Et,useClass:Ks}];for(let e of t)r.push(...e.\u0275providers);return De(r)}var di=new C("");function kc(){return Ys(yi.LegacyInterceptors,[{provide:di,useFactory:Fs},{provide:yr,useExisting:di,multi:!0}])}var Rr=class extends Jn{supportsDOMEvents=!0},Sr=class t extends Rr{static makeCurrent(){Yn(new t)}onAndCancel(r,e,n,i){return r.addEventListener(e,n,i),()=>{r.removeEventListener(e,n,i)}}dispatchEvent(r,e){r.dispatchEvent(e)}remove(r){r.remove()}createElement(r,e){return e=e||this.getDefaultDocument(),e.createElement(r)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(r){return r.nodeType===Node.ELEMENT_NODE}isShadowRoot(r){return r instanceof DocumentFragment}getGlobalEventTarget(r,e){return e==="window"?window:e==="document"?r:e==="body"?r.body:null}getBaseHref(r){let e=eo();return e==null?null:to(e)}resetBaseElement(){Fe=null}getUserAgent(){return window.navigator.userAgent}getCookie(r){return vt(document.cookie,r)}},Fe=null;function eo(){return Fe=Fe||document.querySelector("base"),Fe?Fe.getAttribute("href"):null}function to(t){return new URL(t,document.baseURI).pathname}var ro=(()=>{class t{build(){return new XMLHttpRequest}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),br=new C(""),Ti=(()=>{class t{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,n){this._zone=n,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,n,i,s){return this._findPluginFor(n).addEventListener(e,n,i,s)}getZone(){return this._zone}_findPluginFor(e){let n=this._eventNameToPlugin.get(e);if(n)return n;if(n=this._plugins.find(s=>s.supports(e)),!n)throw new E(5101,!1);return this._eventNameToPlugin.set(e,n),n}static \u0275fac=function(n){return new(n||t)(v(br),v(ee))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),Ct=class{_doc;constructor(r){this._doc=r}manager},Tt="ng-app-id";function wi(t){for(let r of t)r.remove()}function Ri(t,r){let e=r.createElement("style");return e.textContent=t,e}function no(t,r,e,n){let i=t.head?.querySelectorAll(`style[${Tt}="${r}"],link[${Tt}="${r}"]`);if(i)for(let s of i)s.removeAttribute(Tt),s instanceof HTMLLinkElement?n.set(s.href.slice(s.href.lastIndexOf("/")+1),{usage:0,elements:[s]}):s.textContent&&e.set(s.textContent,{usage:0,elements:[s]})}function Er(t,r){let e=r.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",t),e}var Ci=(()=>{class t{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,n,i,s={}){this.doc=e,this.appId=n,this.nonce=i,this.isServer=dr(s),no(e,n,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,n){for(let i of e)this.addUsage(i,this.inline,Ri);n?.forEach(i=>this.addUsage(i,this.external,Er))}removeStyles(e,n){for(let i of e)this.removeUsage(i,this.inline);n?.forEach(i=>this.removeUsage(i,this.external))}addUsage(e,n,i){let s=n.get(e);s?s.usage++:n.set(e,{usage:1,elements:[...this.hosts].map(o=>this.addElement(o,i(e,this.doc)))})}removeUsage(e,n){let i=n.get(e);i&&(i.usage--,i.usage<=0&&(wi(i.elements),n.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])wi(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[n,{elements:i}]of this.inline)i.push(this.addElement(e,Ri(n,this.doc)));for(let[n,{elements:i}]of this.external)i.push(this.addElement(e,Er(n,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,n){return this.nonce&&n.setAttribute("nonce",this.nonce),this.isServer&&n.setAttribute(Tt,this.appId),e.appendChild(n)}static \u0275fac=function(n){return new(n||t)(v(U),v(nr),v(ir,8),v(fe))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),wr={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Cr=/%COMP%/g;var Ai="%COMP%",io=`_nghost-${Ai}`,so=`_ngcontent-${Ai}`,oo=!0,ao=new C("",{providedIn:"root",factory:()=>oo});function co(t){return so.replace(Cr,t)}function uo(t){return io.replace(Cr,t)}function Mi(t,r){return r.map(e=>e.replace(Cr,t))}var Si=(()=>{class t{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,n,i,s,o,c,a,u=null,l=null){this.eventManager=e,this.sharedStylesHost=n,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=o,this.platformId=c,this.ngZone=a,this.nonce=u,this.tracingService=l,this.platformIsServer=dr(c),this.defaultRenderer=new ze(e,o,a,this.platformIsServer,this.tracingService)}createRenderer(e,n){if(!e||!n)return this.defaultRenderer;this.platformIsServer&&n.encapsulation===Ue.ShadowDom&&(n=O(d({},n),{encapsulation:Ue.Emulated}));let i=this.getOrCreateRenderer(e,n);return i instanceof At?i.applyToHost(e):i instanceof Be&&i.applyStyles(),i}getOrCreateRenderer(e,n){let i=this.rendererByCompId,s=i.get(n.id);if(!s){let o=this.doc,c=this.ngZone,a=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,h=this.platformIsServer,g=this.tracingService;switch(n.encapsulation){case Ue.Emulated:s=new At(a,u,n,this.appId,l,o,c,h,g);break;case Ue.ShadowDom:return new Tr(a,u,e,n,o,c,this.nonce,h,g);default:s=new Be(a,u,n,l,o,c,h,g);break}i.set(n.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(n){return new(n||t)(v(Ti),v(Ci),v(nr),v(ao),v(U),v(fe),v(ee),v(ir),v(Mn,8))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),ze=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(r,e,n,i,s){this.eventManager=r,this.doc=e,this.ngZone=n,this.platformIsServer=i,this.tracingService=s}destroy(){}destroyNode=null;createElement(r,e){return e?this.doc.createElementNS(wr[e]||e,r):this.doc.createElement(r)}createComment(r){return this.doc.createComment(r)}createText(r){return this.doc.createTextNode(r)}appendChild(r,e){(bi(r)?r.content:r).appendChild(e)}insertBefore(r,e,n){r&&(bi(r)?r.content:r).insertBefore(e,n)}removeChild(r,e){e.remove()}selectRootElement(r,e){let n=typeof r=="string"?this.doc.querySelector(r):r;if(!n)throw new E(-5104,!1);return e||(n.textContent=""),n}parentNode(r){return r.parentNode}nextSibling(r){return r.nextSibling}setAttribute(r,e,n,i){if(i){e=i+":"+e;let s=wr[i];s?r.setAttributeNS(s,e,n):r.setAttribute(e,n)}else r.setAttribute(e,n)}removeAttribute(r,e,n){if(n){let i=wr[n];i?r.removeAttributeNS(i,e):r.removeAttribute(`${n}:${e}`)}else r.removeAttribute(e)}addClass(r,e){r.classList.add(e)}removeClass(r,e){r.classList.remove(e)}setStyle(r,e,n,i){i&(xe.DashCase|xe.Important)?r.style.setProperty(e,n,i&xe.Important?"important":""):r.style[e]=n}removeStyle(r,e,n){n&xe.DashCase?r.style.removeProperty(e):r.style[e]=""}setProperty(r,e,n){r!=null&&(r[e]=n)}setValue(r,e){r.nodeValue=e}listen(r,e,n,i){if(typeof r=="string"&&(r=lr().getGlobalEventTarget(this.doc,r),!r))throw new Error(`Unsupported event target ${r} for event ${e}`);let s=this.decoratePreventDefault(n);return this.tracingService!==null&&this.tracingService.wrapEventListener&&(s=this.tracingService.wrapEventListener(r,e,s)),this.eventManager.addEventListener(r,e,s,i)}decoratePreventDefault(r){return e=>{if(e==="__ngUnwrap__")return r;(this.platformIsServer?this.ngZone.runGuarded(()=>r(e)):r(e))===!1&&e.preventDefault()}}};function bi(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var Tr=class extends ze{sharedStylesHost;hostEl;shadowRoot;constructor(r,e,n,i,s,o,c,a,u){super(r,s,o,a,u),this.sharedStylesHost=e,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=i.styles;l=Mi(i.id,l);for(let g of l){let T=document.createElement("style");c&&T.setAttribute("nonce",c),T.textContent=g,this.shadowRoot.appendChild(T)}let h=i.getExternalStyles?.();if(h)for(let g of h){let T=Er(g,s);c&&T.setAttribute("nonce",c),this.shadowRoot.appendChild(T)}}nodeOrShadowRoot(r){return r===this.hostEl?this.shadowRoot:r}appendChild(r,e){return super.appendChild(this.nodeOrShadowRoot(r),e)}insertBefore(r,e,n){return super.insertBefore(this.nodeOrShadowRoot(r),e,n)}removeChild(r,e){return super.removeChild(null,e)}parentNode(r){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(r)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Be=class extends ze{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(r,e,n,i,s,o,c,a,u){super(r,s,o,c,a),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i;let l=n.styles;this.styles=u?Mi(u,l):l,this.styleUrls=n.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},At=class extends Be{contentAttr;hostAttr;constructor(r,e,n,i,s,o,c,a,u){let l=i+"-"+n.id;super(r,e,n,s,o,c,a,u,l),this.contentAttr=co(l),this.hostAttr=uo(l)}applyToHost(r){this.applyStyles(),this.setAttribute(r,this.hostAttr,"")}createElement(r,e){let n=super.createElement(r,e);return super.setAttribute(n,this.contentAttr,""),n}},lo=(()=>{class t extends Ct{constructor(e){super(e)}supports(e){return!0}addEventListener(e,n,i,s){return e.addEventListener(n,i,s),()=>this.removeEventListener(e,n,i,s)}removeEventListener(e,n,i,s){return e.removeEventListener(n,i,s)}static \u0275fac=function(n){return new(n||t)(v(U))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})(),Ei=["alt","control","meta","shift"],ho={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},fo={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},po=(()=>{class t extends Ct{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,n,i,s){let o=t.parseEventName(n),c=t.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>lr().onAndCancel(e,o.domEventName,c,s))}static parseEventName(e){let n=e.toLowerCase().split("."),i=n.shift();if(n.length===0||!(i==="keydown"||i==="keyup"))return null;let s=t._normalizeKey(n.pop()),o="",c=n.indexOf("code");if(c>-1&&(n.splice(c,1),o="code."),Ei.forEach(u=>{let l=n.indexOf(u);l>-1&&(n.splice(l,1),o+=u+".")}),o+=s,n.length!=0||s.length===0)return null;let a={};return a.domEventName=i,a.fullKey=o,a}static matchEventFullKeyCode(e,n){let i=ho[e.key]||e.key,s="";return n.indexOf("code.")>-1&&(i=e.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Ei.forEach(o=>{if(o!==i){let c=fo[o];c(e)&&(s+=o+".")}}),s+=i,s===n)}static eventCallback(e,n,i){return s=>{t.matchEventFullKeyCode(s,e)&&i.runGuarded(()=>n(s))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(n){return new(n||t)(v(U))};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();function ou(t,r){return Kn(d({rootComponent:t},go(r)))}function go(t){return{appProviders:[...Ro,...t?.providers??[]],platformProviders:wo}}function mo(){Sr.makeCurrent()}function vo(){return new rr}function yo(){return Cn(document),document}var wo=[{provide:fe,useValue:ti},{provide:An,useValue:mo,multi:!0},{provide:U,useFactory:yo,deps:[]}];var Ro=[{provide:Rn,useValue:"root"},{provide:rr,useFactory:vo,deps:[]},{provide:br,useClass:lo,multi:!0,deps:[U]},{provide:br,useClass:po,multi:!0,deps:[U]},Si,Ci,Ti,{provide:Ln,useExisting:Si},{provide:yt,useClass:ro,deps:[]},[]];var Ii=(()=>{class t{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(n){return new(n||t)(v(U))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var So=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:function(n){let i=null;return n?i=new(n||t):i=v(bo),i},providedIn:"root"})}return t})(),bo=(()=>{class t extends So{_doc;constructor(e){super(),this._doc=e}sanitize(e,n){if(n==null)return null;switch(e){case ae.NONE:return n;case ae.HTML:return ge(n,"HTML")?pe(n):_n(this._doc,String(n)).toString();case ae.STYLE:return ge(n,"Style")?pe(n):n;case ae.SCRIPT:if(ge(n,"Script"))return pe(n);throw new E(5200,!1);case ae.URL:return ge(n,"URL")?pe(n):xn(String(n));case ae.RESOURCE_URL:if(ge(n,"ResourceURL"))return pe(n);throw new E(5201,!1);default:throw new E(5202,!1)}}bypassSecurityTrustHtml(e){return Dn(e)}bypassSecurityTrustStyle(e){return On(e)}bypassSecurityTrustScript(e){return Pn(e)}bypassSecurityTrustUrl(e){return Nn(e)}bypassSecurityTrustResourceUrl(e){return Un(e)}static \u0275fac=function(n){return new(n||t)(v(U))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var m="primary",rt=Symbol("RouteTitle"),Or=class{params;constructor(r){this.params=r||{}}has(r){return Object.prototype.hasOwnProperty.call(this.params,r)}get(r){if(this.has(r)){let e=this.params[r];return Array.isArray(e)?e[0]:e}return null}getAll(r){if(this.has(r)){let e=this.params[r];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function Ee(t){return new Or(t)}function To(t,r,e){let n=e.path.split("/");if(n.length>t.length||e.pathMatch==="full"&&(r.hasChildren()||n.length<t.length))return null;let i={};for(let s=0;s<n.length;s++){let o=n[s],c=t[s];if(o[0]===":")i[o.substring(1)]=c;else if(o!==c.path)return null}return{consumed:t.slice(0,n.length),posParams:i}}function Co(t,r){if(t.length!==r.length)return!1;for(let e=0;e<t.length;++e)if(!q(t[e],r[e]))return!1;return!0}function q(t,r){let e=t?Pr(t):void 0,n=r?Pr(r):void 0;if(!e||!n||e.length!=n.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!ji(t[i],r[i]))return!1;return!0}function Pr(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function ji(t,r){if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return!1;let e=[...t].sort(),n=[...r].sort();return e.every((i,s)=>n[s]===i)}else return t===r}function $i(t){return t.length>0?t[t.length-1]:null}function ie(t){return hn(t)?t:qn(t)?N(Promise.resolve(t)):p(t)}var Ao={exact:zi,subset:Bi},Fi={exact:Mo,subset:Io,ignored:()=>!0};function Di(t,r,e){return Ao[e.paths](t.root,r.root,e.matrixParams)&&Fi[e.queryParams](t.queryParams,r.queryParams)&&!(e.fragment==="exact"&&t.fragment!==r.fragment)}function Mo(t,r){return q(t,r)}function zi(t,r,e){if(!le(t.segments,r.segments)||!Dt(t.segments,r.segments,e)||t.numberOfChildren!==r.numberOfChildren)return!1;for(let n in r.children)if(!t.children[n]||!zi(t.children[n],r.children[n],e))return!1;return!0}function Io(t,r){return Object.keys(r).length<=Object.keys(t).length&&Object.keys(r).every(e=>ji(t[e],r[e]))}function Bi(t,r,e){return Vi(t,r,r.segments,e)}function Vi(t,r,e,n){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!le(i,e)||r.hasChildren()||!Dt(i,e,n))}else if(t.segments.length===e.length){if(!le(t.segments,e)||!Dt(t.segments,e,n))return!1;for(let i in r.children)if(!t.children[i]||!Bi(t.children[i],r.children[i],n))return!1;return!0}else{let i=e.slice(0,t.segments.length),s=e.slice(t.segments.length);return!le(t.segments,i)||!Dt(t.segments,i,n)||!t.children[m]?!1:Vi(t.children[m],r,s,n)}}function Dt(t,r,e){return r.every((n,i)=>Fi[e](t[i].parameters,n.parameters))}var Y=class{root;queryParams;fragment;_queryParamMap;constructor(r=new b([],{}),e={},n=null){this.root=r,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap??=Ee(this.queryParams),this._queryParamMap}toString(){return Po.serialize(this)}},b=class{segments;children;parent=null;constructor(r,e){this.segments=r,this.children=e,Object.values(e).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ot(this)}},ue=class{path;parameters;_parameterMap;constructor(r,e){this.path=r,this.parameters=e}get parameterMap(){return this._parameterMap??=Ee(this.parameters),this._parameterMap}toString(){return Gi(this)}};function Do(t,r){return le(t,r)&&t.every((e,n)=>q(e.parameters,r[n].parameters))}function le(t,r){return t.length!==r.length?!1:t.every((e,n)=>e.path===r[n].path)}function Oo(t,r){let e=[];return Object.entries(t.children).forEach(([n,i])=>{n===m&&(e=e.concat(r(i,n)))}),Object.entries(t.children).forEach(([n,i])=>{n!==m&&(e=e.concat(r(i,n)))}),e}var nt=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:()=>new Te,providedIn:"root"})}return t})(),Te=class{parse(r){let e=new Ur(r);return new Y(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(r){let e=`/${Ve(r.root,!0)}`,n=xo(r.queryParams),i=typeof r.fragment=="string"?`#${No(r.fragment)}`:"";return`${e}${n}${i}`}},Po=new Te;function Ot(t){return t.segments.map(r=>Gi(r)).join("/")}function Ve(t,r){if(!t.hasChildren())return Ot(t);if(r){let e=t.children[m]?Ve(t.children[m],!1):"",n=[];return Object.entries(t.children).forEach(([i,s])=>{i!==m&&n.push(`${i}:${Ve(s,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}else{let e=Oo(t,(n,i)=>i===m?[Ve(t.children[m],!1)]:[`${i}:${Ve(n,!1)}`]);return Object.keys(t.children).length===1&&t.children[m]!=null?`${Ot(t)}/${e[0]}`:`${Ot(t)}/(${e.join("//")})`}}function qi(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Mt(t){return qi(t).replace(/%3B/gi,";")}function No(t){return encodeURI(t)}function Nr(t){return qi(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Pt(t){return decodeURIComponent(t)}function Oi(t){return Pt(t.replace(/\+/g,"%20"))}function Gi(t){return`${Nr(t.path)}${Uo(t.parameters)}`}function Uo(t){return Object.entries(t).map(([r,e])=>`;${Nr(r)}=${Nr(e)}`).join("")}function xo(t){let r=Object.entries(t).map(([e,n])=>Array.isArray(n)?n.map(i=>`${Mt(e)}=${Mt(i)}`).join("&"):`${Mt(e)}=${Mt(n)}`).filter(e=>e);return r.length?`?${r.join("&")}`:""}var _o=/^[^\/()?;#]+/;function Ar(t){let r=t.match(_o);return r?r[0]:""}var ko=/^[^\/()?;=#]+/;function Lo(t){let r=t.match(ko);return r?r[0]:""}var jo=/^[^=?&#]+/;function $o(t){let r=t.match(jo);return r?r[0]:""}var Fo=/^[^&#]+/;function zo(t){let r=t.match(Fo);return r?r[0]:""}var Ur=class{url;remaining;constructor(r){this.url=r,this.remaining=r}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new b([],{}):new b([],this.parseChildren())}parseQueryParams(){let r={};if(this.consumeOptional("?"))do this.parseQueryParam(r);while(this.consumeOptional("&"));return r}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let r=[];for(this.peekStartsWith("(")||r.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),r.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(r.length>0||Object.keys(e).length>0)&&(n[m]=new b(r,e)),n}parseSegment(){let r=Ar(this.remaining);if(r===""&&this.peekStartsWith(";"))throw new E(4009,!1);return this.capture(r),new ue(Pt(r),this.parseMatrixParams())}parseMatrixParams(){let r={};for(;this.consumeOptional(";");)this.parseParam(r);return r}parseParam(r){let e=Lo(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let i=Ar(this.remaining);i&&(n=i,this.capture(n))}r[Pt(e)]=Pt(n)}parseQueryParam(r){let e=$o(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let o=zo(this.remaining);o&&(n=o,this.capture(n))}let i=Oi(e),s=Oi(n);if(r.hasOwnProperty(i)){let o=r[i];Array.isArray(o)||(o=[o],r[i]=o),o.push(s)}else r[i]=s}parseParens(r){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=Ar(this.remaining),i=this.remaining[n.length];if(i!=="/"&&i!==")"&&i!==";")throw new E(4010,!1);let s;n.indexOf(":")>-1?(s=n.slice(0,n.indexOf(":")),this.capture(s),this.capture(":")):r&&(s=m);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[m]:new b([],o),this.consumeOptional("//")}return e}peekStartsWith(r){return this.remaining.startsWith(r)}consumeOptional(r){return this.peekStartsWith(r)?(this.remaining=this.remaining.substring(r.length),!0):!1}capture(r){if(!this.consumeOptional(r))throw new E(4011,!1)}};function Hi(t){return t.segments.length>0?new b([],{[m]:t}):t}function Wi(t){let r={};for(let[n,i]of Object.entries(t.children)){let s=Wi(i);if(n===m&&s.segments.length===0&&s.hasChildren())for(let[o,c]of Object.entries(s.children))r[o]=c;else(s.segments.length>0||s.hasChildren())&&(r[n]=s)}let e=new b(t.segments,r);return Bo(e)}function Bo(t){if(t.numberOfChildren===1&&t.children[m]){let r=t.children[m];return new b(t.segments.concat(r.segments),r.children)}return t}function de(t){return t instanceof Y}function Vo(t,r,e=null,n=null){let i=Ki(t);return Xi(i,r,e,n)}function Ki(t){let r;function e(s){let o={};for(let a of s.children){let u=e(a);o[a.outlet]=u}let c=new b(s.url,o);return s===t&&(r=c),c}let n=e(t.root),i=Hi(n);return r??i}function Xi(t,r,e,n){let i=t;for(;i.parent;)i=i.parent;if(r.length===0)return Mr(i,i,i,e,n);let s=qo(r);if(s.toRoot())return Mr(i,i,new b([],{}),e,n);let o=Go(s,i,t),c=o.processChildren?Ge(o.segmentGroup,o.index,s.commands):Ji(o.segmentGroup,o.index,s.commands);return Mr(i,o.segmentGroup,c,e,n)}function Nt(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function Ke(t){return typeof t=="object"&&t!=null&&t.outlets}function Mr(t,r,e,n,i){let s={};n&&Object.entries(n).forEach(([a,u])=>{s[a]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let o;t===r?o=e:o=Yi(t,r,e);let c=Hi(Wi(o));return new Y(c,s,i)}function Yi(t,r,e){let n={};return Object.entries(t.children).forEach(([i,s])=>{s===r?n[i]=e:n[i]=Yi(s,r,e)}),new b(t.segments,n)}var Ut=class{isAbsolute;numberOfDoubleDots;commands;constructor(r,e,n){if(this.isAbsolute=r,this.numberOfDoubleDots=e,this.commands=n,r&&n.length>0&&Nt(n[0]))throw new E(4003,!1);let i=n.find(Ke);if(i&&i!==$i(n))throw new E(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function qo(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new Ut(!0,0,t);let r=0,e=!1,n=t.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let c={};return Object.entries(s.outlets).forEach(([a,u])=>{c[a]=typeof u=="string"?u.split("/"):u}),[...i,{outlets:c}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((c,a)=>{a==0&&c==="."||(a==0&&c===""?e=!0:c===".."?r++:c!=""&&i.push(c))}),i):[...i,s]},[]);return new Ut(e,r,n)}var Re=class{segmentGroup;processChildren;index;constructor(r,e,n){this.segmentGroup=r,this.processChildren=e,this.index=n}};function Go(t,r,e){if(t.isAbsolute)return new Re(r,!0,0);if(!e)return new Re(r,!1,NaN);if(e.parent===null)return new Re(e,!0,0);let n=Nt(t.commands[0])?0:1,i=e.segments.length-1+n;return Ho(e,i,t.numberOfDoubleDots)}function Ho(t,r,e){let n=t,i=r,s=e;for(;s>i;){if(s-=i,n=n.parent,!n)throw new E(4005,!1);i=n.segments.length}return new Re(n,!1,i-s)}function Wo(t){return Ke(t[0])?t[0].outlets:{[m]:t}}function Ji(t,r,e){if(t??=new b([],{}),t.segments.length===0&&t.hasChildren())return Ge(t,r,e);let n=Ko(t,r,e),i=e.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){let s=new b(t.segments.slice(0,n.pathIndex),{});return s.children[m]=new b(t.segments.slice(n.pathIndex),t.children),Ge(s,0,i)}else return n.match&&i.length===0?new b(t.segments,{}):n.match&&!t.hasChildren()?xr(t,r,e):n.match?Ge(t,0,i):xr(t,r,e)}function Ge(t,r,e){if(e.length===0)return new b(t.segments,{});{let n=Wo(e),i={};if(Object.keys(n).some(s=>s!==m)&&t.children[m]&&t.numberOfChildren===1&&t.children[m].segments.length===0){let s=Ge(t.children[m],r,e);return new b(t.segments,s.children)}return Object.entries(n).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=Ji(t.children[s],r,o))}),Object.entries(t.children).forEach(([s,o])=>{n[s]===void 0&&(i[s]=o)}),new b(t.segments,i)}}function Ko(t,r,e){let n=0,i=r,s={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(n>=e.length)return s;let o=t.segments[i],c=e[n];if(Ke(c))break;let a=`${c}`,u=n<e.length-1?e[n+1]:null;if(i>0&&a===void 0)break;if(a&&u&&typeof u=="object"&&u.outlets===void 0){if(!Ni(a,u,o))return s;n+=2}else{if(!Ni(a,{},o))return s;n++}i++}return{match:!0,pathIndex:i,commandIndex:n}}function xr(t,r,e){let n=t.segments.slice(0,r),i=0;for(;i<e.length;){let s=e[i];if(Ke(s)){let a=Xo(s.outlets);return new b(n,a)}if(i===0&&Nt(e[0])){let a=t.segments[r];n.push(new ue(a.path,Pi(e[0]))),i++;continue}let o=Ke(s)?s.outlets[m]:`${s}`,c=i<e.length-1?e[i+1]:null;o&&c&&Nt(c)?(n.push(new ue(o,Pi(c))),i+=2):(n.push(new ue(o,{})),i++)}return new b(n,{})}function Xo(t){let r={};return Object.entries(t).forEach(([e,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(r[e]=xr(new b([],{}),0,n))}),r}function Pi(t){let r={};return Object.entries(t).forEach(([e,n])=>r[e]=`${n}`),r}function Ni(t,r,e){return t==e.path&&q(r,e.parameters)}var He="imperative",D=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(D||{}),z=class{id;url;constructor(r,e){this.id=r,this.url=e}},Ce=class extends z{type=D.NavigationStart;navigationTrigger;restoredState;constructor(r,e,n="imperative",i=null){super(r,e),this.navigationTrigger=n,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},G=class extends z{urlAfterRedirects;type=D.NavigationEnd;constructor(r,e,n){super(r,e),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},L=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(L||{}),xt=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(xt||{}),X=class extends z{reason;code;type=D.NavigationCancel;constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},re=class extends z{reason;code;type=D.NavigationSkipped;constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i}},Xe=class extends z{error;target;type=D.NavigationError;constructor(r,e,n,i){super(r,e),this.error=n,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},_t=class extends z{urlAfterRedirects;state;type=D.RoutesRecognized;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},_r=class extends z{urlAfterRedirects;state;type=D.GuardsCheckStart;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},kr=class extends z{urlAfterRedirects;state;shouldActivate;type=D.GuardsCheckEnd;constructor(r,e,n,i,s){super(r,e),this.urlAfterRedirects=n,this.state=i,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Lr=class extends z{urlAfterRedirects;state;type=D.ResolveStart;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},jr=class extends z{urlAfterRedirects;state;type=D.ResolveEnd;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},$r=class{route;type=D.RouteConfigLoadStart;constructor(r){this.route=r}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Fr=class{route;type=D.RouteConfigLoadEnd;constructor(r){this.route=r}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},zr=class{snapshot;type=D.ChildActivationStart;constructor(r){this.snapshot=r}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Br=class{snapshot;type=D.ChildActivationEnd;constructor(r){this.snapshot=r}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Vr=class{snapshot;type=D.ActivationStart;constructor(r){this.snapshot=r}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},qr=class{snapshot;type=D.ActivationEnd;constructor(r){this.snapshot=r}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},kt=class{routerEvent;position;anchor;type=D.Scroll;constructor(r,e,n){this.routerEvent=r,this.position=e,this.anchor=n}toString(){let r=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${r}')`}},Ye=class{},Ae=class{url;navigationBehaviorOptions;constructor(r,e){this.url=r,this.navigationBehaviorOptions=e}};function Yo(t,r){return t.providers&&!t._injector&&(t._injector=sr(t.providers,r,`Route: ${t.path}`)),t._injector??r}function B(t){return t.outlet||m}function Jo(t,r){let e=t.filter(n=>B(n)===r);return e.push(...t.filter(n=>B(n)!==r)),e}function it(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let r=t.parent;r;r=r.parent){let e=r.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var Gr=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return it(this.route?.snapshot)??this.rootInjector}constructor(r){this.rootInjector=r,this.children=new st(this.rootInjector)}},st=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,n){let i=this.getOrCreateContext(e);i.outlet=n,this.contexts.set(e,i)}onChildOutletDestroyed(e){let n=this.getContext(e);n&&(n.outlet=null,n.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let n=this.getContext(e);return n||(n=new Gr(this.rootInjector),this.contexts.set(e,n)),n}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(n){return new(n||t)(v(oe))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Lt=class{_root;constructor(r){this._root=r}get root(){return this._root.value}parent(r){let e=this.pathFromRoot(r);return e.length>1?e[e.length-2]:null}children(r){let e=Hr(r,this._root);return e?e.children.map(n=>n.value):[]}firstChild(r){let e=Hr(r,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(r){let e=Wr(r,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==r)}pathFromRoot(r){return Wr(r,this._root).map(e=>e.value)}};function Hr(t,r){if(t===r.value)return r;for(let e of r.children){let n=Hr(t,e);if(n)return n}return null}function Wr(t,r){if(t===r.value)return[r];for(let e of r.children){let n=Wr(t,e);if(n.length)return n.unshift(r),n}return[]}var k=class{value;children;constructor(r,e){this.value=r,this.children=e}toString(){return`TreeNode(${this.value})`}};function we(t){let r={};return t&&t.children.forEach(e=>r[e.value.outlet]=e),r}var jt=class extends Lt{snapshot;constructor(r,e){super(r),this.snapshot=e,rn(this,r)}toString(){return this.snapshot.toString()}};function Zi(t){let r=Zo(t),e=new P([new ue("",{})]),n=new P({}),i=new P({}),s=new P({}),o=new P(""),c=new ne(e,n,s,o,i,m,t,r.root);return c.snapshot=r.root,new jt(new k(c,[]),r)}function Zo(t){let r={},e={},n={},i="",s=new Se([],r,n,i,e,m,t,null,{});return new Ft("",new k(s,[]))}var ne=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(r,e,n,i,s,o,c,a){this.urlSubject=r,this.paramsSubject=e,this.queryParamsSubject=n,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=c,this._futureSnapshot=a,this.title=this.dataSubject?.pipe(y(u=>u[rt]))??p(void 0),this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(y(r=>Ee(r))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(y(r=>Ee(r))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function $t(t,r,e="emptyOnly"){let n,{routeConfig:i}=t;return r!==null&&(e==="always"||i?.path===""||!r.component&&!r.routeConfig?.loadComponent)?n={params:d(d({},r.params),t.params),data:d(d({},r.data),t.data),resolve:d(d(d(d({},t.data),r.data),i?.data),t._resolvedData)}:n={params:d({},t.params),data:d({},t.data),resolve:d(d({},t.data),t._resolvedData??{})},i&&es(i)&&(n.resolve[rt]=i.title),n}var Se=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[rt]}constructor(r,e,n,i,s,o,c,a,u){this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=s,this.outlet=o,this.component=c,this.routeConfig=a,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Ee(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Ee(this.queryParams),this._queryParamMap}toString(){let r=this.url.map(n=>n.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${r}', path:'${e}')`}},Ft=class extends Lt{url;constructor(r,e){super(e),this.url=r,rn(this,e)}toString(){return Qi(this._root)}};function rn(t,r){r.value._routerState=t,r.children.forEach(e=>rn(t,e))}function Qi(t){let r=t.children.length>0?` { ${t.children.map(Qi).join(", ")} } `:"";return`${t.value}${r}`}function Ir(t){if(t.snapshot){let r=t.snapshot,e=t._futureSnapshot;t.snapshot=e,q(r.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),r.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),q(r.params,e.params)||t.paramsSubject.next(e.params),Co(r.url,e.url)||t.urlSubject.next(e.url),q(r.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Kr(t,r){let e=q(t.params,r.params)&&Do(t.url,r.url),n=!t.parent!=!r.parent;return e&&!n&&(!t.parent||Kr(t.parent,r.parent))}function es(t){return typeof t.title=="string"||t.title===null}var Qo=new C(""),ea=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=m;activateEvents=new Ne;deactivateEvents=new Ne;attachEvents=new Ne;detachEvents=new Ne;routerOutletData=En(void 0);parentContexts=f(st);location=f(Fn);changeDetector=f(ur);inputBinder=f(qt,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:n,previousValue:i}=e.name;if(n)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new E(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new E(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new E(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,n){this.activated=e,this._activatedRoute=n,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,n){if(this.isActivated)throw new E(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,a=new Xr(e,c,i.injector,this.routerOutletData);this.activated=i.createComponent(o,{index:i.length,injector:a,environmentInjector:n}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(n){return new(n||t)};static \u0275dir=or({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[er]})}return t})(),Xr=class t{route;childContexts;parent;outletData;__ngOutletInjector(r){return new t(this.route,this.childContexts,r,this.outletData)}constructor(r,e,n,i){this.route=r,this.childContexts=e,this.parent=n,this.outletData=i}get(r,e){return r===ne?this.route:r===st?this.childContexts:r===Qo?this.outletData:this.parent.get(r,e)}},qt=new C(""),Ui=(()=>{class t{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(e){this.unsubscribeFromRouteData(e),this.subscribeToRouteData(e)}unsubscribeFromRouteData(e){this.outletDataSubscriptions.get(e)?.unsubscribe(),this.outletDataSubscriptions.delete(e)}subscribeToRouteData(e){let{activatedRoute:n}=e,i=lt([n.queryParams,n.params,n.data]).pipe(_(([s,o,c],a)=>(c=d(d(d({},s),o),c),a===0?p(c):Promise.resolve(c)))).subscribe(s=>{if(!e.isActivated||!e.activatedComponentRef||e.activatedRoute!==n||n.component===null){this.unsubscribeFromRouteData(e);return}let o=Xn(n.component);if(!o){this.unsubscribeFromRouteData(e);return}for(let{templateName:c}of o.inputs)e.activatedComponentRef.setInput(c,s[c])});this.outletDataSubscriptions.set(e,i)}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();function ta(t,r,e){let n=Je(t,r._root,e?e._root:void 0);return new jt(n,r)}function Je(t,r,e){if(e&&t.shouldReuseRoute(r.value,e.value.snapshot)){let n=e.value;n._futureSnapshot=r.value;let i=ra(t,r,e);return new k(n,i)}else{if(t.shouldAttach(r.value)){let s=t.retrieve(r.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=r.value,o.children=r.children.map(c=>Je(t,c)),o}}let n=na(r.value),i=r.children.map(s=>Je(t,s));return new k(n,i)}}function ra(t,r,e){return r.children.map(n=>{for(let i of e.children)if(t.shouldReuseRoute(n.value,i.value.snapshot))return Je(t,n,i);return Je(t,n)})}function na(t){return new ne(new P(t.url),new P(t.params),new P(t.queryParams),new P(t.fragment),new P(t.data),t.outlet,t.component,t)}var Ze=class{redirectTo;navigationBehaviorOptions;constructor(r,e){this.redirectTo=r,this.navigationBehaviorOptions=e}},ts="ngNavigationCancelingError";function zt(t,r){let{redirectTo:e,navigationBehaviorOptions:n}=de(r)?{redirectTo:r,navigationBehaviorOptions:void 0}:r,i=rs(!1,L.Redirect);return i.url=e,i.navigationBehaviorOptions=n,i}function rs(t,r){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[ts]=!0,e.cancellationCode=r,e}function ia(t){return ns(t)&&de(t.url)}function ns(t){return!!t&&t[ts]}var sa=(t,r,e,n)=>y(i=>(new Yr(r,i.targetRouterState,i.currentRouterState,e,n).activate(t),i)),Yr=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(r,e,n,i,s){this.routeReuseStrategy=r,this.futureState=e,this.currState=n,this.forwardEvent=i,this.inputBindingEnabled=s}activate(r){let e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,r),Ir(this.futureState.root),this.activateChildRoutes(e,n,r)}deactivateChildRoutes(r,e,n){let i=we(e);r.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],n),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,n)})}deactivateRoutes(r,e,n){let i=r.value,s=e?e.value:null;if(i===s)if(i.component){let o=n.getContext(i.outlet);o&&this.deactivateChildRoutes(r,e,o.children)}else this.deactivateChildRoutes(r,e,n);else s&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(r,e){r.value.component&&this.routeReuseStrategy.shouldDetach(r.value.snapshot)?this.detachAndStoreRouteSubtree(r,e):this.deactivateRouteAndOutlet(r,e)}detachAndStoreRouteSubtree(r,e){let n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,s=we(r);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(n&&n.outlet){let o=n.outlet.detach(),c=n.children.onOutletDeactivated();this.routeReuseStrategy.store(r.value.snapshot,{componentRef:o,route:r,contexts:c})}}deactivateRouteAndOutlet(r,e){let n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,s=we(r);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(r,e,n){let i=we(e);r.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],n),this.forwardEvent(new qr(s.value.snapshot))}),r.children.length&&this.forwardEvent(new Br(r.value.snapshot))}activateRoutes(r,e,n){let i=r.value,s=e?e.value:null;if(Ir(i),i===s)if(i.component){let o=n.getOrCreateContext(i.outlet);this.activateChildRoutes(r,e,o.children)}else this.activateChildRoutes(r,e,n);else if(i.component){let o=n.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let c=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(c.contexts),o.attachRef=c.componentRef,o.route=c.route.value,o.outlet&&o.outlet.attach(c.componentRef,c.route.value),Ir(c.route.value),this.activateChildRoutes(r,null,o.children)}else o.attachRef=null,o.route=i,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(r,null,o.children)}else this.activateChildRoutes(r,null,n)}},Bt=class{path;route;constructor(r){this.path=r,this.route=this.path[this.path.length-1]}},be=class{component;route;constructor(r,e){this.component=r,this.route=e}};function oa(t,r,e){let n=t._root,i=r?r._root:null;return qe(n,i,e,[n.value])}function aa(t){let r=t.routeConfig?t.routeConfig.canActivateChild:null;return!r||r.length===0?null:{node:t,guards:r}}function Ie(t,r){let e=Symbol(),n=r.get(t,e);return n===e?typeof t=="function"&&!wn(t)?t:r.get(t):n}function qe(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=we(r);return t.children.forEach(o=>{ca(o,s[o.value.outlet],e,n.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,c])=>We(c,e.getContext(o),i)),i}function ca(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=t.value,o=r?r.value:null,c=e?e.getContext(t.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let a=ua(o,s,s.routeConfig.runGuardsAndResolvers);a?i.canActivateChecks.push(new Bt(n)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?qe(t,r,c?c.children:null,n,i):qe(t,r,e,n,i),a&&c&&c.outlet&&c.outlet.isActivated&&i.canDeactivateChecks.push(new be(c.outlet.component,o))}else o&&We(r,c,i),i.canActivateChecks.push(new Bt(n)),s.component?qe(t,null,c?c.children:null,n,i):qe(t,null,e,n,i);return i}function ua(t,r,e){if(typeof e=="function")return e(t,r);switch(e){case"pathParamsChange":return!le(t.url,r.url);case"pathParamsOrQueryParamsChange":return!le(t.url,r.url)||!q(t.queryParams,r.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Kr(t,r)||!q(t.queryParams,r.queryParams);case"paramsChange":default:return!Kr(t,r)}}function We(t,r,e){let n=we(t),i=t.value;Object.entries(n).forEach(([s,o])=>{i.component?r?We(o,r.children.getContext(s),e):We(o,null,e):We(o,r,e)}),i.component?r&&r.outlet&&r.outlet.isActivated?e.canDeactivateChecks.push(new be(r.outlet.component,i)):e.canDeactivateChecks.push(new be(null,i)):e.canDeactivateChecks.push(new be(null,i))}function ot(t){return typeof t=="function"}function la(t){return typeof t=="boolean"}function da(t){return t&&ot(t.canLoad)}function ha(t){return t&&ot(t.canActivate)}function fa(t){return t&&ot(t.canActivateChild)}function pa(t){return t&&ot(t.canDeactivate)}function ga(t){return t&&ot(t.canMatch)}function is(t){return t instanceof fn||t?.name==="EmptyError"}var It=Symbol("INITIAL_VALUE");function Me(){return _(t=>lt(t.map(r=>r.pipe(he(1),vn(It)))).pipe(y(r=>{for(let e of r)if(e!==!0){if(e===It)return It;if(e===!1||ma(e))return e}return!0}),K(r=>r!==It),he(1)))}function ma(t){return de(t)||t instanceof Ze}function va(t,r){return x(e=>{let{targetSnapshot:n,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?p(O(d({},e),{guardsResult:!0})):ya(o,n,i,t).pipe(x(c=>c&&la(c)?wa(n,s,t,r):p(c)),y(c=>O(d({},e),{guardsResult:c})))})}function ya(t,r,e,n){return N(t).pipe(x(i=>Ta(i.component,i.route,e,r,n)),Q(i=>i!==!0,!0))}function wa(t,r,e,n){return N(r).pipe(Z(i=>pn(Sa(i.route.parent,n),Ra(i.route,n),Ea(t,i.path,e),ba(t,i.route,e))),Q(i=>i!==!0,!0))}function Ra(t,r){return t!==null&&r&&r(new Vr(t)),p(!0)}function Sa(t,r){return t!==null&&r&&r(new zr(t)),p(!0)}function ba(t,r,e){let n=r.routeConfig?r.routeConfig.canActivate:null;if(!n||n.length===0)return p(!0);let i=n.map(s=>Yt(()=>{let o=it(r)??e,c=Ie(s,o),a=ha(c)?c.canActivate(r,t):F(o,()=>c(r,t));return ie(a).pipe(Q())}));return p(i).pipe(Me())}function Ea(t,r,e){let n=r[r.length-1],s=r.slice(0,r.length-1).reverse().map(o=>aa(o)).filter(o=>o!==null).map(o=>Yt(()=>{let c=o.guards.map(a=>{let u=it(o.node)??e,l=Ie(a,u),h=fa(l)?l.canActivateChild(n,t):F(u,()=>l(n,t));return ie(h).pipe(Q())});return p(c).pipe(Me())}));return p(s).pipe(Me())}function Ta(t,r,e,n,i){let s=r&&r.routeConfig?r.routeConfig.canDeactivate:null;if(!s||s.length===0)return p(!0);let o=s.map(c=>{let a=it(r)??i,u=Ie(c,a),l=pa(u)?u.canDeactivate(t,r,e,n):F(a,()=>u(t,r,e,n));return ie(l).pipe(Q())});return p(o).pipe(Me())}function Ca(t,r,e,n){let i=r.canLoad;if(i===void 0||i.length===0)return p(!0);let s=i.map(o=>{let c=Ie(o,t),a=da(c)?c.canLoad(r,e):F(t,()=>c(r,e));return ie(a)});return p(s).pipe(Me(),ss(n))}function ss(t){return ln(A(r=>{if(typeof r!="boolean")throw zt(t,r)}),y(r=>r===!0))}function Aa(t,r,e,n){let i=r.canMatch;if(!i||i.length===0)return p(!0);let s=i.map(o=>{let c=Ie(o,t),a=ga(c)?c.canMatch(r,e):F(t,()=>c(r,e));return ie(a)});return p(s).pipe(Me(),ss(n))}var Qe=class{segmentGroup;constructor(r){this.segmentGroup=r||null}},et=class extends Error{urlTree;constructor(r){super(),this.urlTree=r}};function ye(t){return $(new Qe(t))}function Ma(t){return $(new E(4e3,!1))}function Ia(t){return $(rs(!1,L.GuardRejected))}var Jr=class{urlSerializer;urlTree;constructor(r,e){this.urlSerializer=r,this.urlTree=e}lineralizeSegments(r,e){let n=[],i=e.root;for(;;){if(n=n.concat(i.segments),i.numberOfChildren===0)return p(n);if(i.numberOfChildren>1||!i.children[m])return Ma(`${r.redirectTo}`);i=i.children[m]}}applyRedirectCommands(r,e,n,i,s){if(typeof e!="string"){let c=e,{queryParams:a,fragment:u,routeConfig:l,url:h,outlet:g,params:T,data:I,title:S}=i,R=F(s,()=>c({params:T,data:I,queryParams:a,fragment:u,routeConfig:l,url:h,outlet:g,title:S}));if(R instanceof Y)throw new et(R);e=R}let o=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),r,n);if(e[0]==="/")throw new et(o);return o}applyRedirectCreateUrlTree(r,e,n,i){let s=this.createSegmentGroup(r,e.root,n,i);return new Y(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(r,e){let n={};return Object.entries(r).forEach(([i,s])=>{if(typeof s=="string"&&s[0]===":"){let c=s.substring(1);n[i]=e[c]}else n[i]=s}),n}createSegmentGroup(r,e,n,i){let s=this.createSegments(r,e.segments,n,i),o={};return Object.entries(e.children).forEach(([c,a])=>{o[c]=this.createSegmentGroup(r,a,n,i)}),new b(s,o)}createSegments(r,e,n,i){return e.map(s=>s.path[0]===":"?this.findPosParam(r,s,i):this.findOrReturn(s,n))}findPosParam(r,e,n){let i=n[e.path.substring(1)];if(!i)throw new E(4001,!1);return i}findOrReturn(r,e){let n=0;for(let i of e){if(i.path===r.path)return e.splice(n),i;n++}return r}},Zr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Da(t,r,e,n,i){let s=os(t,r,e);return s.matched?(n=Yo(r,n),Aa(n,r,e,i).pipe(y(o=>o===!0?s:d({},Zr)))):p(s)}function os(t,r,e){if(r.path==="**")return Oa(e);if(r.path==="")return r.pathMatch==="full"&&(t.hasChildren()||e.length>0)?d({},Zr):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(r.matcher||To)(e,t,r);if(!i)return d({},Zr);let s={};Object.entries(i.posParams??{}).forEach(([c,a])=>{s[c]=a.path});let o=i.consumed.length>0?d(d({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function Oa(t){return{matched:!0,parameters:t.length>0?$i(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function xi(t,r,e,n){return e.length>0&&Ua(t,e,n)?{segmentGroup:new b(r,Na(n,new b(e,t.children))),slicedSegments:[]}:e.length===0&&xa(t,e,n)?{segmentGroup:new b(t.segments,Pa(t,e,n,t.children)),slicedSegments:e}:{segmentGroup:new b(t.segments,t.children),slicedSegments:e}}function Pa(t,r,e,n){let i={};for(let s of e)if(Gt(t,r,s)&&!n[B(s)]){let o=new b([],{});i[B(s)]=o}return d(d({},n),i)}function Na(t,r){let e={};e[m]=r;for(let n of t)if(n.path===""&&B(n)!==m){let i=new b([],{});e[B(n)]=i}return e}function Ua(t,r,e){return e.some(n=>Gt(t,r,n)&&B(n)!==m)}function xa(t,r,e){return e.some(n=>Gt(t,r,n))}function Gt(t,r,e){return(t.hasChildren()||r.length>0)&&e.pathMatch==="full"?!1:e.path===""}function _a(t,r,e){return r.length===0&&!t.children[e]}var Qr=class{};function ka(t,r,e,n,i,s,o="emptyOnly"){return new en(t,r,e,n,i,o,s).recognize()}var La=31,en=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(r,e,n,i,s,o,c){this.injector=r,this.configLoader=e,this.rootComponentType=n,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=c,this.applyRedirects=new Jr(this.urlSerializer,this.urlTree)}noMatchError(r){return new E(4002,`'${r.segmentGroup}'`)}recognize(){let r=xi(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(r).pipe(y(({children:e,rootSnapshot:n})=>{let i=new k(n,e),s=new Ft("",i),o=Vo(n,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),{state:s,tree:o}}))}match(r){let e=new Se([],Object.freeze({}),Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),m,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,r,m,e).pipe(y(n=>({children:n,rootSnapshot:e})),V(n=>{if(n instanceof et)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof Qe?this.noMatchError(n):n}))}processSegmentGroup(r,e,n,i,s){return n.segments.length===0&&n.hasChildren()?this.processChildren(r,e,n,s):this.processSegment(r,e,n,n.segments,i,!0,s).pipe(y(o=>o instanceof k?[o]:[]))}processChildren(r,e,n,i){let s=[];for(let o of Object.keys(n.children))o==="primary"?s.unshift(o):s.push(o);return N(s).pipe(Z(o=>{let c=n.children[o],a=Jo(e,o);return this.processSegmentGroup(r,a,c,o,i)}),mn((o,c)=>(o.push(...c),o)),Jt(null),gn(),x(o=>{if(o===null)return ye(n);let c=as(o);return ja(c),p(c)}))}processSegment(r,e,n,i,s,o,c){return N(e).pipe(Z(a=>this.processSegmentAgainstRoute(a._injector??r,e,a,n,i,s,o,c).pipe(V(u=>{if(u instanceof Qe)return p(null);throw u}))),Q(a=>!!a),V(a=>{if(is(a))return _a(n,i,s)?p(new Qr):ye(n);throw a}))}processSegmentAgainstRoute(r,e,n,i,s,o,c,a){return B(n)!==o&&(o===m||!Gt(i,s,n))?ye(i):n.redirectTo===void 0?this.matchSegmentAgainstRoute(r,i,n,s,o,a):this.allowRedirects&&c?this.expandSegmentAgainstRouteUsingRedirect(r,i,e,n,s,o,a):ye(i)}expandSegmentAgainstRouteUsingRedirect(r,e,n,i,s,o,c){let{matched:a,parameters:u,consumedSegments:l,positionalParamSegments:h,remainingSegments:g}=os(e,i,s);if(!a)return ye(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>La&&(this.allowRedirects=!1));let T=new Se(s,u,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,_i(i),B(i),i.component??i._loadedComponent??null,i,ki(i)),I=$t(T,c,this.paramsInheritanceStrategy);T.params=Object.freeze(I.params),T.data=Object.freeze(I.data);let S=this.applyRedirects.applyRedirectCommands(l,i.redirectTo,h,T,r);return this.applyRedirects.lineralizeSegments(i,S).pipe(x(R=>this.processSegment(r,n,e,R.concat(g),o,!1,c)))}matchSegmentAgainstRoute(r,e,n,i,s,o){let c=Da(e,n,i,r,this.urlSerializer);return n.path==="**"&&(e.children={}),c.pipe(_(a=>a.matched?(r=n._injector??r,this.getChildConfig(r,n,i).pipe(_(({routes:u})=>{let l=n._loadedInjector??r,{parameters:h,consumedSegments:g,remainingSegments:T}=a,I=new Se(g,h,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,_i(n),B(n),n.component??n._loadedComponent??null,n,ki(n)),S=$t(I,o,this.paramsInheritanceStrategy);I.params=Object.freeze(S.params),I.data=Object.freeze(S.data);let{segmentGroup:R,slicedSegments:j}=xi(e,g,T,u);if(j.length===0&&R.hasChildren())return this.processChildren(l,u,R,I).pipe(y(M=>new k(I,M)));if(u.length===0&&j.length===0)return p(new k(I,[]));let ut=B(n)===s;return this.processSegment(l,u,R,j,ut?m:s,!0,I).pipe(y(M=>new k(I,M instanceof k?[M]:[])))}))):ye(e)))}getChildConfig(r,e,n){return e.children?p({routes:e.children,injector:r}):e.loadChildren?e._loadedRoutes!==void 0?p({routes:e._loadedRoutes,injector:e._loadedInjector}):Ca(r,e,n,this.urlSerializer).pipe(x(i=>i?this.configLoader.loadChildren(r,e).pipe(A(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):Ia(e))):p({routes:[],injector:r})}};function ja(t){t.sort((r,e)=>r.value.outlet===m?-1:e.value.outlet===m?1:r.value.outlet.localeCompare(e.value.outlet))}function $a(t){let r=t.value.routeConfig;return r&&r.path===""}function as(t){let r=[],e=new Set;for(let n of t){if(!$a(n)){r.push(n);continue}let i=r.find(s=>n.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...n.children),e.add(i)):r.push(n)}for(let n of e){let i=as(n.children);r.push(new k(n.value,i))}return r.filter(n=>!e.has(n))}function _i(t){return t.data||{}}function ki(t){return t.resolve||{}}function Fa(t,r,e,n,i,s){return x(o=>ka(t,r,e,n,o.extractedUrl,i,s).pipe(y(({state:c,tree:a})=>O(d({},o),{targetSnapshot:c,urlAfterRedirects:a}))))}function za(t,r){return x(e=>{let{targetSnapshot:n,guards:{canActivateChecks:i}}=e;if(!i.length)return p(e);let s=new Set(i.map(a=>a.route)),o=new Set;for(let a of s)if(!o.has(a))for(let u of cs(a))o.add(u);let c=0;return N(o).pipe(Z(a=>s.has(a)?Ba(a,n,t,r):(a.data=$t(a,a.parent,t).resolve,p(void 0))),A(()=>c++),Zt(1),x(a=>c===o.size?p(e):W))})}function cs(t){let r=t.children.map(e=>cs(e)).flat();return[t,...r]}function Ba(t,r,e,n){let i=t.routeConfig,s=t._resolve;return i?.title!==void 0&&!es(i)&&(s[rt]=i.title),Va(s,t,r,n).pipe(y(o=>(t._resolvedData=o,t.data=$t(t,t.parent,e).resolve,null)))}function Va(t,r,e,n){let i=Pr(t);if(i.length===0)return p({});let s={};return N(i).pipe(x(o=>qa(t[o],r,e,n).pipe(Q(),A(c=>{if(c instanceof Ze)throw zt(new Te,c);s[o]=c}))),Zt(1),y(()=>s),V(o=>is(o)?W:$(o)))}function qa(t,r,e,n){let i=it(r)??n,s=Ie(t,i),o=s.resolve?s.resolve(r,e):F(i,()=>s(r,e));return ie(o)}function Dr(t){return _(r=>{let e=t(r);return e?N(e).pipe(y(()=>r)):p(r)})}var us=(()=>{class t{buildTitle(e){let n,i=e.root;for(;i!==void 0;)n=this.getResolvedTitleForRoute(i)??n,i=i.children.find(s=>s.outlet===m);return n}getResolvedTitleForRoute(e){return e.data[rt]}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:()=>f(Ga),providedIn:"root"})}return t})(),Ga=(()=>{class t extends us{title;constructor(e){super(),this.title=e}updateTitle(e){let n=this.buildTitle(e);n!==void 0&&this.title.setTitle(n)}static \u0275fac=function(n){return new(n||t)(v(Ii))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),at=new C("",{providedIn:"root",factory:()=>({})}),Ha=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275cmp=Bn({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(n,i){n&1&&Hn(0,"router-outlet")},dependencies:[ea],encapsulation:2})}return t})();function nn(t){let r=t.children&&t.children.map(nn),e=r?O(d({},t),{children:r}):d({},t);return!e.component&&!e.loadComponent&&(r||e.loadChildren)&&e.outlet&&e.outlet!==m&&(e.component=Ha),e}var tt=new C(""),sn=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=f(gt);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return p(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let n=ie(e.loadComponent()).pipe(y(ls),A(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),se(()=>{this.componentLoaders.delete(e)})),i=new Kt(n,()=>new J).pipe(Wt());return this.componentLoaders.set(e,i),i}loadChildren(e,n){if(this.childrenLoaders.get(n))return this.childrenLoaders.get(n);if(n._loadedRoutes)return p({routes:n._loadedRoutes,injector:n._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(n);let s=Wa(n,this.compiler,e,this.onLoadEndListener).pipe(se(()=>{this.childrenLoaders.delete(n)})),o=new Kt(s,()=>new J).pipe(Wt());return this.childrenLoaders.set(n,o),o}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Wa(t,r,e,n){return ie(t.loadChildren()).pipe(y(ls),x(i=>i instanceof zn||Array.isArray(i)?p(i):N(r.compileModuleAsync(i))),y(i=>{n&&n(t);let s,o,c=!1;return Array.isArray(i)?(o=i,c=!0):(s=i.create(e).injector,o=s.get(tt,[],{optional:!0,self:!0}).flat()),{routes:o.map(nn),injector:s}}))}function Ka(t){return t&&typeof t=="object"&&"default"in t}function ls(t){return Ka(t)?t.default:t}var on=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:()=>f(Xa),providedIn:"root"})}return t})(),Xa=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,n){return e}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ds=new C(""),hs=new C("");function Ya(t,r,e){let n=t.get(hs),i=t.get(U);return t.get(ee).runOutsideAngular(()=>{if(!i.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,new Promise(u=>setTimeout(u));let s,o=new Promise(u=>{s=u}),c=i.startViewTransition(()=>(s(),Ja(t))),{onViewTransitionCreated:a}=n;return a&&F(t,()=>a({transition:c,from:r,to:e})),o})}function Ja(t){return new Promise(r=>{In({read:()=>setTimeout(r)},{injector:t})})}var fs=new C(""),an=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new J;transitionAbortSubject=new J;configLoader=f(sn);environmentInjector=f(oe);destroyRef=f(bn);urlSerializer=f(nt);rootContexts=f(st);location=f(ke);inputBindingEnabled=f(qt,{optional:!0})!==null;titleStrategy=f(us);options=f(at,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=f(on);createViewTransition=f(ds,{optional:!0});navigationErrorHandler=f(fs,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>p(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new $r(i)),n=i=>this.events.next(new Fr(i));this.configLoader.onLoadEndListener=n,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let n=++this.navigationId;this.transitions?.next(O(d(d({},this.transitions.value),e),{id:n}))}setupNavigations(e,n,i){return this.transitions=new P({id:0,currentUrlTree:n,currentRawUrl:n,extractedUrl:this.urlHandlingStrategy.extract(n),urlAfterRedirects:this.urlHandlingStrategy.extract(n),rawUrl:n,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:He,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(K(s=>s.id!==0),y(s=>O(d({},s),{extractedUrl:this.urlHandlingStrategy.extract(s.rawUrl)})),_(s=>{let o=!1,c=!1;return p(s).pipe(_(a=>{if(this.navigationId>s.id)return this.cancelNavigationTransition(s,"",L.SupersededByNewNavigation),W;this.currentTransition=s,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,targetBrowserUrl:typeof a.extras.browserUrl=="string"?this.urlSerializer.parse(a.extras.browserUrl):a.extras.browserUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?O(d({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let u=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=a.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!u&&l!=="reload"){let h="";return this.events.next(new re(a.id,this.urlSerializer.serialize(a.rawUrl),h,xt.IgnoredSameUrlNavigation)),a.resolve(!1),W}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return p(a).pipe(_(h=>{let g=this.transitions?.getValue();return this.events.next(new Ce(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),g!==this.transitions?.getValue()?W:Promise.resolve(h)}),Fa(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),A(h=>{s.targetSnapshot=h.targetSnapshot,s.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation=O(d({},this.currentNavigation),{finalUrl:h.urlAfterRedirects});let g=new _t(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(g)}));if(u&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:h,extractedUrl:g,source:T,restoredState:I,extras:S}=a,R=new Ce(h,this.urlSerializer.serialize(g),T,I);this.events.next(R);let j=Zi(this.rootComponentType).snapshot;return this.currentTransition=s=O(d({},a),{targetSnapshot:j,urlAfterRedirects:g,extras:O(d({},S),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,p(s)}else{let h="";return this.events.next(new re(a.id,this.urlSerializer.serialize(a.extractedUrl),h,xt.IgnoredByUrlHandlingStrategy)),a.resolve(!1),W}}),A(a=>{let u=new _r(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}),y(a=>(this.currentTransition=s=O(d({},a),{guards:oa(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),s)),va(this.environmentInjector,a=>this.events.next(a)),A(a=>{if(s.guardsResult=a.guardsResult,a.guardsResult&&typeof a.guardsResult!="boolean")throw zt(this.urlSerializer,a.guardsResult);let u=new kr(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(u)}),K(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",L.GuardRejected),!1)),Dr(a=>{if(a.guards.canActivateChecks.length)return p(a).pipe(A(u=>{let l=new Lr(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}),_(u=>{let l=!1;return p(u).pipe(za(this.paramsInheritanceStrategy,this.environmentInjector),A({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(u,"",L.NoDataFromResolver)}}))}),A(u=>{let l=new jr(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}))}),Dr(a=>{let u=l=>{let h=[];l.routeConfig?.loadComponent&&!l.routeConfig._loadedComponent&&h.push(this.configLoader.loadComponent(l.routeConfig).pipe(A(g=>{l.component=g}),y(()=>{})));for(let g of l.children)h.push(...u(g));return h};return lt(u(a.targetSnapshot.root)).pipe(Jt(null),he(1))}),Dr(()=>this.afterPreactivation()),_(()=>{let{currentSnapshot:a,targetSnapshot:u}=s,l=this.createViewTransition?.(this.environmentInjector,a.root,u.root);return l?N(l).pipe(y(()=>s)):p(s)}),y(a=>{let u=ta(e.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=s=O(d({},a),{targetRouterState:u}),this.currentNavigation.targetRouterState=u,s}),A(()=>{this.events.next(new Ye)}),sa(this.rootContexts,e.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),he(1),A({next:a=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new G(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),this.titleStrategy?.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{o=!0}}),yn(this.transitionAbortSubject.pipe(A(a=>{throw a}))),se(()=>{!o&&!c&&this.cancelNavigationTransition(s,"",L.SupersededByNewNavigation),this.currentTransition?.id===s.id&&(this.currentNavigation=null,this.currentTransition=null)}),V(a=>{if(this.destroyed)return s.resolve(!1),W;if(c=!0,ns(a))this.events.next(new X(s.id,this.urlSerializer.serialize(s.extractedUrl),a.message,a.cancellationCode)),ia(a)?this.events.next(new Ae(a.url,a.navigationBehaviorOptions)):s.resolve(!1);else{let u=new Xe(s.id,this.urlSerializer.serialize(s.extractedUrl),a,s.targetSnapshot??void 0);try{let l=F(this.environmentInjector,()=>this.navigationErrorHandler?.(u));if(l instanceof Ze){let{message:h,cancellationCode:g}=zt(this.urlSerializer,l);this.events.next(new X(s.id,this.urlSerializer.serialize(s.extractedUrl),h,g)),this.events.next(new Ae(l.redirectTo,l.navigationBehaviorOptions))}else throw this.events.next(u),a}catch(l){this.options.resolveNavigationPromiseOnError?s.resolve(!1):s.reject(l)}}return W}))}))}cancelNavigationTransition(e,n,i){let s=new X(e.id,this.urlSerializer.serialize(e.extractedUrl),n,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),n=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==n?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Za(t){return t!==He}var Qa=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:()=>f(ec),providedIn:"root"})}return t})(),tn=class{shouldDetach(r){return!1}store(r,e){}shouldAttach(r){return!1}retrieve(r){return null}shouldReuseRoute(r,e){return r.routeConfig===e.routeConfig}},ec=(()=>{class t extends tn{static \u0275fac=(()=>{let e;return function(i){return(e||(e=tr(t)))(i||t)}})();static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ps=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:()=>f(tc),providedIn:"root"})}return t})(),tc=(()=>{class t extends ps{location=f(ke);urlSerializer=f(nt);options=f(at,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";urlHandlingStrategy=f(on);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Y;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}routerState=Zi(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(n=>{n.type==="popstate"&&e(n.url,n.state)})}handleRouterEvent(e,n){if(e instanceof Ce)this.stateMemento=this.createStateMemento();else if(e instanceof re)this.rawUrlTree=n.initialUrl;else if(e instanceof _t){if(this.urlUpdateStrategy==="eager"&&!n.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl);this.setBrowserUrl(n.targetBrowserUrl??i,n)}}else e instanceof Ye?(this.currentUrlTree=n.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl),this.routerState=n.targetRouterState,this.urlUpdateStrategy==="deferred"&&!n.extras.skipLocationChange&&this.setBrowserUrl(n.targetBrowserUrl??this.rawUrlTree,n)):e instanceof X&&(e.code===L.GuardRejected||e.code===L.NoDataFromResolver)?this.restoreHistory(n):e instanceof Xe?this.restoreHistory(n,!0):e instanceof G&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,n){let i=e instanceof Y?this.urlSerializer.serialize(e):e;if(this.location.isCurrentPathEqualTo(i)||n.extras.replaceUrl){let s=this.browserPageId,o=d(d({},n.extras.state),this.generateNgRouterState(n.id,s));this.location.replaceState(i,"",o)}else{let s=d(d({},n.extras.state),this.generateNgRouterState(n.id,this.browserPageId+1));this.location.go(i,"",s)}}restoreHistory(e,n=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.currentUrlTree===e.finalUrl&&s===0&&(this.resetState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(n&&this.resetState(e),this.resetUrlToCurrentUrlTree())}resetState(e){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,n){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:n}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=tr(t)))(i||t)}})();static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function gs(t,r){t.events.pipe(K(e=>e instanceof G||e instanceof X||e instanceof Xe||e instanceof re),y(e=>e instanceof G||e instanceof re?0:(e instanceof X?e.code===L.Redirect||e.code===L.SupersededByNewNavigation:!1)?2:1),K(e=>e!==2),he(1)).subscribe(()=>{r()})}var rc={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},nc={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},H=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=f(ar);stateManager=f(ps);options=f(at,{optional:!0})||{};pendingTasks=f(Pe);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=f(an);urlSerializer=f(nt);location=f(ke);urlHandlingStrategy=f(on);_events=new J;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=f(Qa);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=f(tt,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!f(qt,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new un;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(n=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(n,s),n instanceof X&&n.code!==L.Redirect&&n.code!==L.SupersededByNewNavigation)this.navigated=!0;else if(n instanceof G)this.navigated=!0;else if(n instanceof Ae){let o=n.navigationBehaviorOptions,c=this.urlHandlingStrategy.merge(n.url,i.currentRawUrl),a=d({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Za(i.source)},o);this.scheduleNavigation(c,He,null,a,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}sc(n)&&this._events.next(n)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),He,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,n)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(e,"popstate",n)},0)})}navigateToSyncWithBrowser(e,n,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let a=d({},i);delete a.navigationId,delete a.\u0275routerPageId,Object.keys(a).length!==0&&(s.state=a)}let c=this.parseUrl(e);this.scheduleNavigation(c,n,o,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(nn),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,n={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:c,preserveFragment:a}=n,u=a?this.currentUrlTree.fragment:o,l=null;switch(c??this.options.defaultQueryParamsHandling){case"merge":l=d(d({},this.currentUrlTree.queryParams),s);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=s||null}l!==null&&(l=this.removeEmptyProps(l));let h;try{let g=i?i.snapshot:this.routerState.snapshot.root;h=Ki(g)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),h=this.currentUrlTree.root}return Xi(h,e,l,u??null)}navigateByUrl(e,n={skipLocationChange:!1}){let i=de(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,He,null,n)}navigate(e,n={skipLocationChange:!1}){return ic(e),this.navigateByUrl(this.createUrlTree(e,n),n)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,n){let i;if(n===!0?i=d({},rc):n===!1?i=d({},nc):i=n,de(e))return Di(this.currentUrlTree,e,i);let s=this.parseUrl(e);return Di(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((n,[i,s])=>(s!=null&&(n[i]=s),n),{})}scheduleNavigation(e,n,i,s,o){if(this.disposed)return Promise.resolve(!1);let c,a,u;o?(c=o.resolve,a=o.reject,u=o.promise):u=new Promise((h,g)=>{c=h,a=g});let l=this.pendingTasks.add();return gs(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:n,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:c,reject:a,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(h=>Promise.reject(h))}static \u0275fac=function(n){return new(n||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function ic(t){for(let r=0;r<t.length;r++)if(t[r]==null)throw new E(4008,!1)}function sc(t){return!(t instanceof Ye)&&!(t instanceof Ae)}var Ou=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new J;constructor(e,n,i,s,o,c){this.router=e,this.route=n,this.tabIndexAttribute=i,this.renderer=s,this.el=o,this.locationStrategy=c;let a=o.nativeElement.tagName?.toLowerCase();this.isAnchorElement=a==="a"||a==="area",this.isAnchorElement?this.subscription=e.events.subscribe(u=>{u instanceof G&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(de(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,n,i,s,o){let c=this.urlTree;if(c===null||this.isAnchorElement&&(e!==0||n||i||s||o||typeof this.target=="string"&&this.target!="_self"))return!0;let a={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(c,a),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let n=this.href===null?null:kn(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",n)}applyAttributeValue(e,n){let i=this.renderer,s=this.el.nativeElement;n!==null?i.setAttribute(s,e,n):i.removeAttribute(s,e)}get urlTree(){return this.routerLinkInput===null?null:de(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(n){return new(n||t)(me(H),me(ne),Sn("tabindex"),me(jn),me(Tn),me(_e))};static \u0275dir=or({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(n,i){n&1&&Wn("click",function(o){return i.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),n&2&&Gn("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",mt],skipLocationChange:[2,"skipLocationChange","skipLocationChange",mt],replaceUrl:[2,"replaceUrl","replaceUrl",mt],routerLink:"routerLink"},features:[Vn,er]})}return t})();var Vt=class{};var oc=(()=>{class t{router;injector;preloadingStrategy;loader;subscription;constructor(e,n,i,s,o){this.router=e,this.injector=i,this.preloadingStrategy=s,this.loader=o}setUpPreloading(){this.subscription=this.router.events.pipe(K(e=>e instanceof G),Z(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,n){let i=[];for(let s of n){s.providers&&!s._injector&&(s._injector=sr(s.providers,e,`Route: ${s.path}`));let o=s._injector??e,c=s._loadedInjector??o;(s.loadChildren&&!s._loadedRoutes&&s.canLoad===void 0||s.loadComponent&&!s._loadedComponent)&&i.push(this.preloadConfig(o,s)),(s.children||s._loadedRoutes)&&i.push(this.processRoutes(c,s.children??s._loadedRoutes))}return N(i).pipe(Xt())}preloadConfig(e,n){return this.preloadingStrategy.preload(n,()=>{let i;n.loadChildren&&n.canLoad===void 0?i=this.loader.loadChildren(e,n):i=p(null);let s=i.pipe(x(o=>o===null?p(void 0):(n._loadedRoutes=o.routes,n._loadedInjector=o.injector,this.processRoutes(o.injector??e,o.routes))));if(n.loadComponent&&!n._loadedComponent){let o=this.loader.loadComponent(n);return N([s,o]).pipe(Xt())}else return s})}static \u0275fac=function(n){return new(n||t)(v(H),v(gt),v(oe),v(Vt),v(sn))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ms=new C(""),ac=(()=>{class t{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(e,n,i,s,o={}){this.urlSerializer=e,this.transitions=n,this.viewportScroller=i,this.zone=s,this.options=o,o.scrollPositionRestoration||="disabled",o.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof Ce?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof G?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof re&&e.code===xt.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof kt&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,n){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new kt(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,n))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(n){$n()};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();function Pu(t,...r){return De([{provide:tt,multi:!0,useValue:t},[],{provide:ne,useFactory:vs,deps:[H]},{provide:ft,multi:!0,useFactory:ys},r.map(e=>e.\u0275providers)])}function vs(t){return t.routerState.root}function ct(t,r){return{\u0275kind:t,\u0275providers:r}}function ys(){let t=f(Oe);return r=>{let e=t.get(pt);if(r!==e.components[0])return;let n=t.get(H),i=t.get(ws);t.get(cn)===1&&n.initialNavigation(),t.get(Rs,null,Qt.Optional)?.setUpPreloading(),t.get(ms,null,Qt.Optional)?.init(),n.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var ws=new C("",{factory:()=>new J}),cn=new C("",{providedIn:"root",factory:()=>1});function cc(){return ct(2,[{provide:cn,useValue:0},{provide:cr,multi:!0,deps:[Oe],useFactory:r=>{let e=r.get(Zn,Promise.resolve());return()=>e.then(()=>new Promise(n=>{let i=r.get(H),s=r.get(ws);gs(i,()=>{n(!0)}),r.get(an).afterPreactivation=()=>(n(!0),s.closed?p(void 0):s),i.initialNavigation()}))}}])}function uc(){return ct(3,[{provide:cr,multi:!0,useFactory:()=>{let r=f(H);return()=>{r.setUpLocationChangeListener()}}},{provide:cn,useValue:2}])}var Rs=new C("");function lc(t){return ct(0,[{provide:Rs,useExisting:oc},{provide:Vt,useExisting:t}])}function dc(){return ct(8,[Ui,{provide:qt,useExisting:Ui}])}function hc(t){let r=[{provide:ds,useValue:Ya},{provide:hs,useValue:d({skipNextTransition:!!t?.skipInitialTransition},t)}];return ct(9,r)}var fc=[ke,{provide:nt,useClass:Te},H,st,{provide:ne,useFactory:vs,deps:[H]},sn,[]],Nu=(()=>{class t{constructor(){}static forRoot(e,n){return{ngModule:t,providers:[fc,[],{provide:tt,multi:!0,useValue:e},[],n?.errorHandler?{provide:fs,useValue:n.errorHandler}:[],{provide:at,useValue:n||{}},n?.useHash?gc():mc(),pc(),n?.preloadingStrategy?lc(n.preloadingStrategy).\u0275providers:[],n?.initialNavigation?vc(n):[],n?.bindToComponentInputs?dc().\u0275providers:[],n?.enableViewTransitions?hc().\u0275providers:[],yc()]}}static forChild(e){return{ngModule:t,providers:[{provide:tt,multi:!0,useValue:e}]}}static \u0275fac=function(n){return new(n||t)};static \u0275mod=ht({type:t});static \u0275inj=dt({})}return t})();function pc(){return{provide:ms,useFactory:()=>{let t=f(ri),r=f(ee),e=f(at),n=f(an),i=f(nt);return e.scrollOffset&&t.setOffset(e.scrollOffset),new ac(i,n,t,r,e)}}}function gc(){return{provide:_e,useClass:ei}}function mc(){return{provide:_e,useClass:Qn}}function vc(t){return[t.initialNavigation==="disabled"?uc().\u0275providers:[],t.initialNavigation==="enabledBlocking"?cc().\u0275providers:[]]}var Li=new C("");function yc(){return[{provide:Li,useFactory:ys},{provide:ft,multi:!0,useExisting:Li}]}var Ss={production:!1,apiUrl:"http://localhost:8080/api"};var bs=class t{constructor(r,e){this.http=r;this.router=e;this.loadStoredAuth()}TOKEN_KEY="auth_token";USER_KEY="user_data";apiUrl=Ss.apiUrl;currentUserSubject=new P(null);currentUser$=this.currentUserSubject.asObservable();isAuthenticatedSubject=new P(!1);isAuthenticated$=this.isAuthenticatedSubject.asObservable();loadStoredAuth(){let r=localStorage.getItem(this.TOKEN_KEY),e=localStorage.getItem(this.USER_KEY);if(r&&e)try{let n=JSON.parse(e);this.currentUserSubject.next(n),this.isAuthenticatedSubject.next(!0)}catch(n){console.error("Error parsing stored user data:",n),this.logout()}}login(r){return this.mockLogin(r).pipe(A(e=>{this.storeAuthData(e),this.currentUserSubject.next(e.user),this.isAuthenticatedSubject.next(!0)}),y(e=>e.user),V(e=>(console.error("Login error:",e),$(()=>new Error("Invalid email or password")))))}register(r){return this.mockRegister(r).pipe(A(e=>{this.storeAuthData(e),this.currentUserSubject.next(e.user),this.isAuthenticatedSubject.next(!0)}),y(e=>e.user),V(e=>(console.error("Registration error:",e),$(()=>new Error("Registration failed. Please try again.")))))}logout(){localStorage.removeItem(this.TOKEN_KEY),localStorage.removeItem(this.USER_KEY),this.currentUserSubject.next(null),this.isAuthenticatedSubject.next(!1),this.router.navigate(["/auth/login"])}getToken(){return localStorage.getItem(this.TOKEN_KEY)}getCurrentUser(){return this.currentUserSubject.value}hasRole(r){let e=this.currentUserSubject.value;return!!e&&e.role===r}updateProfile(r){let e=this.currentUserSubject.value;return e?this.mockUpdateProfile(r).pipe(A(n=>{let i=d(d({},e),n);localStorage.setItem(this.USER_KEY,JSON.stringify(i)),this.currentUserSubject.next(i)}),V(n=>(console.error("Profile update error:",n),$(()=>new Error("Failed to update profile. Please try again."))))):$(()=>new Error("User not authenticated"))}storeAuthData(r){localStorage.setItem(this.TOKEN_KEY,r.token),localStorage.setItem(this.USER_KEY,JSON.stringify(r.user))}mockLogin(r){return r.email==="<EMAIL>"&&r.password==="admin123"?p({token:"mock-jwt-token-admin",user:{id:"admin1",username:"admin",email:"<EMAIL>",role:"admin",createdAt:new Date("2022-01-01"),lastLogin:new Date}}):r.email==="<EMAIL>"&&r.password==="provider123"?p({token:"mock-jwt-token-provider",user:{id:"provider1",username:"provider",email:"<EMAIL>",role:"provider",providerName:"Provider A",createdAt:new Date("2022-01-02"),lastLogin:new Date}}):r.email==="<EMAIL>"&&r.password==="viewer123"?p({token:"mock-jwt-token-viewer",user:{id:"viewer1",username:"viewer",email:"<EMAIL>",role:"viewer",createdAt:new Date("2022-01-03"),lastLogin:new Date}}):$(()=>new Error("Invalid email or password"))}mockRegister(r){return p({token:"mock-jwt-token-new-user",user:{id:"user"+Math.floor(Math.random()*1e3),username:r.username,email:r.email,role:r.providerName?"provider":"viewer",providerName:r.providerName,createdAt:new Date,lastLogin:new Date}})}mockUpdateProfile(r){let e=this.currentUserSubject.value;return e?p(O(d(d({},e),r),{lastLogin:new Date})):$(()=>new Error("User not authenticated"))}static \u0275fac=function(e){return new(e||t)(v(vr),v(H))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})};export{te as a,vr as b,js as c,_c as d,kc as e,Si as f,ou as g,So as h,ne as i,ea as j,H as k,Ou as l,Pu as m,Nu as n,Ss as o,bs as p};
