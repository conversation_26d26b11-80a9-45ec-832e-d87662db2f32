import{$ as d,_ as a,aa as s,da as p,ea as l}from"./chunk-HE4KASLF.js";import{Bb as t,Cb as n,Ub as i,fb as o}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var m=class r{static \u0275fac=function(e){return new(e||r)};static \u0275cmp=o({type:r,selectors:[["app-provider-lines"]],decls:9,vars:0,consts:[[1,"provider-lines-container"],[1,"provider-lines-placeholder"]],template:function(e,f){e&1&&(t(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),i(4,"Provider Lines Management"),n()(),t(5,"mat-card-content")(6,"div",1)(7,"p"),i(8,"Provider lines management will be implemented in Phase 5"),n()()()()())},dependencies:[l,a,s,p,d],styles:[".provider-lines-container[_ngcontent-%COMP%]{padding:20px}.provider-lines-placeholder[_ngcontent-%COMP%]{height:300px;display:flex;justify-content:center;align-items:center;background-color:#f5f5f5;border:1px solid #ddd}"]})};export{m as ProviderLinesComponent};
