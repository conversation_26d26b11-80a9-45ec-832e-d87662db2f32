import{a as oi,b as ni}from"./chunk-4N6OITQX.js";import{a as ai,b as ri}from"./chunk-SSOC3NBY.js";import{a as Ie,b as Oe}from"./chunk-QG3LXPLB.js";import{b as si}from"./chunk-7BGM6SBA.js";import{a as ei,d as ti,g as Fe,h as ii}from"./chunk-ORNC4OUU.js";import{b as Kt,f as Zt,i as Xt,p as Jt}from"./chunk-Q6MA6IAZ.js";import{a as Yt,b as Me,c as Wt,d as ge,e as Ee,i as qt,k as Gt}from"./chunk-3OB45MWO.js";import{a as Qt,b as xe,c as Te,d as $t,e as Ut}from"./chunk-QB7XPJNY.js";import{l as Ot,n as Ft}from"./chunk-KGIFXD27.js";import{F as Lt,I as Bt,J as Ht,L as pe,O as Vt,U as jt,a as Pt,k as At,p as zt,x as Nt}from"./chunk-HE4KASLF.js";import{$ as s,$b as Rt,Ab as wt,Bb as l,Cb as d,Db as B,Dc as Tt,Ea as ht,Eb as W,Ec as Mt,Fb as q,Fc as Et,Gb as k,Hb as H,Ia as pt,Jb as b,Jc as It,Kb as f,Lb as le,Mb as Q,Nb as G,Ob as fe,Pb as T,Q as De,Qb as M,Tb as Ct,Ub as g,V as re,Va as c,Vb as bt,W as Z,Wb as z,Xa as Y,Xb as vt,Y as X,Yb as Dt,Zb as St,_a as gt,_b as E,aa as Se,ab as se,ba as Re,bc as kt,f as U,fb as S,g as ne,ga as J,gb as ee,h as Ce,ha as I,hb as h,ia as O,ib as w,ja as te,jc as ce,k as ae,ka as ct,kb as A,kc as de,la as F,lb as _,m as lt,mc as C,na as dt,nc as he,o as be,oa as ut,p as ve,ra as P,rb as L,sa as mt,sb as p,tb as ke,ub as ie,va as j,w as me,wc as xt,xb as R,ya as ft,yb as _t,zb as yt}from"./chunk-BS5MTC5G.js";var Ii=[[["caption"]],[["colgroup"],["col"]],"*"],Oi=["caption","colgroup, col","*"];function Fi(i,n){i&1&&Q(0,2)}function Pi(i,n){i&1&&(l(0,"thead",0),k(1,1),d(),l(2,"tbody",0),k(3,2)(4,3),d(),l(5,"tfoot",0),k(6,4),d())}function Ai(i,n){i&1&&k(0,1)(1,2)(2,3)(3,4)}var V=new X("CDK_TABLE");var He=(()=>{class i{template=s(Y);constructor(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkCellDef",""]]})}return i})(),Ve=(()=>{class i{template=s(Y);constructor(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkHeaderCellDef",""]]})}return i})(),di=(()=>{class i{template=s(Y);constructor(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkFooterCellDef",""]]})}return i})(),ue=(()=>{class i{_table=s(V,{optional:!0});_hasStickyChanged=!1;get name(){return this._name}set name(e){this._setNameInput(e)}_name;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;get stickyEnd(){return this._stickyEnd}set stickyEnd(e){e!==this._stickyEnd&&(this._stickyEnd=e,this._hasStickyChanged=!0)}_stickyEnd=!1;cell;headerCell;footerCell;cssClassFriendlyName;_columnCssClassName;constructor(){}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}_updateColumnCssClassName(){this._columnCssClassName=[`cdk-column-${this.cssClassFriendlyName}`]}_setNameInput(e){e&&(this._name=e,this.cssClassFriendlyName=e.replace(/[^a-z0-9_-]/gi,"-"),this._updateColumnCssClassName())}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkColumnDef",""]],contentQueries:function(t,o,a){if(t&1&&(G(a,He,5),G(a,Ve,5),G(a,di,5)),t&2){let r;T(r=M())&&(o.cell=r.first),T(r=M())&&(o.headerCell=r.first),T(r=M())&&(o.footerCell=r.first)}},inputs:{name:[0,"cdkColumnDef","name"],sticky:[2,"sticky","sticky",C],stickyEnd:[2,"stickyEnd","stickyEnd",C]},features:[E([{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:i}]),A]})}return i})(),Ae=class{constructor(n,e){e.nativeElement.classList.add(...n._columnCssClassName)}},ui=(()=>{class i extends Ae{constructor(){super(s(ue),s(j))}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["cdk-header-cell"],["th","cdk-header-cell",""]],hostAttrs:["role","columnheader",1,"cdk-header-cell"],features:[w]})}return i})();var mi=(()=>{class i extends Ae{constructor(){let e=s(ue),t=s(j);super(e,t);let o=e._table?._getCellRole();o&&t.nativeElement.setAttribute("role",o)}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["cdk-cell"],["td","cdk-cell",""]],hostAttrs:[1,"cdk-cell"],features:[w]})}return i})(),ze=class{tasks=[];endTasks=[]},Ne=new X("_COALESCED_STYLE_SCHEDULER"),Ge=(()=>{class i{_currentSchedule=null;_ngZone=s(mt);constructor(){}schedule(e){this._createScheduleIfNeeded(),this._currentSchedule.tasks.push(e)}scheduleEnd(e){this._createScheduleIfNeeded(),this._currentSchedule.endTasks.push(e)}_createScheduleIfNeeded(){this._currentSchedule||(this._currentSchedule=new ze,this._ngZone.runOutsideAngular(()=>queueMicrotask(()=>{for(;this._currentSchedule.tasks.length||this._currentSchedule.endTasks.length;){let e=this._currentSchedule;this._currentSchedule=new ze;for(let t of e.tasks)t();for(let t of e.endTasks)t()}this._currentSchedule=null})))}static \u0275fac=function(t){return new(t||i)};static \u0275prov=re({token:i,factory:i.\u0275fac})}return i})();var Ke=(()=>{class i{template=s(Y);_differs=s(de);columns;_columnsDiffer;constructor(){}ngOnChanges(e){if(!this._columnsDiffer){let t=e.columns&&e.columns.currentValue||[];this._columnsDiffer=this._differs.find(t).create(),this._columnsDiffer.diff(t)}}getColumnsDiff(){return this._columnsDiffer.diff(this.columns)}extractCellTemplate(e){return this instanceof _e?e.headerCell.template:this instanceof Ze?e.footerCell.template:e.cell.template}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,features:[J]})}return i})(),_e=(()=>{class i extends Ke{_table=s(V,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(Y),s(de))}ngOnChanges(e){super.ngOnChanges(e)}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkHeaderRowDef",""]],inputs:{columns:[0,"cdkHeaderRowDef","columns"],sticky:[2,"cdkHeaderRowDefSticky","sticky",C]},features:[A,w,J]})}return i})(),Ze=(()=>{class i extends Ke{_table=s(V,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(Y),s(de))}ngOnChanges(e){super.ngOnChanges(e)}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkFooterRowDef",""]],inputs:{columns:[0,"cdkFooterRowDef","columns"],sticky:[2,"cdkFooterRowDefSticky","sticky",C]},features:[A,w,J]})}return i})(),je=(()=>{class i extends Ke{_table=s(V,{optional:!0});when;constructor(){super(s(Y),s(de))}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkRowDef",""]],inputs:{columns:[0,"cdkRowDefColumns","columns"],when:[0,"cdkRowDefWhen","when"]},features:[w]})}return i})(),oe=(()=>{class i{_viewContainer=s(se);cells;context;static mostRecentCellOutlet=null;constructor(){i.mostRecentCellOutlet=this}ngOnDestroy(){i.mostRecentCellOutlet===this&&(i.mostRecentCellOutlet=null)}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","cdkCellOutlet",""]]})}return i})(),Xe=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275cmp=S({type:i,selectors:[["cdk-header-row"],["tr","cdk-header-row",""]],hostAttrs:["role","row",1,"cdk-header-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,o){t&1&&k(0,0)},dependencies:[oe],encapsulation:2})}return i})();var Je=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275cmp=S({type:i,selectors:[["cdk-row"],["tr","cdk-row",""]],hostAttrs:["role","row",1,"cdk-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,o){t&1&&k(0,0)},dependencies:[oe],encapsulation:2})}return i})(),Qe=(()=>{class i{templateRef=s(Y);_contentClassName="cdk-no-data-row";constructor(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["ng-template","cdkNoDataRow",""]]})}return i})(),li=["top","bottom","left","right"],qe=class{_isNativeHtmlTable;_stickCellCss;direction;_coalescedStyleScheduler;_isBrowser;_needsPositionStickyOnElement;_positionListener;_tableInjector;_elemSizeCache=new WeakMap;_resizeObserver=globalThis?.ResizeObserver?new globalThis.ResizeObserver(n=>this._updateCachedSizes(n)):null;_updatedStickyColumnsParamsToReplay=[];_stickyColumnsReplayTimeout=null;_cachedCellWidths=[];_borderCellCss;_destroyed=!1;constructor(n,e,t,o,a=!0,r=!0,u,m){this._isNativeHtmlTable=n,this._stickCellCss=e,this.direction=t,this._coalescedStyleScheduler=o,this._isBrowser=a,this._needsPositionStickyOnElement=r,this._positionListener=u,this._tableInjector=m,this._borderCellCss={top:`${e}-border-elem-top`,bottom:`${e}-border-elem-bottom`,left:`${e}-border-elem-left`,right:`${e}-border-elem-right`}}clearStickyPositioning(n,e){(e.includes("left")||e.includes("right"))&&this._removeFromStickyColumnReplayQueue(n);let t=[];for(let o of n)o.nodeType===o.ELEMENT_NODE&&t.push(o,...Array.from(o.children));this._afterNextRender({write:()=>{for(let o of t)this._removeStickyStyle(o,e)}})}updateStickyColumns(n,e,t,o=!0,a=!0){if(!n.length||!this._isBrowser||!(e.some(N=>N)||t.some(N=>N))){this._positionListener?.stickyColumnsUpdated({sizes:[]}),this._positionListener?.stickyEndColumnsUpdated({sizes:[]});return}let r=n[0],u=r.children.length,m=this.direction==="rtl",y=m?"right":"left",D=m?"left":"right",v=e.lastIndexOf(!0),$=t.indexOf(!0),K,at,rt;a&&this._updateStickyColumnReplayQueue({rows:[...n],stickyStartStates:[...e],stickyEndStates:[...t]}),this._afterNextRender({earlyRead:()=>{K=this._getCellWidths(r,o),at=this._getStickyStartColumnPositions(K,e),rt=this._getStickyEndColumnPositions(K,t)},write:()=>{for(let N of n)for(let x=0;x<u;x++){let st=N.children[x];e[x]&&this._addStickyStyle(st,y,at[x],x===v),t[x]&&this._addStickyStyle(st,D,rt[x],x===$)}this._positionListener&&K.some(N=>!!N)&&(this._positionListener.stickyColumnsUpdated({sizes:v===-1?[]:K.slice(0,v+1).map((N,x)=>e[x]?N:null)}),this._positionListener.stickyEndColumnsUpdated({sizes:$===-1?[]:K.slice($).map((N,x)=>t[x+$]?N:null).reverse()}))}})}stickRows(n,e,t){if(!this._isBrowser)return;let o=t==="bottom"?n.slice().reverse():n,a=t==="bottom"?e.slice().reverse():e,r=[],u=[],m=[];this._afterNextRender({earlyRead:()=>{for(let y=0,D=0;y<o.length;y++){if(!a[y])continue;r[y]=D;let v=o[y];m[y]=this._isNativeHtmlTable?Array.from(v.children):[v];let $=this._retrieveElementSize(v).height;D+=$,u[y]=$}},write:()=>{let y=a.lastIndexOf(!0);for(let D=0;D<o.length;D++){if(!a[D])continue;let v=r[D],$=D===y;for(let K of m[D])this._addStickyStyle(K,t,v,$)}t==="top"?this._positionListener?.stickyHeaderRowsUpdated({sizes:u,offsets:r,elements:m}):this._positionListener?.stickyFooterRowsUpdated({sizes:u,offsets:r,elements:m})}})}updateStickyFooterContainer(n,e){this._isNativeHtmlTable&&this._afterNextRender({write:()=>{let t=n.querySelector("tfoot");t&&(e.some(o=>!o)?this._removeStickyStyle(t,["bottom"]):this._addStickyStyle(t,"bottom",0,!1))}})}destroy(){this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._resizeObserver?.disconnect(),this._destroyed=!0}_removeStickyStyle(n,e){if(!n.classList.contains(this._stickCellCss))return;for(let o of e)n.style[o]="",n.classList.remove(this._borderCellCss[o]);li.some(o=>e.indexOf(o)===-1&&n.style[o])?n.style.zIndex=this._getCalculatedZIndex(n):(n.style.zIndex="",this._needsPositionStickyOnElement&&(n.style.position=""),n.classList.remove(this._stickCellCss))}_addStickyStyle(n,e,t,o){n.classList.add(this._stickCellCss),o&&n.classList.add(this._borderCellCss[e]),n.style[e]=`${t}px`,n.style.zIndex=this._getCalculatedZIndex(n),this._needsPositionStickyOnElement&&(n.style.cssText+="position: -webkit-sticky; position: sticky; ")}_getCalculatedZIndex(n){let e={top:100,bottom:10,left:1,right:1},t=0;for(let o of li)n.style[o]&&(t+=e[o]);return t?`${t}`:""}_getCellWidths(n,e=!0){if(!e&&this._cachedCellWidths.length)return this._cachedCellWidths;let t=[],o=n.children;for(let a=0;a<o.length;a++){let r=o[a];t.push(this._retrieveElementSize(r).width)}return this._cachedCellWidths=t,t}_getStickyStartColumnPositions(n,e){let t=[],o=0;for(let a=0;a<n.length;a++)e[a]&&(t[a]=o,o+=n[a]);return t}_getStickyEndColumnPositions(n,e){let t=[],o=0;for(let a=n.length;a>0;a--)e[a]&&(t[a]=o,o+=n[a]);return t}_retrieveElementSize(n){let e=this._elemSizeCache.get(n);if(e)return e;let t=n.getBoundingClientRect(),o={width:t.width,height:t.height};return this._resizeObserver&&(this._elemSizeCache.set(n,o),this._resizeObserver.observe(n,{box:"border-box"})),o}_updateStickyColumnReplayQueue(n){this._removeFromStickyColumnReplayQueue(n.rows),this._stickyColumnsReplayTimeout||this._updatedStickyColumnsParamsToReplay.push(n)}_removeFromStickyColumnReplayQueue(n){let e=new Set(n);for(let t of this._updatedStickyColumnsParamsToReplay)t.rows=t.rows.filter(o=>!e.has(o));this._updatedStickyColumnsParamsToReplay=this._updatedStickyColumnsParamsToReplay.filter(t=>!!t.rows.length)}_updateCachedSizes(n){let e=!1;for(let t of n){let o=t.borderBoxSize?.length?{width:t.borderBoxSize[0].inlineSize,height:t.borderBoxSize[0].blockSize}:{width:t.contentRect.width,height:t.contentRect.height};o.width!==this._elemSizeCache.get(t.target)?.width&&zi(t.target)&&(e=!0),this._elemSizeCache.set(t.target,o)}e&&this._updatedStickyColumnsParamsToReplay.length&&(this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._stickyColumnsReplayTimeout=setTimeout(()=>{if(!this._destroyed){for(let t of this._updatedStickyColumnsParamsToReplay)this.updateStickyColumns(t.rows,t.stickyStartStates,t.stickyEndStates,!0,!1);this._updatedStickyColumnsParamsToReplay=[],this._stickyColumnsReplayTimeout=null}},0))}_afterNextRender(n){this._tableInjector?pt(n,{injector:this._tableInjector}):this._coalescedStyleScheduler.schedule(()=>{n.earlyRead?.(),n.write()})}};function zi(i){return["cdk-cell","cdk-header-cell","cdk-footer-cell"].some(n=>i.classList.contains(n))}var Le=new X("CDK_SPL");var et=(()=>{class i{viewContainer=s(se);elementRef=s(j);constructor(){let e=s(V);e._rowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","rowOutlet",""]]})}return i})(),tt=(()=>{class i{viewContainer=s(se);elementRef=s(j);constructor(){let e=s(V);e._headerRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","headerRowOutlet",""]]})}return i})(),it=(()=>{class i{viewContainer=s(se);elementRef=s(j);constructor(){let e=s(V);e._footerRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","footerRowOutlet",""]]})}return i})(),ot=(()=>{class i{viewContainer=s(se);elementRef=s(j);constructor(){let e=s(V);e._noDataRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=h({type:i,selectors:[["","noDataRowOutlet",""]]})}return i})();var nt=(()=>{class i{_differs=s(de);_changeDetectorRef=s(ce);_elementRef=s(j);_dir=s(Ht,{optional:!0});_platform=s(Pt);_viewRepeater=s(ge);_coalescedStyleScheduler=s(Ne);_viewportRuler=s(qt);_stickyPositioningListener=s(Le,{optional:!0,skipSelf:!0});_document=s(xt);_data;_onDestroy=new U;_renderRows;_renderChangeSubscription;_columnDefsByName=new Map;_rowDefs;_headerRowDefs;_footerRowDefs;_dataDiffer;_defaultRowDef;_customColumnDefs=new Set;_customRowDefs=new Set;_customHeaderRowDefs=new Set;_customFooterRowDefs=new Set;_customNoDataRow;_headerRowDefChanged=!0;_footerRowDefChanged=!0;_stickyColumnStylesNeedReset=!0;_forceRecalculateCellWidths=!0;_cachedRenderRowsMap=new Map;_isNativeHtmlTable;_stickyStyler;stickyCssClass="cdk-table-sticky";needsPositionStickyOnElement=!0;_isServer;_isShowingNoDataRow=!1;_hasAllOutlets=!1;_hasInitialized=!1;_getCellRole(){if(this._cellRoleInternal===void 0){let e=this._elementRef.nativeElement.getAttribute("role");return e==="grid"||e==="treegrid"?"gridcell":"cell"}return this._cellRoleInternal}_cellRoleInternal=void 0;get trackBy(){return this._trackByFn}set trackBy(e){this._trackByFn=e}_trackByFn;get dataSource(){return this._dataSource}set dataSource(e){this._dataSource!==e&&this._switchDataSource(e)}_dataSource;get multiTemplateDataRows(){return this._multiTemplateDataRows}set multiTemplateDataRows(e){this._multiTemplateDataRows=e,this._rowOutlet&&this._rowOutlet.viewContainer.length&&(this._forceRenderDataRows(),this.updateStickyColumnStyles())}_multiTemplateDataRows=!1;get fixedLayout(){return this._fixedLayout}set fixedLayout(e){this._fixedLayout=e,this._forceRecalculateCellWidths=!0,this._stickyColumnStylesNeedReset=!0}_fixedLayout=!1;contentChanged=new P;viewChange=new ne({start:0,end:Number.MAX_VALUE});_rowOutlet;_headerRowOutlet;_footerRowOutlet;_noDataRowOutlet;_contentColumnDefs;_contentRowDefs;_contentHeaderRowDefs;_contentFooterRowDefs;_noDataRow;_injector=s(dt);constructor(){s(new ut("role"),{optional:!0})||this._elementRef.nativeElement.setAttribute("role","table"),this._isServer=!this._platform.isBrowser,this._isNativeHtmlTable=this._elementRef.nativeElement.nodeName==="TABLE"}ngOnInit(){this._setupStickyStyler(),this._dataDiffer=this._differs.find([]).create((e,t)=>this.trackBy?this.trackBy(t.dataIndex,t.data):t),this._viewportRuler.change().pipe(De(this._onDestroy)).subscribe(()=>{this._forceRecalculateCellWidths=!0})}ngAfterContentInit(){this._hasInitialized=!0}ngAfterContentChecked(){this._canRender()&&this._render()}ngOnDestroy(){this._stickyStyler?.destroy(),[this._rowOutlet?.viewContainer,this._headerRowOutlet?.viewContainer,this._footerRowOutlet?.viewContainer,this._cachedRenderRowsMap,this._customColumnDefs,this._customRowDefs,this._customHeaderRowDefs,this._customFooterRowDefs,this._columnDefsByName].forEach(e=>{e?.clear()}),this._headerRowDefs=[],this._footerRowDefs=[],this._defaultRowDef=null,this._onDestroy.next(),this._onDestroy.complete(),Me(this.dataSource)&&this.dataSource.disconnect(this)}renderRows(){this._renderRows=this._getAllRenderRows();let e=this._dataDiffer.diff(this._renderRows);if(!e){this._updateNoDataRow(),this.contentChanged.next();return}let t=this._rowOutlet.viewContainer;this._viewRepeater.applyChanges(e,t,(o,a,r)=>this._getEmbeddedViewArgs(o.item,r),o=>o.item.data,o=>{o.operation===Wt.INSERTED&&o.context&&this._renderCellTemplateForItem(o.record.item.rowDef,o.context)}),this._updateRowIndexContext(),e.forEachIdentityChange(o=>{let a=t.get(o.currentIndex);a.context.$implicit=o.item.data}),this._updateNoDataRow(),this.contentChanged.next(),this.updateStickyColumnStyles()}addColumnDef(e){this._customColumnDefs.add(e)}removeColumnDef(e){this._customColumnDefs.delete(e)}addRowDef(e){this._customRowDefs.add(e)}removeRowDef(e){this._customRowDefs.delete(e)}addHeaderRowDef(e){this._customHeaderRowDefs.add(e),this._headerRowDefChanged=!0}removeHeaderRowDef(e){this._customHeaderRowDefs.delete(e),this._headerRowDefChanged=!0}addFooterRowDef(e){this._customFooterRowDefs.add(e),this._footerRowDefChanged=!0}removeFooterRowDef(e){this._customFooterRowDefs.delete(e),this._footerRowDefChanged=!0}setNoDataRow(e){this._customNoDataRow=e}updateStickyHeaderRowStyles(){let e=this._getRenderedRows(this._headerRowOutlet);if(this._isNativeHtmlTable){let o=ci(this._headerRowOutlet,"thead");o&&(o.style.display=e.length?"":"none")}let t=this._headerRowDefs.map(o=>o.sticky);this._stickyStyler.clearStickyPositioning(e,["top"]),this._stickyStyler.stickRows(e,t,"top"),this._headerRowDefs.forEach(o=>o.resetStickyChanged())}updateStickyFooterRowStyles(){let e=this._getRenderedRows(this._footerRowOutlet);if(this._isNativeHtmlTable){let o=ci(this._footerRowOutlet,"tfoot");o&&(o.style.display=e.length?"":"none")}let t=this._footerRowDefs.map(o=>o.sticky);this._stickyStyler.clearStickyPositioning(e,["bottom"]),this._stickyStyler.stickRows(e,t,"bottom"),this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement,t),this._footerRowDefs.forEach(o=>o.resetStickyChanged())}updateStickyColumnStyles(){let e=this._getRenderedRows(this._headerRowOutlet),t=this._getRenderedRows(this._rowOutlet),o=this._getRenderedRows(this._footerRowOutlet);(this._isNativeHtmlTable&&!this._fixedLayout||this._stickyColumnStylesNeedReset)&&(this._stickyStyler.clearStickyPositioning([...e,...t,...o],["left","right"]),this._stickyColumnStylesNeedReset=!1),e.forEach((a,r)=>{this._addStickyColumnStyles([a],this._headerRowDefs[r])}),this._rowDefs.forEach(a=>{let r=[];for(let u=0;u<t.length;u++)this._renderRows[u].rowDef===a&&r.push(t[u]);this._addStickyColumnStyles(r,a)}),o.forEach((a,r)=>{this._addStickyColumnStyles([a],this._footerRowDefs[r])}),Array.from(this._columnDefsByName.values()).forEach(a=>a.resetStickyChanged())}_outletAssigned(){!this._hasAllOutlets&&this._rowOutlet&&this._headerRowOutlet&&this._footerRowOutlet&&this._noDataRowOutlet&&(this._hasAllOutlets=!0,this._canRender()&&this._render())}_canRender(){return this._hasAllOutlets&&this._hasInitialized}_render(){this._cacheRowDefs(),this._cacheColumnDefs(),!this._headerRowDefs.length&&!this._footerRowDefs.length&&this._rowDefs.length;let t=this._renderUpdatedColumns()||this._headerRowDefChanged||this._footerRowDefChanged;this._stickyColumnStylesNeedReset=this._stickyColumnStylesNeedReset||t,this._forceRecalculateCellWidths=t,this._headerRowDefChanged&&(this._forceRenderHeaderRows(),this._headerRowDefChanged=!1),this._footerRowDefChanged&&(this._forceRenderFooterRows(),this._footerRowDefChanged=!1),this.dataSource&&this._rowDefs.length>0&&!this._renderChangeSubscription?this._observeRenderChanges():this._stickyColumnStylesNeedReset&&this.updateStickyColumnStyles(),this._checkStickyStates()}_getAllRenderRows(){let e=[],t=this._cachedRenderRowsMap;this._cachedRenderRowsMap=new Map;for(let o=0;o<this._data.length;o++){let a=this._data[o],r=this._getRenderRowsForData(a,o,t.get(a));this._cachedRenderRowsMap.has(a)||this._cachedRenderRowsMap.set(a,new WeakMap);for(let u=0;u<r.length;u++){let m=r[u],y=this._cachedRenderRowsMap.get(m.data);y.has(m.rowDef)?y.get(m.rowDef).push(m):y.set(m.rowDef,[m]),e.push(m)}}return e}_getRenderRowsForData(e,t,o){return this._getRowDefs(e,t).map(r=>{let u=o&&o.has(r)?o.get(r):[];if(u.length){let m=u.shift();return m.dataIndex=t,m}else return{data:e,rowDef:r,dataIndex:t}})}_cacheColumnDefs(){this._columnDefsByName.clear(),Pe(this._getOwnDefs(this._contentColumnDefs),this._customColumnDefs).forEach(t=>{this._columnDefsByName.has(t.name),this._columnDefsByName.set(t.name,t)})}_cacheRowDefs(){this._headerRowDefs=Pe(this._getOwnDefs(this._contentHeaderRowDefs),this._customHeaderRowDefs),this._footerRowDefs=Pe(this._getOwnDefs(this._contentFooterRowDefs),this._customFooterRowDefs),this._rowDefs=Pe(this._getOwnDefs(this._contentRowDefs),this._customRowDefs);let e=this._rowDefs.filter(t=>!t.when);!this.multiTemplateDataRows&&e.length>1,this._defaultRowDef=e[0]}_renderUpdatedColumns(){let e=(r,u)=>{let m=!!u.getColumnsDiff();return r||m},t=this._rowDefs.reduce(e,!1);t&&this._forceRenderDataRows();let o=this._headerRowDefs.reduce(e,!1);o&&this._forceRenderHeaderRows();let a=this._footerRowDefs.reduce(e,!1);return a&&this._forceRenderFooterRows(),t||o||a}_switchDataSource(e){this._data=[],Me(this.dataSource)&&this.dataSource.disconnect(this),this._renderChangeSubscription&&(this._renderChangeSubscription.unsubscribe(),this._renderChangeSubscription=null),e||(this._dataDiffer&&this._dataDiffer.diff([]),this._rowOutlet&&this._rowOutlet.viewContainer.clear()),this._dataSource=e}_observeRenderChanges(){if(!this.dataSource)return;let e;Me(this.dataSource)?e=this.dataSource.connect(this):lt(this.dataSource)?e=this.dataSource:Array.isArray(this.dataSource)&&(e=ae(this.dataSource)),this._renderChangeSubscription=e.pipe(De(this._onDestroy)).subscribe(t=>{this._data=t||[],this.renderRows()})}_forceRenderHeaderRows(){this._headerRowOutlet.viewContainer.length>0&&this._headerRowOutlet.viewContainer.clear(),this._headerRowDefs.forEach((e,t)=>this._renderRow(this._headerRowOutlet,e,t)),this.updateStickyHeaderRowStyles()}_forceRenderFooterRows(){this._footerRowOutlet.viewContainer.length>0&&this._footerRowOutlet.viewContainer.clear(),this._footerRowDefs.forEach((e,t)=>this._renderRow(this._footerRowOutlet,e,t)),this.updateStickyFooterRowStyles()}_addStickyColumnStyles(e,t){let o=Array.from(t?.columns||[]).map(u=>{let m=this._columnDefsByName.get(u);return m}),a=o.map(u=>u.sticky),r=o.map(u=>u.stickyEnd);this._stickyStyler.updateStickyColumns(e,a,r,!this._fixedLayout||this._forceRecalculateCellWidths)}_getRenderedRows(e){let t=[];for(let o=0;o<e.viewContainer.length;o++){let a=e.viewContainer.get(o);t.push(a.rootNodes[0])}return t}_getRowDefs(e,t){if(this._rowDefs.length==1)return[this._rowDefs[0]];let o=[];if(this.multiTemplateDataRows)o=this._rowDefs.filter(a=>!a.when||a.when(t,e));else{let a=this._rowDefs.find(r=>r.when&&r.when(t,e))||this._defaultRowDef;a&&o.push(a)}return o.length,o}_getEmbeddedViewArgs(e,t){let o=e.rowDef,a={$implicit:e.data};return{templateRef:o.template,context:a,index:t}}_renderRow(e,t,o,a={}){let r=e.viewContainer.createEmbeddedView(t.template,a,o);return this._renderCellTemplateForItem(t,a),r}_renderCellTemplateForItem(e,t){for(let o of this._getCellTemplates(e))oe.mostRecentCellOutlet&&oe.mostRecentCellOutlet._viewContainer.createEmbeddedView(o,t);this._changeDetectorRef.markForCheck()}_updateRowIndexContext(){let e=this._rowOutlet.viewContainer;for(let t=0,o=e.length;t<o;t++){let r=e.get(t).context;r.count=o,r.first=t===0,r.last=t===o-1,r.even=t%2===0,r.odd=!r.even,this.multiTemplateDataRows?(r.dataIndex=this._renderRows[t].dataIndex,r.renderIndex=t):r.index=this._renderRows[t].dataIndex}}_getCellTemplates(e){return!e||!e.columns?[]:Array.from(e.columns,t=>{let o=this._columnDefsByName.get(t);return e.extractCellTemplate(o)})}_forceRenderDataRows(){this._dataDiffer.diff([]),this._rowOutlet.viewContainer.clear(),this.renderRows()}_checkStickyStates(){let e=(t,o)=>t||o.hasStickyChanged();this._headerRowDefs.reduce(e,!1)&&this.updateStickyHeaderRowStyles(),this._footerRowDefs.reduce(e,!1)&&this.updateStickyFooterRowStyles(),Array.from(this._columnDefsByName.values()).reduce(e,!1)&&(this._stickyColumnStylesNeedReset=!0,this.updateStickyColumnStyles())}_setupStickyStyler(){let e=this._dir?this._dir.value:"ltr";this._stickyStyler=new qe(this._isNativeHtmlTable,this.stickyCssClass,e,this._coalescedStyleScheduler,this._platform.isBrowser,this.needsPositionStickyOnElement,this._stickyPositioningListener,this._injector),(this._dir?this._dir.change:ae()).pipe(De(this._onDestroy)).subscribe(t=>{this._stickyStyler.direction=t,this.updateStickyColumnStyles()})}_getOwnDefs(e){return e.filter(t=>!t._table||t._table===this)}_updateNoDataRow(){let e=this._customNoDataRow||this._noDataRow;if(!e)return;let t=this._rowOutlet.viewContainer.length===0;if(t===this._isShowingNoDataRow)return;let o=this._noDataRowOutlet.viewContainer;if(t){let a=o.createEmbeddedView(e.templateRef),r=a.rootNodes[0];a.rootNodes.length===1&&r?.nodeType===this._document.ELEMENT_NODE&&(r.setAttribute("role","row"),r.classList.add(e._contentClassName))}else o.clear();this._isShowingNoDataRow=t,this._changeDetectorRef.markForCheck()}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=S({type:i,selectors:[["cdk-table"],["table","cdk-table",""]],contentQueries:function(t,o,a){if(t&1&&(G(a,Qe,5),G(a,ue,5),G(a,je,5),G(a,_e,5),G(a,Ze,5)),t&2){let r;T(r=M())&&(o._noDataRow=r.first),T(r=M())&&(o._contentColumnDefs=r),T(r=M())&&(o._contentRowDefs=r),T(r=M())&&(o._contentHeaderRowDefs=r),T(r=M())&&(o._contentFooterRowDefs=r)}},hostAttrs:[1,"cdk-table"],hostVars:2,hostBindings:function(t,o){t&2&&ie("cdk-table-fixed-layout",o.fixedLayout)},inputs:{trackBy:"trackBy",dataSource:"dataSource",multiTemplateDataRows:[2,"multiTemplateDataRows","multiTemplateDataRows",C],fixedLayout:[2,"fixedLayout","fixedLayout",C]},outputs:{contentChanged:"contentChanged"},exportAs:["cdkTable"],features:[E([{provide:V,useExisting:i},{provide:ge,useClass:Ee},{provide:Ne,useClass:Ge},{provide:Le,useValue:null}]),A],ngContentSelectors:Oi,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(t,o){t&1&&(le(Ii),Q(0),Q(1,1),_(2,Fi,1,0)(3,Pi,7,0)(4,Ai,4,0)),t&2&&(c(2),R(o._isServer?2:-1),c(),R(o._isNativeHtmlTable?3:4))},dependencies:[tt,et,ot,it],styles:[".cdk-table-fixed-layout{table-layout:fixed}"],encapsulation:2})}return i})();function Pe(i,n){return i.concat(Array.from(n))}function ci(i,n){let e=n.toUpperCase(),t=i.viewContainer.element.nativeElement;for(;t;){let o=t.nodeType===1?t.nodeName:null;if(o===e)return t;if(o==="TABLE")break;t=t.parentNode}return null}var fi=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=ee({type:i});static \u0275inj=Z({imports:[Gt]})}return i})();var Ni=[[["caption"]],[["colgroup"],["col"]],"*"],Li=["caption","colgroup, col","*"];function Bi(i,n){i&1&&Q(0,2)}function Hi(i,n){i&1&&(l(0,"thead",0),k(1,1),d(),l(2,"tbody",2),k(3,3)(4,4),d(),l(5,"tfoot",0),k(6,5),d())}function Vi(i,n){i&1&&k(0,1)(1,3)(2,4)(3,5)}var Ue=(()=>{class i extends nt{stickyCssClass="mat-mdc-table-sticky";needsPositionStickyOnElement=!1;static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275cmp=S({type:i,selectors:[["mat-table"],["table","mat-table",""]],hostAttrs:[1,"mat-mdc-table","mdc-data-table__table"],hostVars:2,hostBindings:function(t,o){t&2&&ie("mdc-table-fixed-layout",o.fixedLayout)},exportAs:["matTable"],features:[E([{provide:nt,useExisting:i},{provide:V,useExisting:i},{provide:Ne,useClass:Ge},{provide:ge,useClass:Ee},{provide:Le,useValue:null}]),w],ngContentSelectors:Li,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["role","rowgroup",1,"mdc-data-table__content"],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(t,o){t&1&&(le(Ni),Q(0),Q(1,1),_(2,Bi,1,0)(3,Hi,7,0)(4,Vi,4,0)),t&2&&(c(2),R(o._isServer?2:-1),c(),R(o._isNativeHtmlTable?3:4))},dependencies:[tt,et,ot,it],styles:[".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}"],encapsulation:2})}return i})(),hi=(()=>{class i extends He{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["","matCellDef",""]],features:[E([{provide:He,useExisting:i}]),w]})}return i})(),pi=(()=>{class i extends Ve{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["","matHeaderCellDef",""]],features:[E([{provide:Ve,useExisting:i}]),w]})}return i})();var gi=(()=>{class i extends ue{get name(){return this._name}set name(e){this._setNameInput(e)}_updateColumnCssClassName(){super._updateColumnCssClassName(),this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`)}static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["","matColumnDef",""]],inputs:{name:[0,"matColumnDef","name"]},features:[E([{provide:ue,useExisting:i},{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:i}]),w]})}return i})(),_i=(()=>{class i extends ui{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["mat-header-cell"],["th","mat-header-cell",""]],hostAttrs:["role","columnheader",1,"mat-mdc-header-cell","mdc-data-table__header-cell"],features:[w]})}return i})();var yi=(()=>{class i extends mi{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["mat-cell"],["td","mat-cell",""]],hostAttrs:[1,"mat-mdc-cell","mdc-data-table__cell"],features:[w]})}return i})();var wi=(()=>{class i extends _e{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["","matHeaderRowDef",""]],inputs:{columns:[0,"matHeaderRowDef","columns"],sticky:[2,"matHeaderRowDefSticky","sticky",C]},features:[E([{provide:_e,useExisting:i}]),A,w]})}return i})();var Ci=(()=>{class i extends je{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["","matRowDef",""]],inputs:{columns:[0,"matRowDefColumns","columns"],when:[0,"matRowDefWhen","when"]},features:[E([{provide:je,useExisting:i}]),w]})}return i})(),bi=(()=>{class i extends Xe{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275cmp=S({type:i,selectors:[["mat-header-row"],["tr","mat-header-row",""]],hostAttrs:["role","row",1,"mat-mdc-header-row","mdc-data-table__header-row"],exportAs:["matHeaderRow"],features:[E([{provide:Xe,useExisting:i}]),w],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,o){t&1&&k(0,0)},dependencies:[oe],encapsulation:2})}return i})();var vi=(()=>{class i extends Je{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275cmp=S({type:i,selectors:[["mat-row"],["tr","mat-row",""]],hostAttrs:["role","row",1,"mat-mdc-row","mdc-data-table__row"],exportAs:["matRow"],features:[E([{provide:Je,useExisting:i}]),w],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,o){t&1&&k(0,0)},dependencies:[oe],encapsulation:2})}return i})(),Di=(()=>{class i extends Qe{_contentClassName="mat-mdc-no-data-row";static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(i)))(o||i)}})();static \u0275dir=h({type:i,selectors:[["ng-template","matNoDataRow",""]],features:[E([{provide:Qe,useExisting:i}]),w]})}return i})();var Si=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=ee({type:i});static \u0275inj=Z({imports:[pe,fi,pe]})}return i})(),ji=9007199254740991,$e=class extends Yt{_data;_renderData=new ne([]);_filter=new ne("");_internalPageChanges=new U;_renderChangesSubscription=null;filteredData;get data(){return this._data.value}set data(n){n=Array.isArray(n)?n:[],this._data.next(n),this._renderChangesSubscription||this._filterData(n)}get filter(){return this._filter.value}set filter(n){this._filter.next(n),this._renderChangesSubscription||this._filterData(this.data)}get sort(){return this._sort}set sort(n){this._sort=n,this._updateChangeSubscription()}_sort;get paginator(){return this._paginator}set paginator(n){this._paginator=n,this._updateChangeSubscription()}_paginator;sortingDataAccessor=(n,e)=>{let t=n[e];if(zt(t)){let o=Number(t);return o<ji?o:t}return t};sortData=(n,e)=>{let t=e.active,o=e.direction;return!t||o==""?n:n.sort((a,r)=>{let u=this.sortingDataAccessor(a,t),m=this.sortingDataAccessor(r,t),y=typeof u,D=typeof m;y!==D&&(y==="number"&&(u+=""),D==="number"&&(m+=""));let v=0;return u!=null&&m!=null?u>m?v=1:u<m&&(v=-1):u!=null?v=1:m!=null&&(v=-1),v*(o=="asc"?1:-1)})};filterPredicate=(n,e)=>{let t=e.trim().toLowerCase();return Object.values(n).some(o=>`${o}`.toLowerCase().includes(t))};constructor(n=[]){super(),this._data=new ne(n),this._updateChangeSubscription()}_updateChangeSubscription(){let n=this._sort?me(this._sort.sortChange,this._sort.initialized):ae(null),e=this._paginator?me(this._paginator.page,this._internalPageChanges,this._paginator.initialized):ae(null),t=this._data,o=ve([t,this._filter]).pipe(be(([u])=>this._filterData(u))),a=ve([o,n]).pipe(be(([u])=>this._orderData(u))),r=ve([a,e]).pipe(be(([u])=>this._pageData(u)));this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=r.subscribe(u=>this._renderData.next(u))}_filterData(n){return this.filteredData=this.filter==null||this.filter===""?n:n.filter(e=>this.filterPredicate(e,this.filter)),this.paginator&&this._updatePaginator(this.filteredData.length),this.filteredData}_orderData(n){return this.sort?this.sortData(n.slice(),this.sort):n}_pageData(n){if(!this.paginator)return n;let e=this.paginator.pageIndex*this.paginator.pageSize;return n.slice(e,e+this.paginator.pageSize)}_updatePaginator(n){Promise.resolve().then(()=>{let e=this.paginator;if(e&&(e.length=n,e.pageIndex>0)){let t=Math.ceil(e.length/e.pageSize)-1||0,o=Math.min(e.pageIndex,t);o!==e.pageIndex&&(e.pageIndex=o,this._internalPageChanges.next())}})}connect(){return this._renderChangesSubscription||this._updateChangeSubscription(),this._renderData}disconnect(){this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=null}};function $i(i,n){if(i&1&&(l(0,"mat-option",17),g(1),d()),i&2){let e=n.$implicit;p("value",e),c(),z(" ",e," ")}}function Ui(i,n){if(i&1){let e=H();l(0,"mat-form-field",14)(1,"mat-select",16,0),b("selectionChange",function(o){I(e);let a=f(2);return O(a._changePageSize(o.value))}),yt(3,$i,2,2,"mat-option",17,_t),d(),l(5,"div",18),b("click",function(){I(e);let o=Ct(2);return O(o.open())}),d()()}if(i&2){let e=f(2);p("appearance",e._formFieldAppearance)("color",e.color),c(),p("value",e.pageSize)("disabled",e.disabled)("aria-labelledby",e._pageSizeLabelId)("panelClass",e.selectConfig.panelClass||"")("disableOptionCentering",e.selectConfig.disableOptionCentering),c(2),wt(e._displayedPageSizeOptions)}}function Yi(i,n){if(i&1&&(l(0,"div",15),g(1),d()),i&2){let e=f(2);c(),bt(e.pageSize)}}function Wi(i,n){if(i&1&&(l(0,"div",3)(1,"div",13),g(2),d(),_(3,Ui,6,7,"mat-form-field",14)(4,Yi,2,1,"div",15),d()),i&2){let e=f();c(),L("id",e._pageSizeLabelId),c(),z(" ",e._intl.itemsPerPageLabel," "),c(),R(e._displayedPageSizeOptions.length>1?3:-1),c(),R(e._displayedPageSizeOptions.length<=1?4:-1)}}function qi(i,n){if(i&1){let e=H();l(0,"button",19),b("click",function(){I(e);let o=f();return O(o._buttonClicked(0,o._previousButtonsDisabled()))}),te(),l(1,"svg",8),B(2,"path",20),d()()}if(i&2){let e=f();p("matTooltip",e._intl.firstPageLabel)("matTooltipDisabled",e._previousButtonsDisabled())("disabled",e._previousButtonsDisabled()),L("aria-label",e._intl.firstPageLabel)}}function Gi(i,n){if(i&1){let e=H();l(0,"button",21),b("click",function(){I(e);let o=f();return O(o._buttonClicked(o.getNumberOfPages()-1,o._nextButtonsDisabled()))}),te(),l(1,"svg",8),B(2,"path",22),d()()}if(i&2){let e=f();p("matTooltip",e._intl.lastPageLabel)("matTooltipDisabled",e._nextButtonsDisabled())("disabled",e._nextButtonsDisabled()),L("aria-label",e._intl.lastPageLabel)}}var Ye=(()=>{class i{changes=new U;itemsPerPageLabel="Items per page:";nextPageLabel="Next page";previousPageLabel="Previous page";firstPageLabel="First page";lastPageLabel="Last page";getRangeLabel=(e,t,o)=>{if(o==0||t==0)return`0 of ${o}`;o=Math.max(o,0);let a=e*t,r=a<o?Math.min(a+t,o):a+t;return`${a+1} \u2013 ${r} of ${o}`};static \u0275fac=function(t){return new(t||i)};static \u0275prov=re({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();function Ki(i){return i||new Ye}var Zi={provide:Ye,deps:[[new Se,new Re,Ye]],useFactory:Ki},Xi=50;var Ji=new X("MAT_PAGINATOR_DEFAULT_OPTIONS"),ye=(()=>{class i{_intl=s(Ye);_changeDetectorRef=s(ce);_formFieldAppearance;_pageSizeLabelId=s(Bt).getId("mat-paginator-page-size-label-");_intlChanges;_isInitialized=!1;_initializedStream=new Ce(1);color;get pageIndex(){return this._pageIndex}set pageIndex(e){this._pageIndex=Math.max(e||0,0),this._changeDetectorRef.markForCheck()}_pageIndex=0;get length(){return this._length}set length(e){this._length=e||0,this._changeDetectorRef.markForCheck()}_length=0;get pageSize(){return this._pageSize}set pageSize(e){this._pageSize=Math.max(e||0,0),this._updateDisplayedPageSizeOptions()}_pageSize;get pageSizeOptions(){return this._pageSizeOptions}set pageSizeOptions(e){this._pageSizeOptions=(e||[]).map(t=>he(t,0)),this._updateDisplayedPageSizeOptions()}_pageSizeOptions=[];hidePageSize=!1;showFirstLastButtons=!1;selectConfig={};disabled=!1;page=new P;_displayedPageSizeOptions;initialized=this._initializedStream;constructor(){let e=this._intl,t=s(Ji,{optional:!0});if(this._intlChanges=e.changes.subscribe(()=>this._changeDetectorRef.markForCheck()),t){let{pageSize:o,pageSizeOptions:a,hidePageSize:r,showFirstLastButtons:u}=t;o!=null&&(this._pageSize=o),a!=null&&(this._pageSizeOptions=a),r!=null&&(this.hidePageSize=r),u!=null&&(this.showFirstLastButtons=u)}this._formFieldAppearance=t?.formFieldAppearance||"outline"}ngOnInit(){this._isInitialized=!0,this._updateDisplayedPageSizeOptions(),this._initializedStream.next()}ngOnDestroy(){this._initializedStream.complete(),this._intlChanges.unsubscribe()}nextPage(){this.hasNextPage()&&this._navigate(this.pageIndex+1)}previousPage(){this.hasPreviousPage()&&this._navigate(this.pageIndex-1)}firstPage(){this.hasPreviousPage()&&this._navigate(0)}lastPage(){this.hasNextPage()&&this._navigate(this.getNumberOfPages()-1)}hasPreviousPage(){return this.pageIndex>=1&&this.pageSize!=0}hasNextPage(){let e=this.getNumberOfPages()-1;return this.pageIndex<e&&this.pageSize!=0}getNumberOfPages(){return this.pageSize?Math.ceil(this.length/this.pageSize):0}_changePageSize(e){let t=this.pageIndex*this.pageSize,o=this.pageIndex;this.pageIndex=Math.floor(t/e)||0,this.pageSize=e,this._emitPageEvent(o)}_nextButtonsDisabled(){return this.disabled||!this.hasNextPage()}_previousButtonsDisabled(){return this.disabled||!this.hasPreviousPage()}_updateDisplayedPageSizeOptions(){this._isInitialized&&(this.pageSize||(this._pageSize=this.pageSizeOptions.length!=0?this.pageSizeOptions[0]:Xi),this._displayedPageSizeOptions=this.pageSizeOptions.slice(),this._displayedPageSizeOptions.indexOf(this.pageSize)===-1&&this._displayedPageSizeOptions.push(this.pageSize),this._displayedPageSizeOptions.sort((e,t)=>e-t),this._changeDetectorRef.markForCheck())}_emitPageEvent(e){this.page.emit({previousPageIndex:e,pageIndex:this.pageIndex,pageSize:this.pageSize,length:this.length})}_navigate(e){let t=this.pageIndex;e!==t&&(this.pageIndex=e,this._emitPageEvent(t))}_buttonClicked(e,t){t||this._navigate(e)}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=S({type:i,selectors:[["mat-paginator"]],hostAttrs:["role","group",1,"mat-mdc-paginator"],inputs:{color:"color",pageIndex:[2,"pageIndex","pageIndex",he],length:[2,"length","length",he],pageSize:[2,"pageSize","pageSize",he],pageSizeOptions:"pageSizeOptions",hidePageSize:[2,"hidePageSize","hidePageSize",C],showFirstLastButtons:[2,"showFirstLastButtons","showFirstLastButtons",C],selectConfig:"selectConfig",disabled:[2,"disabled","disabled",C]},outputs:{page:"page"},exportAs:["matPaginator"],features:[A],decls:14,vars:12,consts:[["selectRef",""],[1,"mat-mdc-paginator-outer-container"],[1,"mat-mdc-paginator-container"],[1,"mat-mdc-paginator-page-size"],[1,"mat-mdc-paginator-range-actions"],["aria-live","polite",1,"mat-mdc-paginator-range-label"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"matTooltip","matTooltipDisabled","disabled"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-previous",3,"click","matTooltip","matTooltipDisabled","disabled"],["viewBox","0 0 24 24","focusable","false","aria-hidden","true",1,"mat-mdc-paginator-icon"],["d","M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-next",3,"click","matTooltip","matTooltipDisabled","disabled"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"matTooltip","matTooltipDisabled","disabled"],[1,"mat-mdc-paginator-page-size-label"],[1,"mat-mdc-paginator-page-size-select",3,"appearance","color"],[1,"mat-mdc-paginator-page-size-value"],["hideSingleSelectionIndicator","",3,"selectionChange","value","disabled","aria-labelledby","panelClass","disableOptionCentering"],[3,"value"],[1,"mat-mdc-paginator-touch-target",3,"click"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"click","matTooltip","matTooltipDisabled","disabled"],["d","M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"click","matTooltip","matTooltipDisabled","disabled"],["d","M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"]],template:function(t,o){t&1&&(l(0,"div",1)(1,"div",2),_(2,Wi,5,4,"div",3),l(3,"div",4)(4,"div",5),g(5),d(),_(6,qi,3,4,"button",6),l(7,"button",7),b("click",function(){return o._buttonClicked(o.pageIndex-1,o._previousButtonsDisabled())}),te(),l(8,"svg",8),B(9,"path",9),d()(),ct(),l(10,"button",10),b("click",function(){return o._buttonClicked(o.pageIndex+1,o._nextButtonsDisabled())}),te(),l(11,"svg",8),B(12,"path",11),d()(),_(13,Gi,3,4,"button",12),d()()()),t&2&&(c(2),R(o.hidePageSize?-1:2),c(3),z(" ",o._intl.getRangeLabel(o.pageIndex,o.pageSize,o.length)," "),c(),R(o.showFirstLastButtons?6:-1),c(),p("matTooltip",o._intl.previousPageLabel)("matTooltipDisabled",o._previousButtonsDisabled())("disabled",o._previousButtonsDisabled()),L("aria-label",o._intl.previousPageLabel),c(3),p("matTooltip",o._intl.nextPageLabel)("matTooltipDisabled",o._nextButtonsDisabled())("disabled",o._nextButtonsDisabled()),L("aria-label",o._intl.nextPageLabel),c(3),R(o.showFirstLastButtons?13:-1))},dependencies:[Fe,oi,jt,xe,Ie],styles:[".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}"],encapsulation:2,changeDetection:0})}return i})(),Ri=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=ee({type:i});static \u0275inj=Z({providers:[Zi],imports:[Te,ni,Oe,ye]})}return i})();var to=["mat-sort-header",""],io=["*"];function oo(i,n){i&1&&(l(0,"div",2),te(),l(1,"svg",3),B(2,"path",4),d()())}var ki=new X("MAT_SORT_DEFAULT_OPTIONS"),we=(()=>{class i{_defaultOptions;_initializedStream=new Ce(1);sortables=new Map;_stateChanges=new U;active;start="asc";get direction(){return this._direction}set direction(e){this._direction=e}_direction="";disableClear;disabled=!1;sortChange=new P;initialized=this._initializedStream;constructor(e){this._defaultOptions=e}register(e){this.sortables.set(e.id,e)}deregister(e){this.sortables.delete(e.id)}sort(e){this.active!=e.id?(this.active=e.id,this.direction=e.start?e.start:this.start):this.direction=this.getNextSortDirection(e),this.sortChange.emit({active:this.active,direction:this.direction})}getNextSortDirection(e){if(!e)return"";let t=e?.disableClear??this.disableClear??!!this._defaultOptions?.disableClear,o=no(e.start||this.start,t),a=o.indexOf(this.direction)+1;return a>=o.length&&(a=0),o[a]}ngOnInit(){this._initializedStream.next()}ngOnChanges(){this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete(),this._initializedStream.complete()}static \u0275fac=function(t){return new(t||i)(gt(ki,8))};static \u0275dir=h({type:i,selectors:[["","matSort",""]],hostAttrs:[1,"mat-sort"],inputs:{active:[0,"matSortActive","active"],start:[0,"matSortStart","start"],direction:[0,"matSortDirection","direction"],disableClear:[2,"matSortDisableClear","disableClear",C],disabled:[2,"matSortDisabled","disabled",C]},outputs:{sortChange:"matSortChange"},exportAs:["matSort"],features:[A,J]})}return i})();function no(i,n){let e=["asc","desc"];return i=="desc"&&e.reverse(),n||e.push(""),e}var We=(()=>{class i{changes=new U;static \u0275fac=function(t){return new(t||i)};static \u0275prov=re({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();function ao(i){return i||new We}var ro={provide:We,deps:[[new Se,new Re,We]],useFactory:ao},xi=(()=>{class i{_intl=s(We);_sort=s(we,{optional:!0});_columnDef=s("MAT_SORT_HEADER_COLUMN_DEF",{optional:!0});_changeDetectorRef=s(ce);_focusMonitor=s(Lt);_elementRef=s(j);_ariaDescriber=s(Nt,{optional:!0});_renderChanges;_animationModule=s(ht,{optional:!0});_recentlyCleared=ft(null);_sortButton;id;arrowPosition="after";start;disabled=!1;get sortActionDescription(){return this._sortActionDescription}set sortActionDescription(e){this._updateSortActionDescription(e)}_sortActionDescription="Sort";disableClear;constructor(){s(At).load(Vt);let e=s(ki,{optional:!0});this._sort,e?.arrowPosition&&(this.arrowPosition=e?.arrowPosition)}ngOnInit(){!this.id&&this._columnDef&&(this.id=this._columnDef.name),this._sort.register(this),this._renderChanges=me(this._sort._stateChanges,this._sort.sortChange).subscribe(()=>this._changeDetectorRef.markForCheck()),this._sortButton=this._elementRef.nativeElement.querySelector(".mat-sort-header-container"),this._updateSortActionDescription(this._sortActionDescription)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(()=>this._recentlyCleared.set(null))}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._sort.deregister(this),this._renderChanges?.unsubscribe(),this._sortButton&&this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription)}_toggleOnInteraction(){if(!this._isDisabled()){let e=this._isSorted(),t=this._sort.direction;this._sort.sort(this),this._recentlyCleared.set(e&&!this._isSorted()?t:null)}}_handleKeydown(e){(e.keyCode===32||e.keyCode===13)&&(e.preventDefault(),this._toggleOnInteraction())}_isSorted(){return this._sort.active==this.id&&(this._sort.direction==="asc"||this._sort.direction==="desc")}_isDisabled(){return this._sort.disabled||this.disabled}_getAriaSortAttribute(){return this._isSorted()?this._sort.direction=="asc"?"ascending":"descending":"none"}_renderArrow(){return!this._isDisabled()||this._isSorted()}_updateSortActionDescription(e){this._sortButton&&(this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription),this._ariaDescriber?.describe(this._sortButton,e)),this._sortActionDescription=e}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=S({type:i,selectors:[["","mat-sort-header",""]],hostAttrs:[1,"mat-sort-header"],hostVars:3,hostBindings:function(t,o){t&1&&b("click",function(){return o._toggleOnInteraction()})("keydown",function(r){return o._handleKeydown(r)})("mouseleave",function(){return o._recentlyCleared.set(null)}),t&2&&(L("aria-sort",o._getAriaSortAttribute()),ie("mat-sort-header-disabled",o._isDisabled()))},inputs:{id:[0,"mat-sort-header","id"],arrowPosition:"arrowPosition",start:"start",disabled:[2,"disabled","disabled",C],sortActionDescription:"sortActionDescription",disableClear:[2,"disableClear","disableClear",C]},exportAs:["matSortHeader"],features:[A],attrs:to,ngContentSelectors:io,decls:4,vars:17,consts:[[1,"mat-sort-header-container","mat-focus-indicator"],[1,"mat-sort-header-content"],[1,"mat-sort-header-arrow"],["viewBox","0 -960 960 960","focusable","false","aria-hidden","true"],["d","M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z"]],template:function(t,o){t&1&&(le(),l(0,"div",0)(1,"div",1),Q(2),d(),_(3,oo,3,0,"div",2),d()),t&2&&(ie("mat-sort-header-sorted",o._isSorted())("mat-sort-header-position-before",o.arrowPosition==="before")("mat-sort-header-descending",o._sort.direction==="desc")("mat-sort-header-ascending",o._sort.direction==="asc")("mat-sort-header-recently-cleared-ascending",o._recentlyCleared()==="asc")("mat-sort-header-recently-cleared-descending",o._recentlyCleared()==="desc")("mat-sort-header-animations-disabled",o._animationModule==="NoopAnimations"),L("tabindex",o._isDisabled()?null:0)("role",o._isDisabled()?null:"button"),c(3),R(o._renderArrow()?3:-1))},styles:[".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}"],encapsulation:2,changeDetection:0})}return i})(),Ti=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=ee({type:i});static \u0275inj=Z({providers:[ro],imports:[pe]})}return i})();var lo=()=>[10,25,50,100],co=(i,n)=>[i,n];function uo(i,n){if(i&1){let e=H();l(0,"button",18),b("click",function(){I(e);let o=f();return O(o.clearSearch())}),l(1,"mat-icon"),g(2,"close"),d()()}}function mo(i,n){if(i&1){let e=H();l(0,"button",19),b("click",function(){I(e);let o=f();return O(o.add.emit())}),l(1,"mat-icon"),g(2,"add"),d(),g(3," Add "),d()}}function fo(i,n){if(i&1){let e=H();l(0,"button",20),b("click",function(){I(e);let o=f();return O(o.export.emit())}),l(1,"mat-icon"),g(2,"download"),d(),g(3," Export "),d()}}function ho(i,n){if(i&1&&(l(0,"th",23),g(1),d()),i&2){let e=f().$implicit;ke("width",e.width),p("mat-sort-header",e.sortable?e.property:""),c(),z(" ",e.name," ")}}function po(i,n){if(i&1&&(W(0),l(1,"a",26),g(2),d(),q()),i&2){let e=f().$implicit,t=f().$implicit;c(),p("routerLink",kt(2,co,t.linkPrefix||"",e.id)),c(),z(" ",t.format?t.format(e[t.property]):e[t.property]," ")}}function go(i,n){if(i&1&&(W(0),l(1,"span",27),g(2),d(),q()),i&2){let e=f(2).$implicit,t=f().$implicit;c(),p("ngClass","status-"+e[t.property]),c(),z(" ",t.format?t.format(e[t.property]):e[t.property]," ")}}function _o(i,n){if(i&1&&(W(0),g(1),q()),i&2){let e=f(2).$implicit,t=f().$implicit;c(),z(" ",t.format?t.format(e[t.property]):e[t.property]," ")}}function yo(i,n){if(i&1&&(W(0),_(1,go,3,2,"ng-container",25)(2,_o,2,1,"ng-container",25),q()),i&2){let e=f(2).$implicit;c(),p("ngIf",e.property==="status"),c(),p("ngIf",e.property!=="status")}}function wo(i,n){if(i&1&&(l(0,"td",24),_(1,po,3,5,"ng-container",25)(2,yo,3,2,"ng-container",25),d()),i&2){let e=n.$implicit,t=f().$implicit;c(),p("ngIf",t.isLink&&e[t.property]),c(),p("ngIf",!t.isLink)}}function Co(i,n){if(i&1&&(W(0,21),_(1,ho,2,4,"th",22)(2,wo,3,2,"td",13),q()),i&2){let e=n.$implicit;p("matColumnDef",e.property)}}function bo(i,n){i&1&&(l(0,"th",28),g(1,"Actions"),d())}function vo(i,n){if(i&1){let e=H();l(0,"button",32),b("click",function(){I(e);let o=f().$implicit,a=f();return O(a.edit.emit(o))}),l(1,"mat-icon"),g(2,"edit"),d()()}}function Do(i,n){if(i&1){let e=H();l(0,"button",33),b("click",function(){I(e);let o=f().$implicit,a=f();return O(a.delete.emit(o))}),l(1,"mat-icon"),g(2,"delete"),d()()}}function So(i,n){if(i&1){let e=H();l(0,"td",24)(1,"button",29),b("click",function(){let o=I(e).$implicit,a=f();return O(a.view.emit(o))}),l(2,"mat-icon"),g(3,"visibility"),d()(),_(4,vo,3,0,"button",30)(5,Do,3,0,"button",31),d()}if(i&2){let e=f();c(4),p("ngIf",e.showEditButton),c(),p("ngIf",e.showDeleteButton)}}function Ro(i,n){if(i&1&&(l(0,"tr",34)(1,"td",35),g(2),d()()),i&2){let e=f();c(),L("colspan",e.displayedColumns.length),c(),z(' No data matching the filter "',e.searchQuery,'" ')}}function ko(i,n){i&1&&B(0,"tr",36)}function xo(i,n){i&1&&B(0,"tr",37)}var Mi=class i{data=[];columns=[];showAddButton=!0;showEditButton=!0;showDeleteButton=!0;showExportButton=!0;add=new P;edit=new P;delete=new P;view=new P;export=new P;paginator;sort;table;displayedColumns=[];dataSource=new $e([]);searchQuery="";ngOnChanges(n){(n.columns||n.data)&&this.setupTable()}ngAfterViewInit(){this.paginator&&(this.dataSource.paginator=this.paginator),this.sort&&(this.dataSource.sort=this.sort)}setupTable(){this.displayedColumns=this.columns.filter(n=>n.visible!==!1).map(n=>n.property),this.displayedColumns.push("actions"),this.dataSource.data=this.data,this.dataSource.filterPredicate=(n,e)=>this.columns.some(t=>{if(!t.filter)return!1;let o=n[t.property];return o==null?!1:String(o).toLowerCase().includes(e.toLowerCase())}),this.paginator&&(this.dataSource.paginator=this.paginator),this.sort&&(this.dataSource.sort=this.sort)}applyFilter(){this.dataSource.filter=this.searchQuery.trim().toLowerCase(),this.dataSource.paginator&&this.dataSource.paginator.firstPage()}clearSearch(){this.searchQuery="",this.dataSource.filter=""}static \u0275fac=function(e){return new(e||i)};static \u0275cmp=S({type:i,selectors:[["app-data-table"]],viewQuery:function(e,t){if(e&1&&(fe(ye,5),fe(we,5),fe(Ue,5)),e&2){let o;T(o=M())&&(t.paginator=o.first),T(o=M())&&(t.sort=o.first),T(o=M())&&(t.table=o.first)}},inputs:{data:"data",columns:"columns",showAddButton:"showAddButton",showEditButton:"showEditButton",showDeleteButton:"showDeleteButton",showExportButton:"showExportButton"},outputs:{add:"add",edit:"edit",delete:"delete",view:"view",export:"export"},features:[J],decls:20,vars:12,consts:[[1,"table-container"],[1,"table-header"],["appearance","outline",1,"search-field"],["matInput","","placeholder","Search...",3,"ngModelChange","keyup","ngModel"],["matSuffix","","mat-icon-button","","aria-label","Clear",3,"click",4,"ngIf"],[1,"table-actions"],["mat-raised-button","","color","primary",3,"click",4,"ngIf"],["mat-raised-button","","color","accent",3,"click",4,"ngIf"],[1,"table-wrapper"],["mat-table","","matSort","",3,"dataSource"],[3,"matColumnDef",4,"ngFor","ngForOf"],["matColumnDef","actions"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["class","mat-row",4,"matNoDataRow"],["mat-header-row","",4,"matHeaderRowDef","matHeaderRowDefSticky"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","",3,"pageSizeOptions","pageSize"],["matSuffix","","mat-icon-button","","aria-label","Clear",3,"click"],["mat-raised-button","","color","primary",3,"click"],["mat-raised-button","","color","accent",3,"click"],[3,"matColumnDef"],["mat-header-cell","",3,"mat-sort-header","width",4,"matHeaderCellDef"],["mat-header-cell","",3,"mat-sort-header"],["mat-cell",""],[4,"ngIf"],[3,"routerLink"],[1,"status-chip",3,"ngClass"],["mat-header-cell",""],["mat-icon-button","","color","primary","matTooltip","View",3,"click"],["mat-icon-button","","color","accent","matTooltip","Edit",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Edit",3,"click"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click"],[1,"mat-row"],[1,"mat-cell","no-data-cell"],["mat-header-row",""],["mat-row",""]],template:function(e,t){e&1&&(l(0,"div",0)(1,"div",1)(2,"mat-form-field",2)(3,"mat-label"),g(4,"Search"),d(),l(5,"input",3),St("ngModelChange",function(a){return Dt(t.searchQuery,a)||(t.searchQuery=a),a}),b("keyup",function(){return t.applyFilter()}),d(),_(6,uo,3,0,"button",4),d(),l(7,"div",5),_(8,mo,4,0,"button",6)(9,fo,4,0,"button",7),d()(),l(10,"div",8)(11,"table",9),_(12,Co,3,1,"ng-container",10),W(13,11),_(14,bo,2,0,"th",12)(15,So,6,2,"td",13),q(),_(16,Ro,3,2,"tr",14)(17,ko,1,0,"tr",15)(18,xo,1,0,"tr",16),d()(),B(19,"mat-paginator",17),d()),e&2&&(c(5),vt("ngModel",t.searchQuery),c(),p("ngIf",t.searchQuery),c(2),p("ngIf",t.showAddButton),c(),p("ngIf",t.showExportButton),c(2),p("dataSource",t.dataSource),c(),p("ngForOf",t.columns),c(5),p("matHeaderRowDef",t.displayedColumns)("matHeaderRowDefSticky",!0),c(),p("matRowDefColumns",t.displayedColumns),c(),p("pageSizeOptions",Rt(11,lo))("pageSize",10))},dependencies:[It,Tt,Mt,Et,Ft,Ot,Si,Ue,pi,wi,gi,hi,Ci,_i,yi,bi,vi,Di,Ri,ye,Ti,we,xi,ri,ai,Fe,ei,ti,ii,Te,Qt,xe,Ut,$t,Oe,Ie,si,Jt,Kt,Zt,Xt],styles:[".table-container[_ngcontent-%COMP%]{width:100%;margin-bottom:20px}.table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px}.search-field[_ngcontent-%COMP%]{width:300px}.table-actions[_ngcontent-%COMP%]{display:flex;gap:10px}.table-wrapper[_ngcontent-%COMP%]{overflow-x:auto;max-height:500px;overflow-y:auto}table[_ngcontent-%COMP%]{width:100%}.no-data-cell[_ngcontent-%COMP%]{text-align:center;padding:20px;font-style:italic;color:#0000008a}.status-chip[_ngcontent-%COMP%]{padding:4px 8px;border-radius:16px;font-size:12px;font-weight:500;text-transform:capitalize}.status-active[_ngcontent-%COMP%]{background-color:#e6f4ea;color:#137333}.status-planned[_ngcontent-%COMP%]{background-color:#e8f0fe;color:#1a73e8}.status-maintenance[_ngcontent-%COMP%]{background-color:#fef7e0;color:#b06000}.status-inactive[_ngcontent-%COMP%]{background-color:#fce8e6;color:#c5221f}"]})};export{Mi as a};
