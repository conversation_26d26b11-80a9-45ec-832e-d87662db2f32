import{a as te,b as ie}from"./chunk-SSOC3NBY.js";import{a as ne,b as oe,c as ae,d as me,e as le}from"./chunk-5KM6POKN.js";import{a as X,b as Y,c as Z,g as $,h as ee}from"./chunk-ORNC4OUU.js";import{b as q,d as g,f as R,g as k,j as H,k as z,l as J,o as K,q as Q}from"./chunk-Q6MA6IAZ.js";import{b as re}from"./chunk-OJZDOK3T.js";import{a as j,b as G}from"./chunk-OMWDYSFJ.js";import"./chunk-3OB45MWO.js";import{a as V,c as D,d as A,e as B}from"./chunk-QB7XPJNY.js";import{k as F,p as W}from"./chunk-KGIFXD27.js";import{$ as T,_ as N,aa as O,ba as L,da as w,ea as U}from"./chunk-HE4KASLF.js";import{Bb as i,Cb as t,Db as d,Fc as S,Hb as P,Hc as y,Ic as I,Jb as h,Jc as E,Kb as s,Ub as r,Va as a,Vb as f,_a as c,cc as v,dc as C,ec as b,fb as M,ha as _,ia as x,lb as u,sb as l}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";function de(e,p){if(e&1&&(i(0,"mat-card-subtitle"),r(1),t()),e&2){let n=s();a(),f(n.user.username)}}function se(e,p){if(e&1&&d(0,"app-alert",5),e&2){let n=s();l("message",n.error)}}function ue(e,p){if(e&1&&d(0,"app-alert",6),e&2){let n=s();l("message",n.success)}}function fe(e,p){e&1&&(i(0,"div",7),d(1,"mat-spinner",8),t())}function ce(e,p){if(e&1&&(i(0,"div",12)(1,"div",13),r(2,"Provider Name"),t(),i(3,"div",14),r(4),t()()),e&2){let n=s(2);a(4),f(n.user.providerName)}}function ve(e,p){e&1&&(i(0,"mat-error"),r(1," Username is required "),t())}function ge(e,p){e&1&&(i(0,"mat-error"),r(1," Username must be at least 3 characters "),t())}function _e(e,p){e&1&&(i(0,"mat-error"),r(1," Provider name is required "),t())}function xe(e,p){if(e&1&&(i(0,"mat-form-field",18)(1,"mat-label"),r(2,"Provider Name"),t(),d(3,"input",28),u(4,_e,2,0,"mat-error",1),t()),e&2){let n,m=s(2);a(4),l("ngIf",(n=m.profileForm.get("providerName"))==null?null:n.hasError("required"))}}function he(e,p){e&1&&d(0,"mat-spinner",29)}function Ce(e,p){e&1&&(i(0,"span"),r(1,"Update Profile"),t())}function Me(e,p){if(e&1){let n=P();i(0,"div")(1,"mat-tab-group")(2,"mat-tab",9)(3,"div",10)(4,"div",11)(5,"div",12)(6,"div",13),r(7,"Username"),t(),i(8,"div",14),r(9),t()(),i(10,"div",12)(11,"div",13),r(12,"Email"),t(),i(13,"div",14),r(14),t()(),i(15,"div",12)(16,"div",13),r(17,"Role"),t(),i(18,"div",14),r(19),v(20,"titlecase"),t()(),u(21,ce,5,1,"div",15),i(22,"div",12)(23,"div",13),r(24,"Account Created"),t(),i(25,"div",14),r(26),v(27,"date"),t()(),i(28,"div",12)(29,"div",13),r(30,"Last Login"),t(),i(31,"div",14),r(32),v(33,"date"),t()()()()(),i(34,"mat-tab",16)(35,"div",10)(36,"form",17),h("ngSubmit",function(){_(n);let o=s();return x(o.onSubmit())}),i(37,"mat-form-field",18)(38,"mat-label"),r(39,"Username"),t(),d(40,"input",19),u(41,ve,2,0,"mat-error",1)(42,ge,2,0,"mat-error",1),t(),i(43,"mat-form-field",18)(44,"mat-label"),r(45,"Email"),t(),d(46,"input",20),i(47,"mat-hint"),r(48,"Email cannot be changed"),t()(),u(49,xe,5,1,"mat-form-field",21),i(50,"div",22)(51,"button",23),u(52,he,1,0,"mat-spinner",24)(53,Ce,2,0,"span",1),t()()()()()(),d(54,"mat-divider",25),i(55,"div",26)(56,"button",27),h("click",function(){_(n);let o=s();return x(o.onLogout())}),i(57,"mat-icon"),r(58,"exit_to_app"),t(),r(59," Logout "),t()()()}if(e&2){let n,m,o=s();a(9),f(o.user.username),a(5),f(o.user.email),a(5),f(C(20,13,o.user.role)),a(2),l("ngIf",o.user.providerName),a(5),f(C(27,15,o.user.createdAt)),a(6),f(b(33,17,o.user.lastLogin,"medium")),a(4),l("formGroup",o.profileForm),a(5),l("ngIf",(n=o.profileForm.get("username"))==null?null:n.hasError("required")),a(),l("ngIf",(m=o.profileForm.get("username"))==null?null:m.hasError("minlength")),a(7),l("ngIf",o.user.role==="provider"),a(2),l("disabled",o.profileForm.invalid||o.updateLoading),a(),l("ngIf",o.updateLoading),a(),l("ngIf",!o.updateLoading)}}var pe=class e{constructor(p,n,m){this.fb=p;this.authService=n;this.router=m;this.profileForm=this.fb.group({username:["",[g.required,g.minLength(3)]],email:[{value:"",disabled:!0}],providerName:[""]})}user=null;profileForm;loading=!0;updateLoading=!1;error="";success="";ngOnInit(){this.loadUserProfile()}loadUserProfile(){this.loading=!0,this.user=this.authService.getCurrentUser(),this.user?(this.profileForm.patchValue({username:this.user.username,email:this.user.email,providerName:this.user.providerName||""}),this.user.role==="provider"&&(this.profileForm.get("providerName")?.setValidators([g.required]),this.profileForm.get("providerName")?.updateValueAndValidity()),this.loading=!1):this.router.navigate(["/auth/login"])}onSubmit(){if(this.profileForm.invalid)return;this.updateLoading=!0,this.error="",this.success="";let{username:p,providerName:n}=this.profileForm.value,m={username:p};this.user?.role==="provider"&&(m.providerName=n),this.authService.updateProfile(m).subscribe({next:o=>{this.updateLoading=!1,this.success="Profile updated successfully!",this.user=o},error:o=>{this.updateLoading=!1,this.error=o.message||"Failed to update profile. Please try again."}})}onLogout(){this.authService.logout()}static \u0275fac=function(n){return new(n||e)(c(K),c(W),c(F))};static \u0275cmp=M({type:e,selectors:[["app-profile"]],decls:11,vars:5,consts:[[1,"profile-container"],[4,"ngIf"],["type","error",3,"message",4,"ngIf"],["type","success",3,"message",4,"ngIf"],["class","loading-container",4,"ngIf"],["type","error",3,"message"],["type","success",3,"message"],[1,"loading-container"],["diameter","40"],["label","Profile Information"],[1,"tab-content"],[1,"info-grid"],[1,"info-item"],[1,"info-label"],[1,"info-value"],["class","info-item",4,"ngIf"],["label","Edit Profile"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","username","placeholder","Enter your username"],["matInput","","formControlName","email","type","email","placeholder","Enter your email","readonly",""],["appearance","outline","class","full-width",4,"ngIf"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],[1,"divider"],[1,"logout-section"],["mat-raised-button","","color","warn",3,"click"],["matInput","","formControlName","providerName","placeholder","Enter your provider name"],["diameter","20"]],template:function(n,m){n&1&&(i(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),r(4,"User Profile"),t(),u(5,de,2,1,"mat-card-subtitle",1),t(),i(6,"mat-card-content"),u(7,se,1,1,"app-alert",2)(8,ue,1,1,"app-alert",3)(9,fe,2,0,"div",4)(10,Me,60,20,"div",1),t()()()),n&2&&(a(5),l("ngIf",m.user),a(2),l("ngIf",m.error),a(),l("ngIf",m.success),a(),l("ngIf",m.loading),a(),l("ngIf",m.user&&!m.loading))},dependencies:[E,S,y,I,Q,H,q,R,k,z,J,U,N,O,w,L,T,ee,$,X,Z,Y,ie,te,D,V,B,A,G,j,le,me,ae,ne,oe,re],styles:[".profile-container[_ngcontent-%COMP%]{padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:40px}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-bottom:15px}.info-label[_ngcontent-%COMP%]{font-weight:500;color:#0000008a;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{font-size:16px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:15px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:20px}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px;min-height:36px;display:flex;justify-content:center;align-items:center}.divider[_ngcontent-%COMP%]{margin:20px 0}.logout-section[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:20px}"]})};export{pe as ProfileComponent};
