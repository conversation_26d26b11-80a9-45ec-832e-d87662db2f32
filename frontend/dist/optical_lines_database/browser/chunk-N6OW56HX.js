import{k as u,p as s}from"./chunk-KGIFXD27.js";import{D as c,V as n,_ as i,o}from"./chunk-BS5MTC5G.js";var l=class t{constructor(r,e){this.authService=r;this.router=e}canActivate(){return this.checkAuth()}canActivateChild(){return this.checkAuth()}canLoad(){return this.checkAuth()}checkAuth(){return this.authService.isAuthenticated$.pipe(c(1),o(r=>r?!0:this.router.createUrlTree(["/auth/login"])))}static \u0275fac=function(e){return new(e||t)(i(s),i(u))};static \u0275prov=n({token:t,factory:t.\u0275fac,providedIn:"root"})};var h=class t{constructor(r,e){this.authService=r;this.router=e}canActivate(r){let e=r.data.role;return e?this.authService.currentUser$.pipe(c(1),o(a=>a&&a.role===e?!0:a?this.router.createUrlTree(["/"]):this.router.createUrlTree(["/auth/login"]))):this.authService.isAuthenticated$.pipe(c(1),o(a=>a?!0:this.router.createUrlTree(["/auth/login"])))}static \u0275fac=function(e){return new(e||t)(i(s),i(u))};static \u0275prov=n({token:t,factory:t.\u0275fac,providedIn:"root"})};export{l as a,h as b};
