import{a,b as s,o as p}from"./chunk-KGIFXD27.js";import{V as r,_ as n}from"./chunk-BS5MTC5G.js";var o=class i{constructor(t){this.http=t}apiUrl=p.apiUrl;get(t,e=new a){return this.http.get(`${this.apiUrl}/${t}`,{params:e})}post(t,e){return this.http.post(`${this.apiUrl}/${t}`,e)}put(t,e){return this.http.put(`${this.apiUrl}/${t}`,e)}delete(t){return this.http.delete(`${this.apiUrl}/${t}`)}static \u0275fac=function(e){return new(e||i)(n(s))};static \u0275prov=r({token:i,factory:i.\u0275fac,providedIn:"root"})};var m=class i{constructor(t){this.apiService=t}basePath="lines";getLines(t){let e=new a;return t&&(e=e.set("providerId",t)),this.apiService.get(this.basePath,e)}getLine(t){return this.apiService.get(`${this.basePath}/${t}`)}createLine(t){return this.apiService.post(this.basePath,t)}updateLine(t,e){return this.apiService.put(`${this.basePath}/${t}`,e)}deleteLine(t){return this.apiService.delete(`${this.basePath}/${t}`)}static \u0275fac=function(e){return new(e||i)(n(o))};static \u0275prov=r({token:i,factory:i.\u0275fac,providedIn:"root"})};var b=class i{constructor(t){this.apiService=t}basePath="points";getPoints(t){let e=new a;return t&&(e=e.set("providerId",t)),this.apiService.get(this.basePath,e)}getPoint(t){return this.apiService.get(`${this.basePath}/${t}`)}createPoint(t){return this.apiService.post(this.basePath,t)}updatePoint(t,e){return this.apiService.put(`${this.basePath}/${t}`,e)}deletePoint(t){return this.apiService.delete(`${this.basePath}/${t}`)}static \u0275fac=function(e){return new(e||i)(n(o))};static \u0275prov=r({token:i,factory:i.\u0275fac,providedIn:"root"})};export{m as a,b};
