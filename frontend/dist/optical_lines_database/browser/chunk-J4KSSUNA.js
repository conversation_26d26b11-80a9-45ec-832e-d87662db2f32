import{a as h}from"./chunk-4LXAWE6T.js";import"./chunk-4N6OITQX.js";import"./chunk-SSOC3NBY.js";import"./chunk-QG3LXPLB.js";import"./chunk-7BGM6SBA.js";import"./chunk-ORNC4OUU.js";import"./chunk-Q6MA6IAZ.js";import{a as N,b as T,c as V,d as E}from"./chunk-UPFDAAUE.js";import{b as S}from"./chunk-OJZDOK3T.js";import{b as A}from"./chunk-ESTBHZNU.js";import"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import"./chunk-QB7XPJNY.js";import{n as w}from"./chunk-KGIFXD27.js";import{$ as b,_ as M,aa as x,da as I,ea as L}from"./chunk-HE4KASLF.js";import{Bb as l,Cb as c,Db as v,Fc as D,Hb as C,Jb as _,Jc as g,Kb as r,Ub as P,Va as m,_a as u,fb as f,ha as d,ia as p,lb as y,sb as a}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";function B(i,e){if(i&1&&v(0,"app-alert",4),i&2){let t=r();a("message",t.error)}}function O(i,e){i&1&&(l(0,"div",5),v(1,"app-loading-spinner"),c())}function k(i,e){if(i&1){let t=C();l(0,"app-data-table",6),_("edit",function(o){d(t);let s=r();return p(s.onEdit(o))})("delete",function(o){d(t);let s=r();return p(s.onDelete(o))})("view",function(o){d(t);let s=r();return p(s.onView(o))})("export",function(){d(t);let o=r();return p(o.onExport())}),c()}if(i&2){let t=r();a("data",t.points)("columns",t.columns)("showAddButton",!1)}}var j=class i{constructor(e,t){this.pointService=e;this.dialog=t}points=[];loading=!0;error="";columns=[{name:"Name",property:"name",isModelProperty:!0,visible:!0,isLink:!0,linkPrefix:"/points",sortable:!0,filter:!0},{name:"Provider",property:"providerName",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Type",property:"type",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Capacity",property:"capacity",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Address",property:"address",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Status",property:"status",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Installation Date",property:"installationDate",isModelProperty:!0,visible:!0,sortable:!0,filter:!0,format:e=>e?new Date(e).toLocaleDateString():""}];ngOnInit(){this.loadPoints()}loadPoints(){this.loading=!0,this.error="",setTimeout(()=>{this.points=this.getMockPoints(),this.loading=!1},1e3)}onAdd(){console.log("Add new connection point")}onEdit(e){console.log("Edit connection point:",e)}onDelete(e){this.dialog.open(E,{width:"350px",data:{title:"Confirm Delete",message:`Are you sure you want to delete the connection point "${e.name}"?`,confirmText:"Delete",cancelText:"Cancel"}}).afterClosed().subscribe(n=>{n&&console.log("Delete connection point:",e)})}onView(e){console.log("View connection point:",e)}onExport(){console.log("Export connection points")}getMockPoints(){return[{id:"point1",name:"Point 1",providerId:"provider1",providerName:"Provider A",type:"junction",capacity:300,address:"Address 1, City 1",status:"active",installationDate:new Date("2022-01-10"),lastModified:new Date("2023-05-15"),location:null,connectedLines:["1","2"],properties:{}},{id:"point2",name:"Point 2",providerId:"provider1",providerName:"Provider A",type:"endpoint",capacity:100,address:"Address 2, City 1",status:"active",installationDate:new Date("2022-01-12"),lastModified:new Date("2023-05-18"),location:null,connectedLines:["1","3"],properties:{}},{id:"point3",name:"Point 3",providerId:"provider2",providerName:"Provider B",type:"distribution",capacity:200,address:"Address 3, City 2",status:"active",installationDate:new Date("2021-11-05"),lastModified:new Date("2023-06-10"),location:null,connectedLines:["2"],properties:{}},{id:"point4",name:"Point 4",providerId:"provider2",providerName:"Provider B",type:"junction",capacity:250,address:"Address 4, City 2",status:"maintenance",installationDate:new Date("2021-11-08"),lastModified:new Date("2023-06-12"),location:null,connectedLines:["2"],properties:{}},{id:"point5",name:"Point 5",providerId:"provider1",providerName:"Provider A",type:"endpoint",capacity:150,address:"Address 5, City 3",status:"planned",installationDate:new Date("2023-11-20"),lastModified:new Date("2023-07-01"),location:null,connectedLines:["3"],properties:{}},{id:"point6",name:"Point 6",providerId:"provider1",providerName:"Provider A",type:"distribution",capacity:180,address:"Address 6, City 3",status:"planned",installationDate:new Date("2023-11-25"),lastModified:new Date("2023-07-03"),location:null,connectedLines:["3"],properties:{}},{id:"point7",name:"Point 7",providerId:"provider3",providerName:"Provider C",type:"junction",capacity:280,address:"Address 7, City 4",status:"active",installationDate:new Date("2022-03-15"),lastModified:new Date("2023-08-05"),location:null,connectedLines:["4"],properties:{}},{id:"point8",name:"Point 8",providerId:"provider3",providerName:"Provider C",type:"endpoint",capacity:320,address:"Address 8, City 4",status:"maintenance",installationDate:new Date("2022-03-18"),lastModified:new Date("2023-08-08"),location:null,connectedLines:["4"],properties:{}},{id:"point9",name:"Point 9",providerId:"provider2",providerName:"Provider B",type:"distribution",capacity:220,address:"Address 9, City 5",status:"inactive",installationDate:new Date("2022-05-01"),lastModified:new Date("2023-09-10"),location:null,connectedLines:["5"],properties:{}},{id:"point10",name:"Point 10",providerId:"provider2",providerName:"Provider B",type:"junction",capacity:270,address:"Address 10, City 5",status:"inactive",installationDate:new Date("2022-05-03"),lastModified:new Date("2023-09-12"),location:null,connectedLines:["5"],properties:{}},{id:"point11",name:"Point 11",providerId:"provider3",providerName:"Provider C",type:"endpoint",capacity:160,address:"Address 11, City 6",status:"active",installationDate:new Date("2022-06-05"),lastModified:new Date("2023-10-01"),location:null,connectedLines:["6"],properties:{}},{id:"point12",name:"Point 12",providerId:"provider3",providerName:"Provider C",type:"distribution",capacity:190,address:"Address 12, City 6",status:"active",installationDate:new Date("2022-06-08"),lastModified:new Date("2023-10-03"),location:null,connectedLines:["6"],properties:{}}]}static \u0275fac=function(t){return new(t||i)(u(A),u(N))};static \u0275cmp=f({type:i,selectors:[["app-points-list"]],decls:9,vars:3,consts:[[1,"points-container"],["type","error",3,"message",4,"ngIf"],["class","loading-container",4,"ngIf"],[3,"data","columns","showAddButton","edit","delete","view","export",4,"ngIf"],["type","error",3,"message"],[1,"loading-container"],[3,"edit","delete","view","export","data","columns","showAddButton"]],template:function(t,n){t&1&&(l(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),P(4,"Connection Points"),c()(),l(5,"mat-card-content"),y(6,B,1,1,"app-alert",1)(7,O,2,0,"div",2)(8,k,1,3,"app-data-table",3),c()()()),t&2&&(m(6),a("ngIf",n.error),m(),a("ngIf",n.loading),m(),a("ngIf",!n.loading))},dependencies:[g,D,L,M,x,I,b,w,T,h,V,S],styles:[".points-container[_ngcontent-%COMP%]{padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px}"]})};export{j as PointsListComponent};
