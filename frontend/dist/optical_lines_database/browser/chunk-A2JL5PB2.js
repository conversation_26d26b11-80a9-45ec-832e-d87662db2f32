import{$ as o,_ as m,aa as p,da as r,ea as d}from"./chunk-HE4KASLF.js";import{Bb as t,Cb as a,Ub as n,fb as c}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";var l=class i{static \u0275fac=function(e){return new(e||i)};static \u0275cmp=c({type:i,selectors:[["app-capacity-management"]],decls:9,vars:0,consts:[[1,"capacity-management-container"],[1,"capacity-management-placeholder"]],template:function(e,g){e&1&&(t(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),n(4,"Line Capacity Management"),a()(),t(5,"mat-card-content")(6,"div",1)(7,"p"),n(8,"Line capacity management will be implemented in Phase 5"),a()()()()())},dependencies:[d,m,p,r,o],styles:[".capacity-management-container[_ngcontent-%COMP%]{padding:20px}.capacity-management-placeholder[_ngcontent-%COMP%]{height:300px;display:flex;justify-content:center;align-items:center;background-color:#f5f5f5;border:1px solid #ddd}"]})};export{l as CapacityManagementComponent};
