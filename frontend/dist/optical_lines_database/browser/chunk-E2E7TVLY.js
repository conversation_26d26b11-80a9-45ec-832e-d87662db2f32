import{a as V}from"./chunk-4LXAWE6T.js";import"./chunk-4N6OITQX.js";import"./chunk-SSOC3NBY.js";import"./chunk-QG3LXPLB.js";import"./chunk-7BGM6SBA.js";import"./chunk-ORNC4OUU.js";import"./chunk-Q6MA6IAZ.js";import{a as O,b as T,c as N,d as E}from"./chunk-UPFDAAUE.js";import{b as S}from"./chunk-OJZDOK3T.js";import{a as h}from"./chunk-ESTBHZNU.js";import"./chunk-OMWDYSFJ.js";import"./chunk-FMRXKCS7.js";import"./chunk-3OB45MWO.js";import"./chunk-QB7XPJNY.js";import{n as w}from"./chunk-KGIFXD27.js";import{$ as b,_ as x,aa as I,da as M,ea as P}from"./chunk-HE4KASLF.js";import{Bb as d,Cb as m,Db as f,Fc as L,Hb as g,Jb as y,Jc as D,Kb as a,Ub as C,Va as c,_a as u,fb as v,ha as p,ia as s,lb as _,sb as r}from"./chunk-BS5MTC5G.js";import"./chunk-C6Q5SG76.js";function k(i,e){if(i&1&&f(0,"app-alert",4),i&2){let t=a();r("message",t.error)}}function B(i,e){i&1&&(d(0,"div",5),f(1,"app-loading-spinner"),m())}function j(i,e){if(i&1){let t=g();d(0,"app-data-table",6),y("edit",function(o){p(t);let l=a();return s(l.onEdit(o))})("delete",function(o){p(t);let l=a();return s(l.onDelete(o))})("view",function(o){p(t);let l=a();return s(l.onView(o))})("export",function(){p(t);let o=a();return s(o.onExport())}),m()}if(i&2){let t=a();r("data",t.lines)("columns",t.columns)("showAddButton",!1)}}var A=class i{constructor(e,t){this.lineService=e;this.dialog=t}lines=[];loading=!0;error="";columns=[{name:"Name",property:"name",isModelProperty:!0,visible:!0,isLink:!0,linkPrefix:"/lines",sortable:!0,filter:!0},{name:"Provider",property:"providerName",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Capacity",property:"capacity",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Used Capacity",property:"usedCapacity",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Length (km)",property:"length",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Status",property:"status",isModelProperty:!0,visible:!0,sortable:!0,filter:!0},{name:"Installation Date",property:"installationDate",isModelProperty:!0,visible:!0,sortable:!0,filter:!0,format:e=>e?new Date(e).toLocaleDateString():""}];ngOnInit(){this.loadLines()}loadLines(){this.loading=!0,this.error="",setTimeout(()=>{this.lines=this.getMockLines(),this.loading=!1},1e3)}onAdd(){console.log("Add new line")}onEdit(e){console.log("Edit line:",e)}onDelete(e){this.dialog.open(E,{width:"350px",data:{title:"Confirm Delete",message:`Are you sure you want to delete the line "${e.name}"?`,confirmText:"Delete",cancelText:"Cancel"}}).afterClosed().subscribe(n=>{n&&console.log("Delete line:",e)})}onView(e){console.log("View line:",e)}onExport(){console.log("Export lines")}getMockLines(){return[{id:"1",name:"Line 1",providerId:"provider1",providerName:"Provider A",startPointId:"point1",endPointId:"point2",capacity:100,usedCapacity:75,length:25.5,status:"active",installationDate:new Date("2022-01-15"),lastModified:new Date("2023-05-20"),geometry:null,properties:{}},{id:"2",name:"Line 2",providerId:"provider2",providerName:"Provider B",startPointId:"point3",endPointId:"point4",capacity:200,usedCapacity:120,length:15.2,status:"active",installationDate:new Date("2021-11-10"),lastModified:new Date("2023-06-15"),geometry:null,properties:{}},{id:"3",name:"Line 3",providerId:"provider1",providerName:"Provider A",startPointId:"point5",endPointId:"point6",capacity:150,usedCapacity:30,length:18.7,status:"planned",installationDate:new Date("2023-12-01"),lastModified:new Date("2023-07-05"),geometry:null,properties:{}},{id:"4",name:"Line 4",providerId:"provider3",providerName:"Provider C",startPointId:"point7",endPointId:"point8",capacity:300,usedCapacity:150,length:32.1,status:"maintenance",installationDate:new Date("2022-03-20"),lastModified:new Date("2023-08-10"),geometry:null,properties:{}},{id:"5",name:"Line 5",providerId:"provider2",providerName:"Provider B",startPointId:"point9",endPointId:"point10",capacity:250,usedCapacity:0,length:22.8,status:"inactive",installationDate:new Date("2022-05-05"),lastModified:new Date("2023-09-15"),geometry:null,properties:{}},{id:"6",name:"Line 6",providerId:"provider3",providerName:"Provider C",startPointId:"point11",endPointId:"point12",capacity:180,usedCapacity:120,length:15.3,status:"active",installationDate:new Date("2022-06-10"),lastModified:new Date("2023-10-05"),geometry:null,properties:{}}]}static \u0275fac=function(t){return new(t||i)(u(h),u(O))};static \u0275cmp=v({type:i,selectors:[["app-lines-list"]],decls:9,vars:3,consts:[[1,"lines-container"],["type","error",3,"message",4,"ngIf"],["class","loading-container",4,"ngIf"],[3,"data","columns","showAddButton","edit","delete","view","export",4,"ngIf"],["type","error",3,"message"],[1,"loading-container"],[3,"edit","delete","view","export","data","columns","showAddButton"]],template:function(t,n){t&1&&(d(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),C(4,"Optical Lines"),m()(),d(5,"mat-card-content"),_(6,k,1,1,"app-alert",1)(7,B,2,0,"div",2)(8,j,1,3,"app-data-table",3),m()()()),t&2&&(c(6),r("ngIf",n.error),c(),r("ngIf",n.loading),c(),r("ngIf",!n.loading))},dependencies:[D,L,P,x,I,M,b,w,T,V,N,S],styles:[".lines-container[_ngcontent-%COMP%]{padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px}"]})};export{A as LinesListComponent};
