/**
 * Represents a connection point in the optical network
 */
export interface ConnectionPoint {
  id: string;
  name: string;
  providerId: string;
  providerName: string;
  type: 'junction' | 'endpoint' | 'distribution';
  capacity: number;
  address: string;
  status: 'active' | 'planned' | 'maintenance' | 'inactive';
  installationDate: Date;
  lastModified: Date;
  location: any; // Will be GeoJSON.Point when we add the GeoJSON types
  connectedLines: string[];
  properties: Record<string, any>;
}
