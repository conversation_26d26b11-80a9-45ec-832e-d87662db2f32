import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { OpticalLine } from '../models/optical-line.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class OpticalLineService {
  private readonly basePath = 'lines';

  constructor(private apiService: ApiService) { }

  /**
   * Get all optical lines
   * @param providerId Optional provider ID to filter by
   * @returns Observable of optical lines array
   */
  getLines(providerId?: string): Observable<OpticalLine[]> {
    let params = new HttpParams();
    if (providerId) {
      params = params.set('providerId', providerId);
    }
    return this.apiService.get<OpticalLine[]>(this.basePath, params);
  }

  /**
   * Get a specific optical line by ID
   * @param id Optical line ID
   * @returns Observable of the optical line
   */
  getLine(id: string): Observable<OpticalLine> {
    return this.apiService.get<OpticalLine>(`${this.basePath}/${id}`);
  }

  /**
   * Create a new optical line
   * @param line Optical line data
   * @returns Observable of the created optical line
   */
  createLine(line: Omit<OpticalLine, 'id'>): Observable<OpticalLine> {
    return this.apiService.post<OpticalLine>(this.basePath, line);
  }

  /**
   * Update an existing optical line
   * @param id Optical line ID
   * @param line Updated optical line data
   * @returns Observable of the updated optical line
   */
  updateLine(id: string, line: Partial<OpticalLine>): Observable<OpticalLine> {
    return this.apiService.put<OpticalLine>(`${this.basePath}/${id}`, line);
  }

  /**
   * Delete an optical line
   * @param id Optical line ID
   * @returns Observable of the operation result
   */
  deleteLine(id: string): Observable<void> {
    return this.apiService.delete<void>(`${this.basePath}/${id}`);
  }
}
