import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { ConnectionPoint } from '../models/connection-point.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ConnectionPointService {
  private readonly basePath = 'points';

  constructor(private apiService: ApiService) { }

  /**
   * Get all connection points
   * @param providerId Optional provider ID to filter by
   * @returns Observable of connection points array
   */
  getPoints(providerId?: string): Observable<ConnectionPoint[]> {
    let params = new HttpParams();
    if (providerId) {
      params = params.set('providerId', providerId);
    }
    return this.apiService.get<ConnectionPoint[]>(this.basePath, params);
  }

  /**
   * Get a specific connection point by ID
   * @param id Connection point ID
   * @returns Observable of the connection point
   */
  getPoint(id: string): Observable<ConnectionPoint> {
    return this.apiService.get<ConnectionPoint>(`${this.basePath}/${id}`);
  }

  /**
   * Create a new connection point
   * @param point Connection point data
   * @returns Observable of the created connection point
   */
  createPoint(point: Omit<ConnectionPoint, 'id'>): Observable<ConnectionPoint> {
    return this.apiService.post<ConnectionPoint>(this.basePath, point);
  }

  /**
   * Update an existing connection point
   * @param id Connection point ID
   * @param point Updated connection point data
   * @returns Observable of the updated connection point
   */
  updatePoint(id: string, point: Partial<ConnectionPoint>): Observable<ConnectionPoint> {
    return this.apiService.put<ConnectionPoint>(`${this.basePath}/${id}`, point);
  }

  /**
   * Delete a connection point
   * @param id Connection point ID
   * @returns Observable of the operation result
   */
  deletePoint(id: string): Observable<void> {
    return this.apiService.delete<void>(`${this.basePath}/${id}`);
  }
}
